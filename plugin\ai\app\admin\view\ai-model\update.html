<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="UTF-8">
        <title>更新页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/component/jsoneditor/css/jsoneditor.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
        
    </head>
    <body>

        <form class="layui-form">

            <div class="mainBox">
                <div class="main-container mr-5">

                    <div class="layui-form-item">
                        <label class="layui-form-label">名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" value="" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">支持的模型</label>
                        <div class="layui-input-block">
                            <textarea name="models" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">优先级</label>
                        <div class="layui-input-block">
                            <input type="number" name="priority" value="0" class="layui-input">
                        </div>
                    </div>

                    <div id="defaultSettings">

                    </div>
                    
                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit="" lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
        </form>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/component/jsoneditor/jsoneditor.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        
        <script>

            // 相关接口
            const PRIMARY_KEY = "id";
            const SELECT_API = "/app/ai/admin/ai-model/select" + location.search;
            const UPDATE_API = "/app/ai/admin/ai-model/update";

            // 获取数据库记录
            layui.use(["form", "util", "popup"], function () {
                let $ = layui.$;
                $.ajax({
                    url: SELECT_API,
                    dataType: "json",
                    success: function (res) {
                        // 给表单初始化数据
                        layui.each(res.data[0], function (key, item) {
                            let obj = $('*[name="'+key+'"]');
                            if (key === "password") {
                                obj.attr("placeholder", "不更新密码请留空");
                                return;
                            }
                            if (key === "settings") {
                                let html = "";
                                const element = function (key, type, value, desc) {
                                    if (type === "checkbox") {
                                        let checked = value ? "checked" : "";
                                        desc = desc ? `<div class="layui-form-mid layui-word-aux">${desc}</div>` : ''
                                        return [`<input type="checkbox" name="${key}" lay-skin="switch" ${checked}>`, desc];
                                    }
                                    return [`<input type="${type}" name="${key}" value="${value}" lay-verify="${type}" autocomplete="off" placeholder="${desc}" class="layui-input">`, ''];
                                }
                                for (let key in item) {
                                    let {name, type, value, desc} = item[key];
                                    desc = desc || "";
                                    let [ele, des] = element(key, type, value, desc);
                                    html += `<div class="layui-form-item">
                                        <label class="layui-form-label">${name}</label>
                                        <div class="layui-input-block">
                                            ${ele}
                                        </div>
                                        ${des}
                                       </div>`;
                                }
                                $("#defaultSettings").html(html);
                                layui.form.render();
                            }
                            if (typeof obj[0] === "undefined" || !obj[0].nodeName) return;
                            if (key === "models") {
                                obj.val(item.join("\n"));
                            } else if (obj[0].nodeName.toLowerCase() === "textarea") {
                                obj.val(item);
                            } else {
                                obj.attr("value", item);
                                obj[0].value = item;
                            }

                        });

                        // ajax返回失败
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }

                    }
                });
            });

            //提交事件
            layui.use(["form", "popup"], function () {
                // 字段验证允许为空
                layui.form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });
                layui.form.on("submit(save)", function (data) {
                    data.field[PRIMARY_KEY] = layui.url().search[PRIMARY_KEY];
                    layui.$.ajax({
                        url: UPDATE_API,
                        type: "POST",
                        dateType: "json",
                        data: data.field,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>

</html>
