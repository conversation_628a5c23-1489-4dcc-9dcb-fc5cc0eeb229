<?php

namespace plugin\ai\app\admin\controller;

use plugin\ai\app\service\Azure;
use support\Request;
use support\Response;

class AzureController extends SettingBase
{
    /**
     * 服务名
     * @var string
     */
    protected $service = Azure::class;

    /**
     * 更新配置
     * @param Request $request
     * @return Response
     */
    public function update(Request $request): Response
    {
        $data = $request->post();
        if (in_array(get_class($this), [CategoryController::class, PlanController::class, SensitiveWordController::class])) {
            $data = current($request->post());
        }
        $data['api_host'] = rtrim($data['api_host'], '/ ');
        if ($map = $data['map']) {
            if (!json_decode($map, true)) {
                return $this->json(1, '模型映射必须是有效的json格式');
            }
        }
        call_user_func([$this->service, 'saveSetting'], $data);
        return $this->json(0);
    }
}