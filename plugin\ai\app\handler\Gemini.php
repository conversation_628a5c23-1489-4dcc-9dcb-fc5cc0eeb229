<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

class Gemini extends Base
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = 'Gemini';

    /**
     * @var string 模型类型
     */
    protected static $type = 'gemini';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'gemini-pro',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'api' => [
            'name' => 'API',
            'type' => 'text',
            'value' => 'https://gemini.ailard.com',
            'desc' => 'API 地址',
        ],
        'apikey' => [
            'name' => 'ApiKey',
            'type' => 'text',
            'value' => '',
            'desc' => 'API Key',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 0,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Gemini::class;

    /**
     * 对话
     * @param $data
     * @param $options
     * @return void
     */
    public function completions($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->completions($data, $options);
    }
}