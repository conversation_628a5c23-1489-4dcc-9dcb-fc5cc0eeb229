<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

use Webman\Openai\Audio;

class Tts extends Gpt3
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = '朗读';

    /**
     * @var string 模型类型
     */
    protected static $type = 'tts';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'tts-1',
        'tts-1-hd',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'api' => [
            'name' => 'API',
            'type' => 'text',
            'value' => 'https://api.ailard.com',
            'desc' => 'API 地址',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 100,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = Audio::class;

    /**
     * Speech
     * @param array $data
     * @param array $options
     * @return void
     */
    public function speech(array $data, array $options)
    {
        $settings = $this->getRealSettings($data['model']);
        $this->driver = new $this->driverClass($settings);
        $this->driver->speech($data, $options);
    }

}