<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\controller;

use plugin\ai\api\Chat;
use plugin\ai\app\event\data\ChatStreamData;
use plugin\ai\app\event\data\ModelRequestData;
use plugin\ai\app\event\data\ModelResponseData;
use plugin\ai\app\service\Common;
use plugin\ai\app\service\Setting;
use plugin\ai\app\service\User;
use support\exception\BusinessException;
use support\Request;
use support\Response;
use Webman\Event\Event;
use support\Log;
use Workerman\Protocols\Http\Chunk;

class ChatController extends Base
{

    /**
     * 不需要登录的方法
     *
     * @var string[]
     */
    protected $noNeedLogin = ['completions', 'translate', 'summarize'];

    /**
     * 对话
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function completions(Request $request): Response
    {
        $model = $request->post('model'); // 示例 gpt-4o
        if ($error = static::tryReduceBalance($model)) {
            return json(['error' => ['message' => $error]]);
        }
        $stream = (bool)$request->post('stream'); #默认值
        $messages = $request->post('messages'); #会话消息
        $content = last($messages)['content'] ?? '';
        $temperature = (float)$request->post('temperature', 0.5); #参数调整-创造性
        $userMessageId = $request->post('user_message_id');
        $assistantMessageId = $request->post('assistant_message_id');
        $roleId = $request->post('role_id');
        $chatId = $request->post('chat_id');
        $userId = session('user.id') ?? session('user.uid');
        $sessionId = $request->sessionId();
        $realIp = $request->getRealIp();
        $this->saveMessage($userId, $sessionId, $userMessageId, $roleId, 'user', $content, $realIp, $model, $chatId);

        $connection = $request->connection;

        $modelRequestData = new ModelRequestData();
        $modelRequestData->data = [
            'model' => $model,
            'stream' => $stream,
            'messages' => $messages,
            'temperature' => $temperature,
        ];
        $modelRequestData->options = [
            'stream' => function ($data) use ($connection, $modelRequestData) {
                $chatStreamData = new ChatStreamData($modelRequestData, $data);
                Event::dispatch('ai.chat.completions.stream', $chatStreamData);
                $connection->send(new Chunk( json_encode($chatStreamData->streamData, JSON_UNESCAPED_UNICODE) . "\n"));
            },
            'complete' => function ($result, $response) use ($connection, $userId, $sessionId, $assistantMessageId, $roleId, $realIp, $model, $chatId, $modelRequestData) {
                $responseData = new ModelResponseData();
                $responseData->data = $result;
                $responseData->modelRequestData = $modelRequestData;
                Event::dispatch('ai.chat.completions.response', $responseData);
                $result = $responseData->data;
                if (isset($result['error'])) {
                    $result = json_encode($result, JSON_UNESCAPED_UNICODE);
                    Log::error($result);
                    $connection->send(new Chunk($result));
                    $modelType = Common::getModelType($model);
                    if ($userId && User::getBalance($userId, $modelType) > 0) {
                        User::addBalance($userId, $modelType);
                    }
                }
                $connection->send(new Chunk(''));
                $this->saveMessage($userId, $sessionId, $assistantMessageId, $roleId, 'assistant', $result, $realIp, $model, $chatId);
            }
        ];
//        return json(['error' => ['message' => $modelRequestData]]);

        Event::dispatch('ai.chat.completions.request', $modelRequestData);
        Chat::completions($modelRequestData->data, $modelRequestData->options);
        return response()->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    public function translate(Request $request)
    {
        $content = $request->post('content');
        if (!$content) {
            return json(['error' => ['message' => '内容为空']]);
        }
        $setting = Setting::getSetting();
        $model = $setting['translate_model'] ?? 'gpt-3.5-turbo';
        $prompt = !empty($setting['translate_prompt']) ? $setting['translate_prompt'] : '请作为翻译官，将以下内容翻译成英文。注意只做翻译，不回复其它内容。例如"请翻译:画一只小狗"你应该返回"a dog"；例如"请翻译:你能画一只熊猫么?"，你应该返回"a panda"；例如"请翻译:一只猫 --ar:1:2 --s 200"你应该返回"a cat --ar:1:2 --s 200"。记住你的返回中无论如何不能包含中文';
        $connection = $request->connection;
        $modelRequestData = new ModelRequestData();
        $modelRequestData->data = [
            'model' => $model,
            'temperature' => 1,
            'messages' => [
                ['role' => 'user', 'content' => $prompt],
                ['role' => 'assistant', 'content' => '好的，请发送要翻译的内容'],
                ['role' => 'user', 'content' => "请翻译:$content"]
            ],
        ];
        $modelRequestData->options = [
            'complete' => function ($result) use ($connection, $modelRequestData) {
                if (isset($result['error'])) {
                    $result = json_encode($result, JSON_UNESCAPED_UNICODE);
                    Log::error($result);
                    $connection->send(new Chunk($result));
                } else {
                    $responseData = new ModelResponseData();
                    $responseData->data = $result;
                    $responseData->modelRequestData = $modelRequestData;
                    Event::dispatch('ai.chat.translate.response', $responseData);
                    $connection->send(new Chunk(json_encode(['content' => $result])));
                }
                $connection->send(new Chunk(''));
            }
        ];
        Event::dispatch('ai.chat.translate.request', $modelRequestData);
        Chat::completions($modelRequestData->data, $modelRequestData->options);
        return response()->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    /**
     * 总结问题
     * @param Request $request
     * @return Response|null
     */
    public function summarize(Request $request): ?Response
    {
        $messages = $request->post('messages');
        $content = last($messages)['content'] ?? '';
        if (!$content) {
            return json(['error' => ['message' => '内容为空']]);
        }
        $connection = $request->connection;
        $model = $request->post('model');
        $content = json_encode($messages, JSON_UNESCAPED_UNICODE);
        $lastContent = last($messages)['content'];
        $content = $content . "\n\n" . '以上是一段对话，请将对话中最后一句总结成一个具有清晰含义的提问，注意，是总结成一个有明确含义的提问。如果无法总结，请返回 ' . $lastContent;
        Chat::completions([
            'model' => $model,
            'temperature' => 1,
            'messages' => [
                ['role' => 'user', 'content' => $content],
            ]], [
            'complete' => function ($result, $response) use ($connection) {
                if (isset($result['error'])) {
                    $result = json_encode($result, JSON_UNESCAPED_UNICODE);
                    Log::error($result);
                    $connection->send(new Chunk($result));
                } else {
                    $connection->send(new Chunk(json_encode(['content' => $result])));
                }
                $connection->send(new Chunk(''));
            }
        ]);
        return response()->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    /**
     * 处理参考链接
     * @param Request $request
     * @return Response
     */
    public function processReference(Request $request): Response
    {
        try {
            $url = $request->post('url');
            if (!$url) {
                return json(['success' => false, 'message' => 'URL不能为空']);
            }

            // 使用适当的HTML解析库获取页面内容
            $html = file_get_contents($url);
            $dom = new \DOMDocument();
            @$dom->loadHTML($html);
            $xpath = new \DOMXPath($dom);

            // 获取标题
            $titleNode = $xpath->query('//*[@id="title"]')->item(0);
            $title = $titleNode ? trim($titleNode->textContent) : '';

            // 获取描述
            $descriptions = [];
            $bulletPoints = $xpath->query('//*[@id="feature-bullets"]/ul/li');
            foreach ($bulletPoints as $index => $point) {
                if ($index >= 5) break; // 只获取前5条
                $descriptions[] = trim($point->textContent);
            }

            return json([
                'success' => true,
                'title' => $title,
                'description' => $descriptions
            ]);
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 爬取亚马逊商品信息
     */
    public function crawlAmazon(Request $request)
    {
        $urls = $request->post('urls', []);
        if (empty($urls)) {
            return json(['success' => false, 'message' => 'No URLs provided']);
        }

        // 创建多线程请求
        $results = $this->multiCurlRequest($urls);
        
        return json([
            'success' => true,
            'data' => $results,
            'mainLanguage' => $this->determineMainLanguage($results)
        ]);
    }

    /**
     * 执行多线程 cURL 请求
     */
    private function multiCurlRequest(array $urls)
    {
        $mh = curl_multi_init();
        $curlHandles = [];
        $results = [];

        // 初始化每个 cURL 句柄
        foreach ($urls as $index => $url) {
            if (!preg_match('/amazon\.[^\/]+\/(?:.+\/)?(?:dp|gp\/product)\/([A-Z0-9]{10})/', $url)) {
                $results[$index] = ['url' => $url, 'success' => false, 'error' => 'Invalid Amazon URL'];
                continue;
            }

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept: text/html,application/xhtml+xml,application/xml',
                    'Accept-Language: en-US,en;q=0.9',
                    'Cache-Control: no-cache',
                ],
                CURLOPT_ENCODING => 'gzip, deflate',
            ]);

            curl_multi_add_handle($mh, $ch);
            $curlHandles[$index] = $ch;
        }

        // 执行并发请求
        $running = null;
        do {
            curl_multi_exec($mh, $running);
            curl_multi_select($mh);
        } while ($running);

        // 处理响应
        foreach ($curlHandles as $index => $ch) {
            $html = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if ($httpCode === 200 && !empty($html)) {
                $results[$index] = $this->parseProductPage($html, $urls[$index]);
            } else {
                $results[$index] = [
                    'url' => $urls[$index],
                    'success' => false,
                    'error' => 'Failed to fetch content: HTTP ' . $httpCode
                ];
            }

            curl_multi_remove_handle($mh, $ch);
            curl_close($ch);
        }

        curl_multi_close($mh);

        return array_values($results); // 重新索引数组
    }

    /**
     * 解析商品页面
     */
    private function parseProductPage($html, $url)
    {
        try {
            $doc = new \DOMDocument();
            @$doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_NOERROR | LIBXML_NOWARNING);
            $xpath = new \DOMXPath($doc);

            // 使用缓存的选择器数组
            $titleSelectors = ['#productTitle', '.product-title-word-break', '.product-title'];
            $featureSelectors = [
                '//*[@id="feature-bullets"]/ul/li',
                '//*[@id="featurebullets_feature_div"]/div/ul/li',
                '//div[contains(@id, "feature-bullets")]//ul/li'
            ];

            // 获取标题
            $title = '';
            foreach ($titleSelectors as $selector) {
                $titleNode = $xpath->query('//*[@id="' . trim($selector, '#') . '"]')->item(0);
                if ($titleNode) {
                    $title = trim($titleNode->textContent);
                    break;
                }
            }

            if (empty($title)) {
                throw new \Exception('Title not found');
            }

            // 获取特征描述
            $features = [];
            foreach ($featureSelectors as $selector) {
                $featureNodes = $xpath->query($selector);
                if ($featureNodes->length > 0) {
                    foreach ($featureNodes as $node) {
                        $featureText = trim($node->textContent);
                        if (!empty($featureText)) {
                            $features[] = $featureText;
                        }
                    }
                    break;
                }
            }

            $language = $this->detectLanguage($title);

            return [
                'url' => $url,
                'title' => $title,
                'features' => $features,
                'success' => true,
                'language' => $language
            ];

        } catch (\Exception $e) {
            return [
                'url' => $url,
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 确定主要语言
     */
    private function determineMainLanguage($results)
    {
        $languageCounts = [];
        foreach ($results as $result) {
            if ($result['success'] && isset($result['language'])) {
                $lang = $result['language'];
                $languageCounts[$lang] = ($languageCounts[$lang] ?? 0) + 1;
            }
        }

        return !empty($languageCounts) ? array_search(max($languageCounts), $languageCounts) : 'en';
    }

    /**
     * 检测文本语言的简化方法
     */
    private function detectLanguage($text)
    {
        // 简单检测是否含有中文字符
        if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $text)) {
            return 'zh';
        }
        
        // 检测是否含有日文字符
        if (preg_match('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}]/u', $text)) {
            return 'ja';
        }
        
        // 其他常见欧洲语言字符
        if (preg_match('/[àáâäãåèéêëìíîïòóôöõùúûüÿ]/i', $text)) {
            return 'other';
        }
        
        return 'en'; // 默认为英文
    }

}
