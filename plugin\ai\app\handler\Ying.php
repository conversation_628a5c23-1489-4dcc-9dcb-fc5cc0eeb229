<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

class Ying extends Base
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = '莹莹朗读';

    /**
     * @var string 模型类型
     */
    protected static $type = 'ying';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'ying',
        'yingying',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 200,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Ying::class;

    /**
     * Speech
     * @param array $data
     * @param array $options
     * @return void
     */
    public function speech(array $data, array $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->speech($data, $options);
    }

}