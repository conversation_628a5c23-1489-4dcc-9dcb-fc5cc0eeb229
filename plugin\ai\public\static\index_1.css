

.model-item[index_1] {
    transition: transform .3s ease
}

.model-item[index_1]:hover {
    transform: scale(1.1)
}

.swiper-button-next[index_1],.swiper-button-prev[index_1] {
    align-items: center;
    border-radius: 100px;
    cursor: pointer;
    display: flex;
    height: 30px;
    justify-content: center;
    position: relative;
    width: 30px;
    z-index: 99
}
.index_img{
    width: 18px;
    height: 18px;
    object-fit: cover;
}
.index_img_down{
    width: 16px;height: 16px; object-fit: cover;
}
.creation-lists[index_1] {
    flex: 1;
    min-width: 0;
    padding: 10px 0;
}

.creation-lists .creation-item[index_1] {
    /* -webkit-box-orient:vertical; */
    /* -webkit-line-clamp:1; */
    /* border-radius:8px; */
    box-shadow: 0 2px 6px #ebeefd;
    /* color:var(--el-text-color-primary); */
    /* cursor:pointer; */
    /* display:-webkit-box; */
    /* font-size:14px; */
    /* height:40px; */
    line-height: 40px;
    overflow: hidden;
    padding: 0 24px;
    text-align: center
}

.creation-lists .creation-item--active[index_1] {
    background: linear-gradient(87.73deg,var(--gradient-1) 0,var(--gradient-2) 100%);
    box-shadow: 0 3px 6px #ebeefd;
    color: #fff;
    color: var(--color-btn-text,#fff)
}

.cardItem .collect[index_1] {
    opacity: 0;
    transition: all .2s linear
}

.cardItem :hover .collect[index_1] {
    opacity: 1
}

.search[index_1] .el-input .el-input__wrapper {
    border-radius: 16px
}

.cunstom-btn[index_1] {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 46px;
    justify-content: center;
    width: 100%
}

.cunstom-btn .cunstom-path[index_1] {
    margin: 0 8px
}

.cunstom-btn .btn[index_1] {
    border: none;
    height: 36px
}

.cunstom-btn .icon[index_1] {
    margin-right: 5px
}

.cunstom-btn .text[index_1] {
    font-size: 14px
}
