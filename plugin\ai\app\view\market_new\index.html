<html>

<head>
    <style>
        body {
            transition: opacity ease-in 0.2s;
        }

        body[unresolved] {
            opacity: 0;
            display: block;
            overflow: hidden;
            position: relative;
        }
    </style>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <style>
        .header-2 {
            font-size: 24px;
            font-family: Arial, sans-serif;
            display: inline-block;
            overflow: hidden;
            /* 隐藏溢出的内容 */
            white-space: nowrap;
            /* 防止文本换行 */
        }

        .cursor {
            border-right: 0.1em solid black;
            /* 光标 */
            animation: blink 0.5s step-end infinite;
        }

        @keyframes blink {
            50% {
                border-color: transparent;
            }
        }
    </style>
    <!-- navigation.f9cec2d9.css-->
    <link rel="stylesheet" href="/app/ai/static/index.css">
    <link rel="stylesheet" href="/app/ai/static/index_1.css">
    <link rel="stylesheet" href="/app/ai/static/index_2.css">
    <link rel="stylesheet" href="/app/ai/static/navigation.css">
    <link rel="stylesheet" href="/app/ai/static/swiper.css">
    <link rel="stylesheet" href="/app/ai/static/el-scrollbar.css">
    <link rel="stylesheet" href="/app/ai/static/index1.css">
    <link rel="shortcut icon" href="/favicon.ico" />
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>
    <style type="text/css">
        :root body {
            --color-primary: #43CEA2;
            --color-minor: #185A9D;
            --color-btn-text: white;
            --el-color-primary: #43CEA2;
            --el-color-primary-dark-2: rgb(54, 165, 130);
            --el-color-primary-light-3: rgb(123, 221, 190);
            --el-color-primary-light-5: rgb(161, 231, 209);
            --el-color-primary-light-7: rgb(199, 240, 227);
            --el-color-primary-light-8: rgb(217, 245, 236);
            --el-color-primary-light-9: rgb(236, 250, 246);
        }
    </style>

    <title>亚马逊AI助手</title>
    <meta name="description"
        content="我们提供领先的人工智能技术，包括AI创作、聊天机器人、文本生成和学术工具。利用我们的智能系统，你可以获得自动生成的文本内容、与聊天机器人互动以及使用学术工具来支持你的研究和写作。欢迎探索我们的智能平台，体验前沿的人工智能技术带给你的无限可能性。">
    <meta name="keywords" content="人工智能创作, AI聊天机器人, 文本生成, 学术工具, 自然语言处理, 智能技术">

    <style>
        .monica-reading-highlight {
            animation: fadeInOut 1.5s ease-in-out;
        }

        @keyframes fadeInOut {

            0%,
            100% {
                background-color: transparent;
            }

            30%,
            70% {
                background-color: rgba(2, 118, 255, 0.20);
            }
        }
    </style>
</head>

<body style="overflow: hidden;">
    <div id="__nuxt" data-v-app="" style="display: block;unicode-bidi: isolate;">
        <div class="nuxt-loading-indicator"
            style="position: fixed; top: 0px; right: 0px; left: 0px; pointer-events: none; width: auto; height: 2px; opacity: 0; background: rgb(74, 93, 255); transform: scaleX(0); transform-origin: left center; transition: transform 0.1s ease 0s, height 0.4s ease 0s, opacity 0.4s ease 0s; z-index: 999999;">
        </div>
        <section index_1="" class="index_container" style="height:100%;">
            <section class="index_container">
                <section class="index_container">
                    <main class="scrollbar" style="padding: 0px;width: 100%">
                        <div class="h-full" style="padding-bottom: 12px">
                            <div class="h-full" style="padding-left: 12px;padding-right: 12px">
                                <div index_1="" class="h-full flex flex_column bg-white rounded-[4px] py-[16px] ">
                                    <div index_1="" class="cunstom-header">
                                        <div index_1="" class="header-1"> 亚马逊AI助手 Plus </div>
                                        <div index_1="" class="header-2">
                                            <span class="header-2_text" index_1=""></span>
                                            <span class="cursor" index_1=""></span>
                                        </div>
                                    </div>
                                    <div index_1="" class="mt-[16px] ml-[16px] search">
                                        <div index_1="" class="el-input el-input--large el-input--suffix"
                                            style="width: 99%; height: 40px; border-radius: 15px;">
                                            <div class="el-input__wrapper"><input class="el-input__inner" type="text"
                                                    autocomplete="off" tabindex="0" placeholder="请输入关键词搜索"
                                                    id="el-id-100-4"><span class="el-input__suffix"><span
                                                        class="el-input__suffix-inner"><i index_1=""
                                                            class="el-icon el-input__icon"><svg index_1=""
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                viewBox="0 0 1024 1024">
                                                                <path fill="currentColor"
                                                                    d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704z">
                                                                </path>
                                                            </svg></i>
                                                    </span>
                                                </span></div>
                                        </div>
                                    </div>
                                    <div index_1="" class="flex-1 min-h-0">
                                        <div index_1="" class="el-scrollbar">
                                            <div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default">
                                                <div class="el-scrollbar__view">
                                                    <div index_1="" id="app" class="row" v-cloak>
                                                        <div index_1="" v-for="role in filteredRoles" :key="role.roleId"
                                                            class="model-lists flex flex-wrap mt-[10px] px-[6px]">
                                                            <div index_1=""
                                                                class="model-item p-[5px] sm:p-[10px] 2xl:w-1/6">
                                                                <a index_1="" href="detail" class="h-full">
                                                                    <div index_1=""
                                                                        class="el-card is-never-shadow !border-none h-full rounded-[12px] cardItem"
                                                                        style="box-shadow: rgb(235, 238, 253) 0px 3px 10px;">
                                                                        <div class="el-card__body"
                                                                            style="height: 100%; padding: 20px;">
                                                                            <div index_1=""
                                                                                class="h-full flex flex_column">
                                                                                <div index_1="" class="flex">
                                                                                    <div index_1=""
                                                                                        class="flex items-center">
                                                                                        <img index_1=""
                                                                                            class="w-[56px] h-[56px] mr-[7px]"
                                                                                            :src="role.avatar" alt="">
                                                                                        <div index_1=""
                                                                                            class="font-medium line-clamp-1"
                                                                                            style="font-weight: 800; letter-spacing: 0.025em; font-size: 17px;">
                                                                                            {{ role.name }}
                                                                                        </div>
                                                                                    </div>
                                                                                    <div index_1=""
                                                                                        class="ml-auto collect">
                                                                                        <div index_1=""
                                                                                            class="scale-[0.85]"></div>
                                                                                    </div>
                                                                                </div>
                                                                                <div index_1=""
                                                                                    class="text-xl text-tx-secondary mt-[10px] line-clamp-2 flex-1"
                                                                                    style="font-size: 13px;">
                                                                                    {{ role.desc }}
                                                                                </div>
                                                                                <div index_1=""
                                                                                    class="flex mt-[15px] items-center">
                                                                                    <span index_1=""
                                                                                        class="text-tx-secondary text-sm mr-auto">🔥{{ role.installed }}人使用过</span>
                                                                                </div>
                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                            <div index_1=""
                                                                class="model-item p-[5px] sm:p-[10px] 2xl:w-1/6">
                                                                <a index_1="" href="test_video" class="h-full">
                                                                    <div index_1=""
                                                                        class="el-card is-never-shadow !border-none h-full rounded-[12px] cardItem"
                                                                        style="box-shadow: rgb(235, 238, 253) 0px 3px 10px;">
                                                                        <div class="el-card__body"
                                                                            style="height: 100%; padding: 20px;">
                                                                            <div index_1=""
                                                                                class="h-full flex flex_column">
                                                                                <div index_1="" class="flex">
                                                                                    <div index_1=""
                                                                                        class="flex items-center">
                                                                                        <img index_1=""
                                                                                            class="w-[56px] h-[56px] mr-[7px]"
                                                                                            :src="role.avatar" alt="">
                                                                                        <div index_1=""
                                                                                            class="font-medium line-clamp-1"
                                                                                            style="font-weight: 800; letter-spacing: 0.025em; font-size: 17px;">
                                                                                            测试视频播放
                                                                                        </div>
                                                                                    </div>
                                                                                    <div index_1=""
                                                                                        class="ml-auto collect">
                                                                                        <div index_1=""
                                                                                            class="scale-[0.85]"></div>
                                                                                    </div>
                                                                                </div>
                                                                                <div index_1=""
                                                                                    class="text-xl text-tx-secondary mt-[10px] line-clamp-2 flex-1"
                                                                                    style="font-size: 13px;">
                                                                                    {{ role.desc }}
                                                                                </div>
                                                                                <div index_1=""
                                                                                    class="flex mt-[15px] items-center">
                                                                                    <span index_1=""
                                                                                        class="text-tx-secondary text-sm mr-auto">🔥
                                                                                        {{ role.installed }}人使用过</span>
                                                                                </div>
                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                            <div style="display: none;">
                                                <div class="el-scrollbar__thumb" style="transform: translateX(0%);">
                                                </div>
                                            </div>
                                            <div class=" is-vertical" style="display: none;">
                                                <div class="el-scrollbar__thumb"
                                                    style="height: 61.5089px; transform: translateY(0%);"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div index_1="" class="flex justify-end mt-4 px-[16px]">
                                        <div index_1="" class="pagination">
                                            <div class="el-pagination is-background" modelvalue="[object Object]"><span
                                                    class="el-pagination__total is-first" disabled="false">共 61
                                                    条</span><span class="el-pagination__sizes">
                                                    <div class="el-select el-select--default">
                                                        <div
                                                            class="select-trigger el-tooltip__trigger el-tooltip__trigger">
                                                            <!--v-if--><!-- fix: https://github.com/element-plus/element-plus/issues/11415 --><!--v-if-->
                                                            <div class="el-input el-input--default el-input--suffix">
                                                                <!-- input --><!-- prepend slot --><!--v-if-->
                                                                <div class="el-input__wrapper">
                                                                    <!-- prefix slot --><!--v-if--><input
                                                                        class="el-input__inner" type="text" readonly=""
                                                                        autocomplete="off" tabindex="0"
                                                                        placeholder="请选择"
                                                                        id="el-id-100-5"><!-- suffix slot --><span
                                                                        class="el-input__suffix"><span
                                                                            class="el-input__suffix-inner"><i
                                                                                class="el-icon el-select__caret el-select__icon"><svg
                                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                                    viewBox="0 0 1024 1024">
                                                                                    <path fill="currentColor"
                                                                                        d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z">
                                                                                    </path>
                                                                                </svg></i><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span>
                                                                </div><!-- append slot --><!--v-if-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </span><button type="button" class="btn-prev" disabled=""
                                                    aria-label="上一页" aria-disabled="true"><i class="el-icon"><svg
                                                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path fill="currentColor"
                                                                d="M609.408 149.376 277.76 489.6a32 32 0 0 0 0 44.672l331.648 340.352a29.12 29.12 0 0 0 41.728 0 30.592 30.592 0 0 0 0-42.752L339.264 511.936l311.872-319.872a30.592 30.592 0 0 0 0-42.688 29.12 29.12 0 0 0-41.728 0z">
                                                            </path>
                                                        </svg></i></button>
                                                <ul class="el-pager">
                                                    <li class="is-active number" aria-current="true" aria-label="第 1 页"
                                                        tabindex="0"> 1 </li>
                                                    <!--v-if-->
                                                    <li class="number" aria-current="false" aria-label="第 2 页"
                                                        tabindex="0">2</li><!--v-if-->
                                                    <li class="number" aria-current="false" aria-label="第 3 页"
                                                        tabindex="0">3</li>
                                                </ul><button type="button" class="btn-next" aria-label="下一页"
                                                    aria-disabled="false"><i class="el-icon"><svg
                                                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path fill="currentColor"
                                                                d="M340.864 149.312a30.592 30.592 0 0 0 0 42.752L652.736 512 340.864 831.872a30.592 30.592 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z">
                                                            </path>
                                                        </svg></i></button><span class="el-pagination__jump is-last"
                                                    disabled="false"><span class="el-pagination__goto">前往</span>
                                                    <div
                                                        class="el-input el-input--default el-pagination__editor is-in-pagination">
                                                        <!-- input --><!-- prepend slot --><!--v-if-->
                                                        <div class="el-input__wrapper">
                                                            <!-- prefix slot --><!--v-if--><input
                                                                class="el-input__inner" min="1" max="3" type="number"
                                                                autocomplete="off" tabindex="0" aria-label="页"
                                                                id="el-id-100-6"><!-- suffix slot --><!--v-if--></div>
                                                        <!-- append slot --><!--v-if-->
                                                    </div><span class="el-pagination__classifier">页</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </section>
            </section>
        </section>
    </div>

    <div id="el-popper-container-100">
        <div class="el-popper is-pure is-light el-select__popper" tabindex="-1" aria-hidden="true" role="tooltip"
            id="el-id-100-1" style="z-index: 2002; position: absolute; inset: 597px auto auto 1298px; display: none;"
            data-popper-reference-hidden="false" data-popper-escaped="false" data-popper-placement="bottom-start">
            <div class="el-select-dropdown" style="min-width: 128px;">
                <div class="el-scrollbar" style="">
                    <div class="el-select-dropdown__wrap el-scrollbar__wrap el-scrollbar__wrap--hidden-default">
                        <ul class="el-scrollbar__view el-select-dropdown__list" style=""><!--v-if-->
                            <li class="el-select-dropdown__item"><span>15条/页</span></li>
                            <li class="el-select-dropdown__item"><span>20条/页</span></li>
                            <li class="el-select-dropdown__item selected"><span>30条/页</span></li>
                            <li class="el-select-dropdown__item"><span>40条/页</span></li>
                        </ul>
                    </div>
                    <div style="display: none;">
                        <div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div>
                    </div>
                    <div class=" is-vertical" style="display: none;">
                        <div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div>
                    </div>
                </div><!--v-if-->
            </div><span class="el-popper__arrow" data-popper-arrow="" style="position: absolute; left: 0px;"></span>
        </div>
    </div>
    <div class="el-overlay" style="z-index: 2003; display: none;">
        <div role="dialog" aria-modal="true" aria-labelledby="el-id-100-2" aria-describedby="el-id-100-3"
            class="el-overlay-dialog">
            <div class="el-dialog" tabindex="-1" style="--el-dialog-width: 600px;">
                <header class="el-dialog__header">
                    <div data-v-2e42f60d="" class="text-lg text-center font-medium">已更新openAI最新模型（gpt-4o）</div><button
                        aria-label="el.dialog.close" class="el-dialog__headerbtn" type="button"><i
                            class="el-icon el-dialog__close"><svg xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 1024 1024">
                                <path fill="currentColor"
                                    d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z">
                                </path>
                            </svg></i></button>
                </header>
                <div id="el-id-100-3" class="el-dialog__body">
                    <div data-v-2e42f60d="" class="el-scrollbar">
                        <div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default"
                            style="height: 100%; max-height: 60vh;">
                            <div class="el-scrollbar__view" style="">
                                <div data-v-2e42f60d="" class="richText">
                                    <p style="text-align: center; line-height: 2;"><span
                                            style="color: rgba(0, 0, 0, 0.88); background-color: rgba(250, 250, 250, 0.02); font-size: 16px;">openai发布最新的模型，速度极快的gpt-4版本</span>
                                    </p>
                                    <p style="text-align: justify; line-height: 2;"><img
                                            src="https://hetao-1306534873.cos.ap-shanghai.myqcloud.com/uploads/images/20240514/202405141302087f2923212.jpg"
                                            alt="" data-href="" style=""></p>
                                </div>
                            </div>
                        </div>
                        <div style="display: none;">
                            <div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div>
                        </div>
                        <div class=" is-vertical" style="display: none;">
                            <div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div>
                        </div>
                    </div>
                    <div data-v-2e42f60d="" class="flex-1 flex justify-center items-center bg-white pt-[20px]"><button
                            data-v-2e42f60d="" aria-disabled="false" type="button"
                            class="el-button el-button--primary el-button--large"><!--v-if--><span class=""> 我知道了
                            </span></button>
                    </div>
                </div><!--v-if-->
            </div>
        </div>
    </div>
    <div id="monica-content-root" class="monica-widget"></div>
</body><tinamind-app style="position: relative; z-index: 2147483647; visibility: visible;"></tinamind-app>
<svg id="__svg__icons__dom__" xmlns="http://www.w3.org/2000/svg" xmlns:link="http://www.w3.org/1999/xlink"
    style="position: absolute; width: 0px; height: 0px">
    <symbol viewBox="0 0 18 18" class="design-iconfont" id="local-icon-audio_voice">
        <path
            d="M8.751 16.011a1.6 1.6 0 0 1-.914-.292l-2.705-1.791a.284.284 0 0 0-.292-.073H2.645A1.638 1.638 0 0 1 1 12.21v-6.4a1.638 1.638 0 0 1 1.645-1.645h2.194a.689.689 0 0 0 .292-.073l2.705-1.828a1.658 1.658 0 0 1 2.3.475 1.57 1.57 0 0 1 .256.914v10.712a1.687 1.687 0 0 1-1.641 1.646ZM2.609 5.262a.538.538 0 0 0-.548.548v6.362a.562.562 0 0 0 .548.548H4.8a1.6 1.6 0 0 1 .914.292l2.705 1.791a.541.541 0 0 0 .768-.146.415.415 0 0 0 .11-.292V3.617a.625.625 0 0 0-.256-.475.5.5 0 0 0-.548.037L5.751 4.97a1.6 1.6 0 0 1-.914.292Zm11.626 9.729a.767.767 0 0 1-.4-.146.53.53 0 0 1 0-.768 7.15 7.15 0 0 0 0-10.091l-.037-.037a.53.53 0 0 1 .037-.768.555.555 0 0 1 .731 0 8.242 8.242 0 0 1 .073 11.626l-.037.037a.559.559 0 0 1-.367.147Z">
        </path>
        <path
            d="M12.371 13.379a.543.543 0 0 1-.4-.914 4.854 4.854 0 0 0 0-6.873.544.544 0 0 1 .731-.8.036.036 0 0 1 .037.037 5.991 5.991 0 0 1 .036 8.445 1.3 1.3 0 0 1-.404.105Z">
        </path>
    </symbol>
    <symbol class="icon" viewBox="0 0 1024 1024" id="local-icon-back">
        <path
            d="M670.054 299.162h-464.23l139.06-143.514a31.744 31.744 0 0 0 0-44.032 29.44 29.44 0 0 0-42.702 0l-190.924 197.12a31.795 31.795 0 0 0 0 44.032l190.976 197.12c11.776 12.134 30.822 12.134 42.65 0a31.795 31.795 0 0 0 0-44.083L207.666 364.237h462.387c104.192 0 188.672 87.296 188.672 195.02 0 107.674-84.48 194.97-188.672 194.97h-408.73a32 32 0 0 0-31.436 32.512c0 17.92 14.08 32.512 31.437 32.512h408.73C808.96 819.2 921.6 702.771 921.6 559.206c0-143.616-112.64-260.044-251.546-260.044z">
        </path>
    </symbol>
    <symbol viewBox="0 0 16 16" id="local-icon-change">
        <path
            d="M8.719 10.6a2.042 2.042 0 0 1 2.042 2.042v1.32A2.042 2.042 0 0 1 8.719 16H7.357a2.042 2.042 0 0 1-2.042-2.043v-1.32A2.042 2.042 0 0 1 7.357 10.6Zm6.561-7.106a.553.553 0 0 1 .013 1.106h-1.6a4.956 4.956 0 0 1 1.938 4.209 7.047 7.047 0 0 1-2.465 4.726.553.553 0 1 1-.737-.825 5.956 5.956 0 0 0 2.094-3.964 3.9 3.9 0 0 0-1.724-3.443v1.768a.553.553 0 0 1-.54.553h-.013a.553.553 0 0 1-.553-.54V4.042a.553.553 0 0 1 .54-.553h3.042Zm-11.133-.6a.553.553 0 0 1-.17.754l-.011.007a4.035 4.035 0 0 0-2.164 3.741 5.618 5.618 0 0 0 1.393 3.244V8.808a.553.553 0 0 1 .54-.553h.013a.553.553 0 0 1 .553.54v3.042a.553.553 0 0 1-.54.553H.719a.553.553 0 0 1-.013-1.106H2.3A6.688 6.688 0 0 1 .7 7.445 5.134 5.134 0 0 1 3.386 2.7a.553.553 0 0 1 .761.181ZM8.719 0a2.042 2.042 0 0 1 2.042 2.043V3.4a2.042 2.042 0 0 1-2.042 2.042H7.357A2.042 2.042 0 0 1 5.315 3.4V2.043A2.042 2.042 0 0 1 7.357 0Z">
        </path>
    </symbol>
    <symbol class="icon" viewBox="0 0 1024 1024" id="local-icon-chat">
        <path
            d="M851.235 727.645c70.899-69.8 109.61-158.18 109.61-251.621 0-211.787-201.347-384.091-448.842-384.091S63.154 264.237 63.154 476.023c0 211.793 201.354 384.099 448.85 384.099 62.844 0 123.418-10.856 180.363-32.3l166.763 98.83a38.685 38.685 0 0 0 19.76 5.416 38.755 38.755 0 0 0 25.49-9.554c12.61-10.994 16.744-28.842 10.256-44.267l-63.4-150.602zm-82.05 4.986 28.369 67.384-82.07-48.64a38.787 38.787 0 0 0-34.868-2.355c-52.633 22.265-109.365 33.55-168.612 33.55-204.737 0-371.298-137.518-371.298-306.546s166.56-306.541 371.297-306.541c204.73 0 371.292 137.512 371.292 306.54 0 79.35-36.812 154.697-103.646 212.16-12.768 10.975-16.996 28.936-10.463 44.448z">
        </path>
        <path
            d="M285.812 479.686a50.523 50.523 0 1 0 103.4 0 50.523 50.523 0 1 0-103.4 0zm168.026 0a50.523 50.523 0 1 0 103.4 0 50.523 50.523 0 1 0-103.4 0zm168.026 0a50.523 50.523 0 1 0 103.4 0 50.523 50.523 0 1 0-103.4 0z">
        </path>
    </symbol>
    <symbol viewBox="0 0 20 20" id="local-icon-collection">
        <path
            d="M13.939 17.718a1.9 1.9 0 0 1-.8-.19l-2.856-1.485a.65.65 0 0 0-.533 0l-2.856 1.485a1.715 1.715 0 0 1-2.323-.723 2.173 2.173 0 0 1-.152-1.1l.533-3.16a.563.563 0 0 0-.152-.495L2.519 9.798a1.735 1.735 0 0 1-.038-2.437 1.647 1.647 0 0 1 .99-.495l3.16-.457a.59.59 0 0 0 .419-.3l1.447-2.894a1.708 1.708 0 0 1 2.285-.762 1.64 1.64 0 0 1 .762.762l1.409 2.894a.59.59 0 0 0 .419.3l3.16.457a1.69 1.69 0 0 1 1.447 1.942 1.647 1.647 0 0 1-.495.99l-2.285 2.246a.5.5 0 0 0-.19.495l.533 3.16a1.691 1.691 0 0 1-.685 1.675 1.3 1.3 0 0 1-.918.344Zm-3.922-2.894a1.9 1.9 0 0 1 .8.19l2.856 1.485a.557.557 0 0 0 .8-.609l-.533-3.2a1.617 1.617 0 0 1 .495-1.523l2.285-2.246a.552.552 0 0 0 0-.8.483.483 0 0 0-.343-.152l-3.16-.457a1.672 1.672 0 0 1-1.295-.952l-1.407-2.854a.621.621 0 0 0-.762-.267.592.592 0 0 0-.228.3L8.078 6.633a1.858 1.858 0 0 1-1.295.952l-3.16.457a.591.591 0 0 0-.495.647.684.684 0 0 0 .152.343l2.285 2.254a1.617 1.617 0 0 1 .495 1.523l-.533 3.16a.564.564 0 0 0 .457.647.538.538 0 0 0 .343-.038l2.855-1.492a1.539 1.539 0 0 1 .838-.262Z">
        </path>
    </symbol>
    <symbol viewBox="0 0 16 16" id="local-icon-context">
        <path
            d="M11.293 12.564a.624.624 0 0 1-.443-1.064l2.155-2.156a2.5 2.5 0 1 0-3.532-3.533L7.319 7.965a.624.624 0 1 1-.882-.883l2.155-2.154a3.746 3.746 0 1 1 5.32 5.275l-.024.023-2.152 2.154a.622.622 0 0 1-.441.183M3.744 18.82a3.747 3.747 0 0 1-2.65-6.395l2.193-2.194a.623.623 0 1 1 .881.882l-2.192 2.194a2.5 2.5 0 1 0 3.533 3.532L7.7 14.645a.625.625 0 0 1 .884.884l-2.191 2.194a3.736 3.736 0 0 1-2.649 1.1m1.873-5a.624.624 0 0 1-.444-1.066L8.92 9.012a.623.623 0 1 1 .882.881L6.055 13.64a.622.622 0 0 1-.44.183"
            transform="translate(.502 -3.32)" style="fill: #333"></path>
    </symbol>
    <symbol viewBox="0 0 24 24" id="local-icon-create">
        <path data-name="路径 24229"
            d="M3.276 18.831A1.263 1.263 0 0 1 2 17.555v-.2l.51-3.418a1.36 1.36 0 0 1 .357-.714L13.479 2.662a1.33 1.33 0 0 1 1.786 0l2.857 2.908a1.233 1.233 0 0 1 0 1.786L7.562 17.963a1.36 1.36 0 0 1-.714.357l-3.418.51Zm.765-4.541-.459 2.959 2.959-.459L16.898 6.433l-2.5-2.5Z">
        </path>
        <path data-name="路径 24230"
            d="m11.569 5.64 1.082-1.083 3.716 3.716-1.083 1.083Zm9.667 16.047H2.817a.765.765 0 1 1 0-1.531h18.418a.765.765 0 1 1 0 1.531Z">
        </path>
    </symbol>
    <symbol viewBox="0 0 20 20" id="local-icon-customer_service">
        <path data-name="路径 24383"
            d="M16.453 9.93a.541.541 0 0 1-.527-.527 5.926 5.926 0 1 0-11.851 0 .528.528 0 0 1-1.055 0 6.98 6.98 0 0 1 13.96-.035.527.527 0 0 1-.527.562Zm-5.943 7.666a.533.533 0 0 1-.527-.457.546.546 0 0 1 .457-.6c2.321-.281 4.782-1.864 5.556-3.552a.527.527 0 0 1 .949.457c-.949 2-3.692 3.8-6.4 4.149Z">
        </path>
        <path data-name="路径 24384"
            d="M16.418 13.728a1.576 1.576 0 0 1-1.582-1.582v-1.407a1.583 1.583 0 0 1 3.165 0v1.407a1.576 1.576 0 0 1-1.583 1.582Zm0-3.516a.541.541 0 0 0-.527.527v1.407a.528.528 0 0 0 1.055 0v-1.407a.518.518 0 0 0-.528-.527ZM3.583 13.728a1.576 1.576 0 0 1-1.582-1.582v-1.407a1.583 1.583 0 0 1 3.165 0v1.407a1.576 1.576 0 0 1-1.584 1.582Zm0-3.516a.541.541 0 0 0-.527.527v1.407a.528.528 0 0 0 1.055 0v-1.407a.518.518 0 0 0-.529-.527Zm7.947 6.33a1.036 1.036 0 0 1-1.055 1.055h-.7a1.055 1.055 0 0 1 0-2.11h.7a1.036 1.036 0 0 1 1.055 1.055Z">
        </path>
    </symbol>
    <symbol viewBox="0 0 24 24" id="local-icon-dialogue">
        <g data-name="评论、消息-04">
            <path data-name="路径 24218"
                d="M8.747 21.499a1.246 1.246 0 0 1-.562-.14 1.163 1.163 0 0 1-.608-1.03 1.66 1.66 0 0 0-1.591-1.685h-.281a3.036 3.036 0 0 1-3.042-3.042V5.541a3.036 3.036 0 0 1 3.042-3.042h12.589a3.036 3.036 0 0 1 3.042 3.042V15.65a3.036 3.036 0 0 1-3.042 3.042h-4.633a1.688 1.688 0 0 0-.936.281l-3.369 2.34a.863.863 0 0 1-.609.186Zm-3.042-17.6a1.628 1.628 0 0 0-1.638 1.638V15.65a1.658 1.658 0 0 0 1.638 1.638h.234a2.994 2.994 0 0 1 3 2.667l3.042-2.106a2.941 2.941 0 0 1 1.732-.562h4.633a1.658 1.658 0 0 0 1.638-1.638V5.541a1.658 1.658 0 0 0-1.638-1.638Z">
            </path>
            <path data-name="路径 24219"
                d="M16.515 9.145H7.528a.7.7 0 0 1 0-1.4h8.985a.7.7 0 1 1 0 1.4Zm0 3.978H7.528a.7.7 0 0 1 0-1.4h8.985a.7.7 0 1 1 0 1.4Z">
            </path>
        </g>
    </symbol>
    <symbol viewBox="0 0 20 20" id="local-icon-extend">
        <path data-name="路径 24746"
            d="M10 7.356a2.414 2.414 0 1 1 2.414-2.414A2.414 2.414 0 0 1 10 7.356Zm0-3.712a1.333 1.333 0 1 0 1.333 1.333A1.33 1.33 0 0 0 10 3.64ZM4.09 17.478A2.092 2.092 0 0 1 2 15.388a2.123 2.123 0 0 1 2.09-2.126 2.108 2.108 0 0 1 0 4.216Zm0-3.135a1.007 1.007 0 0 0-1.009 1.013 1.009 1.009 0 0 0 2.018 0 1.007 1.007 0 0 0-1.009-1.014ZM10 17.478a2.092 2.092 0 0 1-2.09-2.09A2.123 2.123 0 0 1 10 13.262a2.108 2.108 0 0 1 0 4.216Zm0-3.135a1.007 1.007 0 0 0-1.009 1.013 1.009 1.009 0 1 0 2.018 0A1.007 1.007 0 0 0 10 14.342Zm5.91 3.135a2.09 2.09 0 1 1 2.09-2.09 2.092 2.092 0 0 1-2.09 2.09Zm0-3.135a1.007 1.007 0 0 0-1.01 1.013 1.009 1.009 0 1 0 2.018 0 1.007 1.007 0 0 0-1.008-1.014Z">
        </path>
        <path data-name="路径 24747"
            d="M10 14.271a.554.554 0 0 1-.541-.541V6.991a.541.541 0 1 1 1.081 0v6.739a.531.531 0 0 1-.54.541Z"></path>
        <path data-name="路径 24748"
            d="M15.874 14.163a.508.508 0 0 1-.5-.4 5.642 5.642 0 0 0-10.7-.144.551.551 0 0 1-1.081-.216c0-.036 0-.072.036-.072a6.693 6.693 0 0 1 12.757.18.537.537 0 0 1-.4.649c-.001-.036-.04.003-.112.003Z">
        </path>
    </symbol>
    <symbol class="icon" viewBox="0 0 1024 1024" id="local-icon-lasso">
        <path
            d="M244.185 326.538c-62.386 41.944-96.493 96.728-96.493 153.364 0 33.2 11.58 65.339 33.517 94.838a29.538 29.538 0 0 1-47.38 35.288c-28.436-38.203-45.214-82.471-45.214-130.126 0-82.314 49.625-153.285 122.644-202.359 73.177-49.23 172.662-78.73 281.049-78.73s207.832 29.5 281.048 78.73C846.376 326.617 896 397.627 896 479.902c0 21.937-3.584 43.323-10.28 63.684a29.538 29.538 0 0 1-56.122-18.432c4.804-14.65 7.325-29.774 7.325-45.252 0-56.636-34.146-111.42-96.532-153.364-62.227-41.827-149.858-68.648-248.123-68.648-98.225 0-185.856 26.821-248.044 68.648zm440.713 370.097a29.538 29.538 0 0 1-18.825 37.297 556.898 556.898 0 0 1-173.765 27.058c-55.296 0-108.111-7.68-156.278-21.662a29.538 29.538 0 1 1 16.462-56.714c42.654 12.367 89.915 19.299 139.816 19.299 56.005 0 108.701-8.744 155.254-24.064a29.538 29.538 0 0 1 37.297 18.786z">
        </path>
        <path
            d="m973.154 645.986-323.19-149.543L775.956 846.77l71.365-130.678 125.833-70.105zM247.966 730.9c28.947-4.018 51.318-30.405 51.318-62.346 0-34.737-26.427-62.897-59.077-62.897-32.61 0-59.077 28.16-59.077 62.897 0 17.605 6.813 33.477 17.723 44.898-16.778 25.915-52.382 55.296-120.123 55.296a25.6 25.6 0 1 0 0 51.2c94.602 0 146.274-46.631 169.236-89.009z">
        </path>
    </symbol>
    <symbol viewBox="0 0 24 24" id="local-icon-painting">
        <path data-name="路径 24741"
            d="M19.444 8.894H4.558v-4.84a2.046 2.046 0 0 1 2.055-2.055h10.731a2.046 2.046 0 0 1 2.055 2.055v4.84ZM5.928 7.524h12.1v-3.47a.7.7 0 0 0-.685-.685H6.612a.7.7 0 0 0-.685.685Z">
        </path>
        <path data-name="路径 24742"
            d="M11.996 21.999a2.845 2.845 0 0 1-2.831-2.831v-2.329a1.618 1.618 0 0 0-1.6-1.6H6.064a2.962 2.962 0 0 1-2.968-2.966v-1.735A2.962 2.962 0 0 1 6.064 7.57h11.872a2.962 2.962 0 0 1 2.968 2.968v1.735a2.962 2.962 0 0 1-2.968 2.968h-1.507a1.618 1.618 0 0 0-1.6 1.6v2.329a2.876 2.876 0 0 1-2.833 2.829ZM6.064 8.894a1.618 1.618 0 0 0-1.6 1.6v1.735a1.618 1.618 0 0 0 1.6 1.6h1.461a2.962 2.962 0 0 1 2.968 2.968v2.329a1.482 1.482 0 0 0 1.553 1.416 1.522 1.522 0 0 0 1.416-1.416v-2.332a2.962 2.962 0 0 1 2.968-2.968h1.507a1.618 1.618 0 0 0 1.6-1.6v-1.734a1.618 1.618 0 0 0-1.6-1.6H6.064Zm2.648-2.831a.7.7 0 0 1-.685-.685V2.684a.685.685 0 1 1 1.37 0v2.694a.672.672 0 0 1-.685.685Zm3.288 0a.7.7 0 0 1-.685-.685V2.684a.685.685 0 1 1 1.37 0v2.694a.647.647 0 0 1-.689.685Zm3.288 0a.7.7 0 0 1-.685-.685V2.684a.685.685 0 1 1 1.37 0v2.694a.672.672 0 0 1-.685.685Z">
        </path>
    </symbol>
    <symbol class="icon" viewBox="0 0 1024 1024" id="local-icon-paintings">
        <path
            d="M550.912 961.536h-3.072c-72.704-1.024-125.952-52.224-145.408-138.24C368.64 671.744 307.2 677.888 222.208 687.104c-49.152 5.12-104.448 11.264-146.432-26.624-33.792-30.72-49.152-81.92-49.152-160.768 0-241.664 196.608-438.272 438.272-438.272 193.536 0 366.592 130.048 420.864 316.416 6.144 21.504-6.144 44.032-27.648 51.2-21.504 6.144-44.032-6.144-51.2-27.648-44.032-151.552-184.32-257.024-342.016-257.024-196.608 0-356.352 159.744-356.352 356.352 0 69.632 14.336 93.184 22.528 100.352 14.336 13.312 47.104 9.216 82.944 6.144 87.04-9.216 219.136-23.552 269.312 199.68 16.384 73.728 54.272 73.728 66.56 74.752h1.024C634.88 879.616 768 754.688 808.96 595.968c6.144-21.504 27.648-34.816 50.176-29.696 21.504 6.144 34.816 27.648 29.696 50.176-49.152 185.344-205.824 345.088-337.92 345.088z">
        </path>
        <path
            d="M171.00799999999998 458.752a69.632 69.632 0 1 0 139.264 0 69.632 69.632 0 1 0-139.264 0ZM390.144 296.96a52.224 52.224 0 1 0 104.448 0 52.224 52.224 0 1 0-104.448 0ZM615.424 366.592a43.008 43.008 0 1 0 86.016 0 43.008 43.008 0 1 0-86.016 0ZM518.144 654.336c-17.408 0-32.768-10.24-38.912-27.648-7.168-21.504 4.096-45.056 25.6-52.224L946.176 419.84c21.504-7.168 45.056 4.096 52.224 25.6 7.168 21.504-4.096 45.056-25.6 52.224L531.456 652.288c-5.12 1.024-9.216 2.048-13.312 2.048z">
        </path>
    </symbol>
    <symbol viewBox="0 0 20 20" id="local-icon-public_account">
        <path data-name="路径 24743"
            d="M2.571 17.995A.561.561 0 0 1 2 17.424a7.463 7.463 0 0 1 7.5-7.467 7.548 7.548 0 0 1 4.19 1.3.572.572 0 0 1-.5 1.029c-.038-.038-.076-.038-.114-.076a6.316 6.316 0 0 0-8.8 1.676 5.957 5.957 0 0 0-1.143 3.543.586.586 0 0 1-.562.566Z">
        </path>
        <path data-name="路径 24744"
            d="M9.467 11.1A4.552 4.552 0 1 1 14 6.529 4.52 4.52 0 0 1 9.467 11.1Zm0-7.962a3.39 3.39 0 1 0 3.39 3.39 3.4 3.4 0 0 0-3.39-3.385Zm7.962 12.571h-4.572a.572.572 0 0 1 0-1.143h4.571a.586.586 0 0 1 .571.571.561.561 0 0 1-.57.577Z">
        </path>
        <path data-name="路径 24745"
            d="M15.142 18a.586.586 0 0 1-.571-.571v-4.571a.572.572 0 0 1 1.143 0v4.571a.561.561 0 0 1-.572.571Z"></path>
    </symbol>
    <symbol viewBox="0 0 20 20" id="local-icon-purchase_record">
        <path data-name="路径 24749"
            d="M10.003 17.889a7.945 7.945 0 1 1 7.963-7.927 8.188 8.188 0 0 1-.219 1.9.56.56 0 1 1-1.1-.219v-.037a6.849 6.849 0 1 0-8.292 5 7.3 7.3 0 0 0 3.324 0 .545.545 0 0 1 .256 1.059 8.5 8.5 0 0 1-1.932.224Z">
        </path>
        <path data-name="路径 24750"
            d="M10.511 8.977a.766.766 0 0 1-.4-.146l-1.79-1.79a.543.543 0 0 1 .731-.8.036.036 0 0 1 .037.037l1.79 1.79a.53.53 0 0 1 0 .767.424.424 0 0 1-.368.142Z">
        </path>
        <path data-name="路径 24751"
            d="M10.73 8.977a.766.766 0 0 1-.4-.146.53.53 0 0 1 0-.767l1.79-1.79a.543.543 0 0 1 .8.731.036.036 0 0 1-.037.037l-1.79 1.79a.424.424 0 0 1-.363.145Z">
        </path>
        <path data-name="路径 24752"
            d="M13.726 9.784H7.297a.55.55 0 1 1 0-1.1h6.429a.55.55 0 1 1 0 1.1Zm0 2.009H7.297a.55.55 0 0 1 0-1.1h6.429a.55.55 0 0 1 0 1.1Z">
        </path>
        <path data-name="路径 24753"
            d="M10.512 13.798a.562.562 0 0 1-.548-.548V8.428a.55.55 0 0 1 1.1 0v4.822a.538.538 0 0 1-.552.548Zm6.758 1.973h-4.42a.55.55 0 1 1 0-1.1h4.457a.562.562 0 0 1 .548.548.6.6 0 0 1-.585.552Z">
        </path>
        <path data-name="路径 24754"
            d="M15.041 17.999a.562.562 0 0 1-.548-.548v-4.42a.55.55 0 0 1 1.1 0v4.457a.509.509 0 0 1-.552.511Z"></path>
    </symbol>
    <symbol viewBox="0 0 20 20" id="local-icon-quest">
        <path data-name="路径 24222"
            d="M15.08 17.999H4.92a2.6 2.6 0 0 1-2.6-2.6v-10.8a2.6 2.6 0 0 1 2.6-2.6h10.16a2.6 2.6 0 0 1 2.6 2.6v10.8a2.6 2.6 0 0 1-2.6 2.6ZM4.92 3.199a1.417 1.417 0 0 0-1.4 1.4v10.8a1.417 1.417 0 0 0 1.4 1.4h10.16a1.417 1.417 0 0 0 1.4-1.4v-10.8a1.417 1.417 0 0 0-1.4-1.4Z">
        </path>
        <path data-name="路径 24223" d="M5.88 6.799a.6.6 0 1 0 .6-.6.6.6 0 0 0-.6.6Z"></path>
        <path data-name="路径 24224" d="M13.52 7.399H8.6a.6.6 0 1 1 0-1.2h4.92a.6.6 0 1 1 0 1.2Z"></path>
        <path data-name="路径 24225" d="M5.88 9.999a.6.6 0 1 0 .6-.6.6.6 0 0 0-.6.6Z"></path>
        <path data-name="路径 24226" d="M13.52 10.599H8.6a.6.6 0 1 1 0-1.2h4.92a.6.6 0 1 1 0 1.2Z"></path>
        <path data-name="路径 24227" d="M5.88 13.2a.6.6 0 1 0 .6-.6.6.6 0 0 0-.6.6Z"></path>
        <path data-name="路径 24228" d="M13.52 13.8H8.6a.6.6 0 1 1 0-1.2h4.92a.615.615 0 0 1 .6.6.589.589 0 0 1-.6.6Z">
        </path>
    </symbol>
    <symbol viewBox="0 0 20 20" id="local-icon-recharge_center">
        <g data-name="会员 (1)">
            <path data-name="路径 24391"
                d="M10.002 17.499a2.283 2.283 0 0 1-1.835-.9L2.52 9.441a2.322 2.322 0 0 1-.036-2.842l2.374-3.167a2.386 2.386 0 0 1 1.871-.935h6.547a2.334 2.334 0 0 1 1.871.935l2.374 3.165a2.322 2.322 0 0 1-.036 2.842l-5.647 7.16a2.283 2.283 0 0 1-1.836.9ZM6.728 3.542a1.261 1.261 0 0 0-1.007.5L3.347 7.207a1.3 1.3 0 0 0 .036 1.547l5.647 7.158a1.318 1.318 0 0 0 2.014 0l5.647-7.158a1.243 1.243 0 0 0 0-1.547l-2.338-3.165a1.261 1.261 0 0 0-1.007-.5Zm4.677 12.734Z">
            </path>
            <path data-name="路径 24392"
                d="M10.002 14.549a1.535 1.535 0 0 1-1.259-.612L5.578 9.908a.537.537 0 0 1 .827-.683l.036.036 3.2 4.029a.567.567 0 0 0 .791.072l.072-.072 3.094-4.065a.5.5 0 0 1 .755-.072.527.527 0 0 1 .072.755l-3.2 4.029a1.515 1.515 0 0 1-1.223.612Z">
            </path>
        </g>
    </symbol>
    <symbol class="icon" viewBox="0 0 1024 1024" id="local-icon-rect">
        <path
            d="M298.667 224a32 32 0 0 1 4.352 63.701l-4.352.299h-96v576h576V723.797a32 32 0 0 1 63.701-4.352l.299 4.352V896a32 32 0 0 1-32 32h-640a32 32 0 0 1-32-32V256a32 32 0 0 1 27.648-31.701l4.352-.299h128zm512 211.797a32 32 0 0 1 31.701 27.648l.299 4.352v128a32 32 0 0 1-63.702 4.31l-.298-4.31v-128a32 32 0 0 1 32-32zm0-297.13a32 32 0 0 1 32 32V224H896a32 32 0 0 1 31.701 27.648L928 256a32 32 0 0 1-32 32h-53.333v53.333a32 32 0 0 1-27.648 31.702l-4.352.298a32 32 0 0 1-32-32V288h-53.334a32 32 0 0 1-31.701-27.648l-.299-4.352a32 32 0 0 1 32-32h53.334v-53.333a32 32 0 0 1 27.648-31.702zm-256 85.333a32 32 0 0 1 4.352 63.701l-4.352.299h-128a32 32 0 0 1-4.352-63.701l4.352-.299h128z">
        </path>
    </symbol>
    <symbol viewBox="0 0 24 24" id="local-icon-skill">
        <path
            d="M9 11.249H4.25A2.241 2.241 0 0 1 2 8.999v-4.75a2.273 2.273 0 0 1 2.25-2.25H9a2.241 2.241 0 0 1 2.25 2.25v4.75A2.209 2.209 0 0 1 9 11.249Zm-4.75-7.75a.736.736 0 0 0-.75.75v4.75a.769.769 0 0 0 .75.75H9a.769.769 0 0 0 .75-.75v-4.75a.709.709 0 0 0-.75-.75ZM9 21.999H4.25A2.273 2.273 0 0 1 2 19.749v-4.75a2.241 2.241 0 0 1 2.25-2.25H9a2.241 2.241 0 0 1 2.25 2.25v4.75A2.241 2.241 0 0 1 9 21.999Zm-4.75-7.75a.769.769 0 0 0-.75.75v4.75a.736.736 0 0 0 .75.75H9a.769.769 0 0 0 .75-.75v-4.75a.769.769 0 0 0-.75-.75Zm17-9.3h-6.9a.75.75 0 0 1 0-1.5h6.9a.75.75 0 0 1 0 1.5Zm0 4.85h-6.9a.75.75 0 0 1 0-1.5h6.9a.75.75 0 0 1 0 1.5Zm0 5.9h-6.9a.75.75 0 0 1 0-1.5h6.9a.75.75 0 0 1 0 1.5Zm0 4.85h-6.9a.75.75 0 0 1 0-1.5h6.9a.769.769 0 0 1 .75.75.736.736 0 0 1-.75.75Z">
        </path>
    </symbol>
    <symbol class="icon" viewBox="0 0 1024 1024" id="local-icon-writings">
        <path
            d="M748.3 275.4S1039.1 32 937.4 32c-362.7 63.2-793 722.3-832 893.6-22.2 105 21.4 59.5 45.5 37.2 24.1-22.3 135.9-267.4 268.9-315.2C720.6 576.8 804.3 505.2 825.5 447c-86.9 25.4-128.7.5-184.8-7.7 48-16.3 233.8-15.6 273.2-220.8-65.7 69.3-165.6 56.9-165.6 56.9zM680.9 903H281.4c-24.5 0-44.5 20-44.5 44.5s20 44.5 44.5 44.5h399.5c24.5 0 44.5-20 44.5-44.5s-20-44.5-44.5-44.5z">
        </path>
    </symbol>
</svg>


<!-- vue -->
<script type="text/javascript" src="/app/ai/js/vue.global.js"></script>

<script>
    const App = {
        data() {
            return {
                roles: [],
                installedRoles: [],
                categories: [],
                hover: 0,
                category: '',
                keyword: '',
                display: 'all', // all installed notInstalled
                sort: 'hot', // hot new
                isMobile: false
            };
        },
        computed: {
            filteredRoles() {
                return this.roles.filter((item) => {
                    return (
                        item.status === 0 &&
                        (this.display === 'all' ||
                            (this.display === 'installed' && this.installed(item.roleId)) ||
                            (this.display === 'notInstalled' && !this.installed(item.roleId))) &&
                        (!this.category || (item.category && item.category.includes(this.category))) &&
                        (!this.keyword || (item.name.includes(this.keyword) || (item.desc && item.desc.includes(this.keyword))))
                    );
                }).sort((a, b) => {
                    return this.sort === 'hot' ? (b.installed || 0) - (a.installed || 0) : b.roleId - a.roleId;
                });
            }
        },
        mounted() {
            this.loadRoles();
            this.loadInstalledRoles();
            this.loadCategories();
            this.checkMobile();
        },
        methods: {
            loadRoles() {
                $.ajax({
                    url: '/app/ai/roles?type=all',
                    // 在成功获取数据后更新显示的总条数
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        this.roles = res.data;
                        // 更新显示的总条数
                        const totalCount = res.data.length;
                        const paginationText = document.querySelector('.el-pagination__total');
                        if (paginationText) {
                            paginationText.textContent = `共 ${totalCount} 条`;
                        }
                    }

                });
            },
            loadCategories() {
                $.ajax({
                    url: '/app/ai/setting/categories',
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        for (let category of res.data) {
                            this.categories.push(category.value);
                        }
                    }
                });
            },
            loadInstalledRoles() {
                const data = JSON.parse(localStorage.getItem('ai.data') || '{}');
                this.installedRoles = data.roleList || [];
            },
            install(roleId) {
                const role = this.roles.find((role) => role.roleId === roleId);
                if (role) {
                    window.parent.ai.saveRole(role);
                    this.loadInstalledRoles();
                    $.ajax({
                        url: '/app/ai/role/installed',
                        data: { roleId },
                        type: 'post'
                    });
                }
            },
            installAndSwitch(roleId) {
                this.install(roleId);
                window.parent.ai.switchRoleId(roleId);
                window.parent.ai.switchModule('chat');
            },
            uninstall(roleId) {
                window.parent.ai.deleteRole(roleId);
                this.loadInstalledRoles();
            },
            installed(roleId) {
                return this.installedRoles.find((role) => !role.deleted && role.roleId === roleId);
            },
            checkMobile() {
                this.isMobile = window.innerWidth <= 768; // 假设小于768px的宽度为移动端
            }
        }
    };
    Vue.createApp(App).mount('#app');

    $(document).click(function () {
        try {
            window.parent.ai.hideAll();
        } catch (e) { }
    });

    try {
        $(document.body).attr('data-bs-theme', window.parent.ai.theme);
        parent.ai.showIframe();
    } catch (e) { }
</script>


<script>
    const textContainer = document.querySelector('.header-2_text');
    const texts = [
        '我们正处于AI 的iPhone时刻！',
        '人工智能不是未来，而是现在。',
        '你知道嘛，如果文生图没有出图，可能是你文字中没有“图片”等关键字。'
    ];
    let currentIndex = 0;

    function playAnimation() {
        typeText(texts[currentIndex], () => {
            setTimeout(() => {
                deleteText(() => {
                    currentIndex = (currentIndex + 1) % texts.length;
                    setTimeout(playAnimation, 1000);
                });
            }, 2000);
        });
    }

    function typeText(text, callback) {
        let currentText = '';
        text.split('').forEach((char, i) => {
            setTimeout(() => {
                currentText += char;
                textContainer.textContent = currentText;
                if (i === text.length - 1) callback();
            }, i * 100);
        });
    }

    function deleteText(callback) {
        const text = textContainer.textContent;
        text.split('').forEach((_, i) => {
            setTimeout(() => {
                textContainer.textContent = text.slice(0, text.length - i - 1);
                if (i === text.length - 1) callback();
            }, i * 50); // 让删除速度快一点
        });
    }

    playAnimation();
</script>

</html>