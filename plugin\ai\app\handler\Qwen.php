<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

class Qwen extends Base
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = '通义千问';

    /**
     * @var string 模型类型
     */
    protected static $type = 'qwen';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'qwen-plus',
        'qwen',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'apikey' => [
            'name' => 'ApiKey',
            'type' => 'text',
            'value' => '',
            'desc' => 'API Key',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 0,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Qwen::class;

    /** 对话
     * @param $data
     * @param $options
     * @return void
     */
    public function completions($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->completions($data, $options);
    }

}