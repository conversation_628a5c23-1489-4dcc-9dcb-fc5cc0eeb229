
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>浏览页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body class="pear-container">
    
        <!-- 顶部查询表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form top-search-from">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">主键</label>
                        <div class="layui-input-block">
                            <input type="number" name="roleId" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">名称</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block">
                                <input type="hidden" autocomplete="off" name="name[]" value="like" class="layui-input inline-block">
                                <input type="text" autocomplete="off" name="name[]" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">简介</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block">
                                <input type="hidden" autocomplete="off" name="desc[]" value="like" class="layui-input inline-block">
                                <input type="text" autocomplete="off" name="desc[]" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">角色提示</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block">
                                <input type="hidden" autocomplete="off" name="rolePrompt[]" value="like" class="layui-input inline-block">
                                <input type="text" autocomplete="off" name="rolePrompt[]" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">问候语</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block">
                                <input type="hidden" autocomplete="off" name="greeting[]" value="like" class="layui-input inline-block">
                                <input type="text" autocomplete="off" name="greeting[]" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">模型</label>
                        <div class="layui-input-block">
                            <div name="model" id="model" value="" ></div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">预安装</label>
                        <div class="layui-input-block">
                            <div name="category" id="preinstalled" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">分类</label>
                        <div class="layui-input-block">
                            <div name="category" id="category" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-inline">
                        <label class="layui-form-label"></label>
                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="table-query">
                            <i class="layui-icon layui-icon-search"></i>查询
                        </button>
                        <button type="reset" class="pear-btn pear-btn-md" lay-submit lay-filter="table-reset">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                    <div class="toggle-btn">
                        <a class="layui-hide">展开<i class="layui-icon layui-icon-down"></i></a>
                        <a class="layui-hide">收起<i class="layui-icon layui-icon-up"></i></a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="data-table" lay-filter="data-table"></table>
            </div>
        </div>

        <!-- 表格顶部工具栏 -->
        <script type="text/html" id="table-toolbar">
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add" permission="app.ai.admin.airole.insert">
                <i class="layui-icon layui-icon-add-1"></i>新增
            </button>
            <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove" permission="app.ai.admin.airole.delete">
                <i class="layui-icon layui-icon-delete"></i>删除
            </button>
        </script>

        <!-- 表格行工具栏 -->
        <script type="text/html" id="table-bar">
            <button class="pear-btn pear-btn-xs tool-btn" lay-event="edit" permission="app.ai.admin.airole.update">编辑</button>
            <button class="pear-btn pear-btn-xs tool-btn" lay-event="remove" permission="app.ai.admin.airole.delete">删除</button>
        </script>

        <script src="/app/admin/component/layui/layui.js"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        <script src="/app/admin/admin/js/common.js"></script>
        
        <script>

            // 相关常量
            const PRIMARY_KEY = "roleId";
            const SELECT_API = "/app/ai/admin/ai-role/select";
            const UPDATE_API = "/app/ai/admin/ai-role/update";
            const DELETE_API = "/app/ai/admin/ai-role/delete";
            const INSERT_URL = "/app/ai/admin/ai-role/insert";
            const UPDATE_URL = "/app/ai/admin/ai-role/update";
            
            // 字段 模型 model
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.$.ajax({
                    url: "/app/ai/setting/models",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#model").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#model",
                            name: "model",
                            initValue: initValue,
                            data: res.data, 
                            model: {"icon":"hidden","label":{"type":"text"}},
                            clickClose: true,
                            radio: true,
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            // 字段 预安装 preinstalled
            layui.use(["form"], function() {
                layui.$("#preinstalled").attr("checked", layui.$('input[name="preinstalled"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(preinstalled)", function(data) {
                    layui.$('input[name="preinstalled"]').val(this.checked ? 1 : 0);
                });
            })

            // 字段 分类 preinstalled
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.xmSelect.render({
                    el: "#preinstalled",
                    name: "preinstalled",
                    initValue: "",
                    data: [{name: "是", value: 1}, {name: "否", value: 0}],
                    model: {"icon":"hidden","label":{"type":"text"}},
                    clickClose: true,
                    radio: true,
                });
            });
            
            // 字段 分类 category
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.$.ajax({
                    url: "/app/ai/setting/categories",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#category").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#category",
                            name: "category",
                            initValue: initValue,
                            data: res.data, 
                            toolbar: {"show":true,"list":["ALL","CLEAR","REVERSE"]},
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            // 表格渲染
            layui.use(["table", "form", "common", "popup", "util"], function() {
                let table = layui.table;
                let form = layui.form;
                let $ = layui.$;
                let common = layui.common;
                let util = layui.util;
                
				// 表头参数
				let cols = [
					{
						type: "checkbox",
						align: "center",
					},{
						title: "主键",align: "center",
						field: "roleId",
						sort: true,
                        width: 90
					},{
						title: "名称",align: "center",
						field: "name",
					},{
						title: "头像",align: "center",
						field: "avatar",
						templet: function (d) {
							return '<img src="'+encodeURI(d['avatar'])+'" style="max-width:32px;max-height:32px;" alt="" />'
						},
                        width: 90
					},{
						title: "简介",align: "center",
						field: "desc",
					},{
						title: "角色提示",align: "center",
						field: "rolePrompt",
					},{
						title: "问候语",align: "center",
						field: "greeting",
					},{
						title: "模型",align: "center",
						field: "model",
						templet: function (d) {
							let field = "model";
							if (typeof d[field] == "undefined") return "";
							let items = [];
							layui.each((d[field] + "").split(","), function (k , v) {
								items.push(apiResults[field][v] || v);
							});
							return util.escape(items.join(","));
						}
					},{
						title: "上下文数",align: "center",
						field: "contextNum",
                        hide: true,
					},{
						title: "最大tokens",align: "center",
						field: "maxTokens",
                        hide: true,
					},{
						title: "温度",align: "center",
						field: "temperature",
                        hide: true,
					},{
						title: "预安装",align: "center",
						field: "preinstalled",
						templet: function (d) {
							let field = "preinstalled";
							form.on("switch("+field+")", function (data) {
								let load = layer.load();
								let postData = {};
								postData[field] = data.elem.checked ? 1 : 0;
								postData[PRIMARY_KEY] = this.value;
								$.post(UPDATE_API, postData, function (res) {
									layer.close(load);
									if (res.code) {
				                        return layui.popup.failure(res.msg, function () {
				                            data.elem.checked = !data.elem.checked;
				                            form.render();
				                        });
				                    }
									return layui.popup.success("操作成功");
								})
							});
							let checked = d[field] === 1 ? "checked" : "";
							return '<input type="checkbox" value="'+util.escape(d[PRIMARY_KEY])+'" lay-filter="'+util.escape(field)+'" lay-skin="switch" lay-text="'+util.escape('')+'" '+checked+'/>';
						}
					},{
						title: "安装量",align: "center",
						field: "installed",
                        sort: true,
					},{
						title: "分类",align: "center",
						field: "category",
						templet: function (d) {
							let field = "category";
							if (typeof d[field] == "undefined") return "";
							let items = [];
							layui.each((d[field] + "").split(","), function (k , v) {
								items.push(apiResults[field][v] || v);
							});
							return util.escape(items.join(","));
						}
					},{
                        title: "语言",align: "center",
                        field: "language",
                        hide: true,
                    },{
                        title: "朗读者",align: "center",
                        field: "speaker",
                        hide: true,
                    },{
                        title: "训练集",align: "center",
                        field: "dataset",
                        hide: true,
                        templet: function (d) {
                            let field = "dataset";
                            if (typeof d[field] == "undefined") return "";
                            let items = [];
                            layui.each((d[field] + "").split(","), function (k , v) {
                                items.push(apiResults[field][v] || v);
                            });
                            return util.escape(items.join(","));
                        }
                    },{
                        title: "停用",align: "center",
                        field: "status",
                        templet: function (d) {
                            let field = "status";
                            form.on("switch("+field+")", function (data) {
                                let load = layer.load();
                                let postData = {};
                                postData[field] = data.elem.checked ? 1 : 0;
                                postData[PRIMARY_KEY] = this.value;
                                $.post(UPDATE_API, postData, function (res) {
                                    layer.close(load);
                                    if (res.code) {
                                        return layui.popup.failure(res.msg, function () {
                                            data.elem.checked = !data.elem.checked;
                                            form.render();
                                        });
                                    }
                                    return layui.popup.success("操作成功");
                                })
                            });
                            let checked = d[field] === 1 ? "checked" : "";
                            return '<input type="checkbox" value="'+util.escape(d[PRIMARY_KEY])+'" lay-filter="'+util.escape(field)+'" lay-skin="switch" lay-text="'+util.escape('')+'" '+checked+'/>';
                        }
                    },{
						title: "操作",
						toolbar: "#table-bar",
						align: "center",
						fixed: "right",
						width: 120,
					}
				];
				
				// 渲染表格
				function render()
				{
				    table.render({
				        elem: "#data-table",
				        url: SELECT_API,
				        page: true,
				        cols: [cols],
				        skin: "line",
				        size: "lg",
				        toolbar: "#table-toolbar",
				        autoSort: false,
				        defaultToolbar: [{
				            title: "刷新",
				            layEvent: "refresh",
				            icon: "layui-icon-refresh",
				        }, "filter", "print", "exports"],
				        done: function () {
				            layer.photos({photos: 'div[lay-id="data-table"]', anim: 5});
				        }
				    });
				}
				
				// 获取表格中下拉或树形组件数据
				let apis = [];
				apis.push(["model", "/app/ai/setting/models"]);
				apis.push(["category", "/app/ai/setting/categories"]);
				apis.push(["dataset", "/app/ai/admin/ai-dataset/list"]);
				let apiResults = {};
				apiResults["model"] = [];
				apiResults["category"] = [];
				apiResults["dataset"] = [];
				let count = apis.length;
				layui.each(apis, function (k, item) {
				    let [field, url] = item;
				    $.ajax({
				        url: url,
				        dateType: "json",
				        success: function (res) {
				            if (res.code) {
				                return layui.popup.failure(res.msg);
				            }
				            function travel(items) {
				                for (let k in items) {
				                    let item = items[k];
				                    apiResults[field][item.value] = item.name;
				                    if (item.children) {
				                        travel(item.children);
				                    }
				                }
				            }
				            travel(res.data);
				        },
				        complete: function () {
				            if (--count === 0) {
				                render();
				            }
				        }
				    });
				});
				if (!count) {
				    render();
				}
				
                // 编辑或删除行事件
                table.on("tool(data-table)", function(obj) {
                    if (obj.event === "remove") {
                        remove(obj);
                    } else if (obj.event === "edit") {
                        edit(obj);
                    }
                });

                // 表格顶部工具栏事件
                table.on("toolbar(data-table)", function(obj) {
                    if (obj.event === "add") {
                        add();
                    } else if (obj.event === "refresh") {
                        refreshTable();
                    } else if (obj.event === "batchRemove") {
                        batchRemove(obj);
                    }
                });

                // 表格顶部搜索事件
                form.on("submit(table-query)", function(data) {
                    table.reload("data-table", {
                        page: {
                            curr: 1
                        },
                        where: data.field
                    })
                    return false;
                });
                
                // 表格顶部搜索重置事件
                form.on("submit(table-reset)", function(data) {
                    table.reload("data-table", {
                        where: []
                    })
                });
                
                // 字段允许为空
                form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });

                // 表格排序事件
                table.on("sort(data-table)", function(obj){
                    table.reload("data-table", {
                        initSort: obj,
                        scrollPos: "fixed",
                        where: {
                            field: obj.field,
                            order: obj.type
                        }
                    });
                });

                // 表格新增数据
                let add = function() {
                    layer.open({
                        type: 2,
                        title: "新增",
                        shade: 0.1,
                        shadeClose:true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: INSERT_URL
                    });
                }

                // 表格编辑数据
                let edit = function(obj) {
                    let value = obj.data[PRIMARY_KEY];
                    layer.open({
                        type: 2,
                        title: "修改",
                        shade: 0.1,
                        shadeClose:true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: UPDATE_URL + "?" + PRIMARY_KEY + "=" + value
                    });
                }

                // 删除一行
                let remove = function(obj) {
                    return doRemove(obj.data[PRIMARY_KEY]);
                }

                // 删除多行
                let batchRemove = function(obj) {
                    let checkIds = common.checkField(obj, PRIMARY_KEY);
                    if (checkIds === "") {
                        layui.popup.warning("未选中数据");
                        return false;
                    }
                    doRemove(checkIds.split(","));
                }

                // 执行删除
                let doRemove = function (ids) {
                    let data = {};
                    data[PRIMARY_KEY] = ids;
                    layer.confirm("确定删除?", {
                        icon: 3,
                        title: "提示"
                    }, function(index) {
                        layer.close(index);
                        let loading = layer.load();
                        $.ajax({
                            url: DELETE_API,
                            data: data,
                            dataType: "json",
                            type: "post",
                            success: function(res) {
                                layer.close(loading);
                                if (res.code) {
                                    return layui.popup.failure(res.msg);
                                }
                                return layui.popup.success("操作成功", refreshTable);
                            }
                        })
                    });
                }

                // 刷新表格数据
                window.refreshTable = function(param) {
                    table.reloadData("data-table", {
                        scrollPos: "fixed"
                    });
                }
            })

        </script>
    </body>
</html>
