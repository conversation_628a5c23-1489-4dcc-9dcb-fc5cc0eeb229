.balance[data-v-abfe6c82] {
    background-color: #f9fafc;
    border: 1px solid #feffff;
    border-radius: 8px;
    font-size: 12px
}

.recharge-btn[data-v-abfe6c82] {
    background: linear-gradient(89.14deg, #ffcb58, #f7630e);
    border-radius: 8px;
    color: var(--color-white);
    cursor: pointer;
    font-size: var(--el-font-size-small);
    padding: 3px 10px
}

.layout-header[data-v-e4a392c0] {
    height: var(--header-height);
    position: sticky;
    top: 0;
    width: 100%;
    z-index: 1999
}

.layout-header .header-contain[data-v-e4a392c0] {
    align-items: center;
    display: flex;
    height: 100%;
    margin: 0 auto
}

.layout-header .header-contain .navbar[data-v-e4a392c0] {
    --el-menu-item-font-size: var(--el-font-size-large);
    --el-menu-bg-color: var(--el-color-primary);
    --el-menu-active-color: var(--color-white);
    --el-menu-text-color: var(--color-white);
    --el-menu-item-hover-fill: var(--el-color-primary);
    --el-menu-hover-text-color: var(--color-white);
    --el-menu-hover-bg-color: var(--el-color-primary)
}

/* 侧边栏导航样式 */
.layout-sidebar.dark .nav .nav-item {
    align-items: center;
    border-radius: 8px;
    display: flex;
    height: 40px;
    line-height: 40px;
    margin-bottom: 8px;
    padding: 0 12px;
  }
  
  /* 活跃状态的导航项样式 */
  .layout-sidebar.dark .nav .nav-item.active {
    background: linear-gradient(90deg, var(--gradient-1) 0, var(--gradient-2) 100%);
    color: var(--color-btn-text, #fff);
  }
  
  /* 深色主题下的活跃导航项样式 */
  .layout-sidebar.dark .nav .nav-item.active[data-v-d9bd79d9] {
    background: #2b2d31;
    color: var(--aside-color);
  }
  
  .layout-sidebar.dark .menu-item--active[menu_down] {
    background-color: #2b2d31;
    color: var(--aside-color);
  }
  
  .menu-item[menu_down] {
    align-items: center;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    font-size: var(--el-font-size-small);
    height: 38px;
    line-height: 38px;
    margin: 3px 0;
    padding: 0 12px;
  }
  
  .menu-item--active[menu_down] {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }
  

.menu-item[data-v-fffb03b2] {
    align-items: center;
    display: flex
}

.layout-sidebar.dark .change-theme[data-v-dc8a32f3] {
    color: var(--color-white)
}

.layout-sidebar[index_side_1] {
    background-color: var(--aside-bg-color);
    border-radius: 0 16px 16px 0;
    color: var(--aside-color);
    height: 100%;
    width: var(--sidebar-width)
}

.layout-sidebar[index_side_1] .el-scrollbar__view {
    height: 100%
}

.layout-aside[index_side] {
    padding-bottom: env(safe-area-inset-bottom)
}

.layout-aside[index_side] .el-drawer__body {
    padding: 0
}

.layout-aside .panel[index_side] {
    padding-left: 12px
}