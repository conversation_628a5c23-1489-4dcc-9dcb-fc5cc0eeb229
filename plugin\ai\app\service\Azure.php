<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */


namespace plugin\ai\app\service;

use plugin\admin\app\model\Option;

class Azure extends Base
{
    /**
     * options表对应的name字段
     */
    const OPTION_NAME = 'plugin_ai.azure-setting';

    /**获取配置
     *
     * @return array|mixed
     */
    public static function getSetting($name = '', $default = null)
    {
        $setting = Option::where('name', static::OPTION_NAME)->value('value');
        $setting = $setting ? json_decode($setting, true) : [];
        if (!$setting) {
            $setting = [
                'enable' => false,
                'api_host' => '',
                'map' => json_encode([
                    'gpt-4' => 'gpt-4',
                    'gpt-3.5-turbo' => 'gpt-35-turbo',
                    'gpt-4-vision-preview' => 'gpt-4-vision',
                ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT),
            ];
            static::saveSetting($setting);
        }
        return $name ? $setting[$name] ?? $default : $setting;
    }
}