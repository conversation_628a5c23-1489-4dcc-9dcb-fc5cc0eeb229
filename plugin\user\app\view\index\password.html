<?=plugin\user\api\Template::header('密码设置')?>

<?=plugin\user\api\Template::nav()?>

<div class="container">

    <div class="row">

        <?=plugin\user\api\Template::sidebar()?>

        <div class="col-md-9 col-12 pt-4">

            <div class="mb-4 card bg-white border-0 shadow-sm" style="min-height:80vh">
                <div class="card-body">
                    <h5 class="pb-3 mb-4 border-bottom">密码设置</h5>
                    <form class="col-12 col-md-8 col-lg-6">
                        <div class="form-group">
                            <input type="password" name="password" class="form-control" placeholder="原始密码" autocomplete="off" required>
                        </div>
                        <div class="form-group">
                            <input type="password" name="new_password" class="form-control" placeholder="新密码" autocomplete="off" required>
                        </div>
                        <div class="form-group">
                            <input type="password" name="new_password_confirm" class="form-control" placeholder="确认新密码" autocomplete="off" required>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-block">修改密码</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
    $('input').keyup(function () {
        $(this).removeClass('is-invalid');
    });

    $('form').submit(function(event) {
        event.preventDefault();
        if ($('input[name="new_password"]').val() !== $('input[name="new_password_confirm"]').val()) {
            return webman.error('两次输入的密码不一致');
        }
        $.ajax({
            url: "/app/user/password",
            type: "POST",
            dataType: 'json',
            data: $(this).serialize(),
            success: function (e) {
                if (e.code !== 0) {
                    let field = e.data ? e.data.field : false;
                    field && $('input[name="'+field+'"]').addClass('is-invalid');
                    return webman.error(e.msg);
                }
                webman.success('操作成功', function () {
                    location.href = '/app/user';
                });
            }
        });
    });
</script>

<?=plugin\user\api\Template::footer()?>
