<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>分布表单</title>
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
	</head>
	<body class="pear-container">
		<div class="layui-row layui-col-space10">
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">开发环境</div>
					<div class="layui-card-body">Step 步骤表单</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								&lt;link rel="stylesheet" href="component/pear/css/pear.css" /&gt;
								 并
								&lt;script src="component/layui/layui.js"&gt;&lt;/script&gt;
								 并
								&lt;script src="component/pear/pear.js"&gt;&lt;/script&gt;
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card">
						<div class="layui-card-body" style="padding-top: 40px;">
							<div class="layui-carousel" id="stepForm" lay-filter="stepForm" style="margin: 0 auto;">
								<div carousel-item>
									<div>
										<form class="layui-form" action="javascript:void(0);" style="margin: 0 auto;max-width: 460px;padding-top: 40px;">
											<div class="layui-form-item">
												<label class="layui-form-label">游戏编号:</label>
												<div class="layui-input-block">
													<input type="text" placeholder="请填写入款人游戏ID" class="layui-input" lay-verify="number" required />
				
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">入款金额:</label>
												<div class="layui-input-block">
													<input type="number" placeholder="请填写入款金额" value="" class="layui-input" lay-verify="number" required>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">入款类型:</label>
												<div class="layui-input-block">
													<select lay-verify="required">
														<option value="1" selected>保险箱</option>
														<option value="2">现金</option>
													</select>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">入款方式:</label>
												<div class="layui-input-block">
													<select lay-verify="required">
														<option value="1" selected>人工入款</option>
														<option value="2">修正</option>
														<option value="3">活动</option>
														<option value="4">佣金</option>
													</select>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">备注说明:</label>
												<div class="layui-input-block">
													<textarea placeholder="入款备注" value="" class="layui-textarea"></textarea>
												</div>
											</div>
											<div class="layui-form-item">
												<div class="layui-input-block">
													<button class="pear-btn pear-btn-success" lay-submit lay-filter="formStep">
														&emsp;下一步&emsp;
													</button>
												</div>
											</div>
										</form>
									</div>
									<div>
										<form class="layui-form"  action="javascript:void(0);" style="margin: 0 auto;max-width: 460px;padding-top: 40px;">
											<div class="layui-form-item">
												<label class="layui-form-label">游戏编号:</label>
												<div class="layui-input-block">
													<div class="layui-form-mid layui-word-aux">639537</div>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">账户余额:</label>
												<div class="layui-input-block">
													<div class="layui-form-mid layui-word-aux">3000 元（保险箱：1000，现金：2000）</div>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">入款金额:</label>
												<div class="layui-input-block">
													<div class="layui-form-mid layui-word-aux">
														<span style="font-size: 18px;color: #333;">1800 元</span>
													</div>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">入款类型:</label>
												<div class="layui-input-block">
													<div class="layui-form-mid layui-word-aux">保险箱</div>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">入款方式:</label>
												<div class="layui-input-block">
													<div class="layui-form-mid layui-word-aux">人工入款</div>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">备注说明:</label>
												<div class="layui-input-block">
													<div class="layui-form-mid layui-word-aux">备注说明</div>
												</div>
											</div>
											<div class="layui-form-item">
												<div class="layui-input-block">
													<button type="button" class="pear-btn pear-btn-success pre">上一步</button>
													<button class="pear-btn pear-btn-success" lay-submit lay-filter="formStep2">
														&emsp;确认入款&emsp;
													</button>
												</div>
											</div>
										</form>
									</div>
									<div>
										<div style="text-align: center;margin-top: 90px;">
											<i class="layui-icon layui-circle" style="color: white;font-size:30px;font-weight:bold;background: #52C41A;padding: 20px;line-height: 80px;">&#xe605;</i>
											<div style="font-size: 24px;color: #333;font-weight: 500;margin-top: 30px;">
												入款成功
											</div>
											<div style="font-size: 14px;color: #666;margin-top: 20px;">预计两小时到账</div>
										</div>
										<div style="text-align: center;margin-top: 50px;">
											<button class="pear-btn pear-btn-success next">再入一笔</button>
											<button class="pear-btn pear-btn-success">查看账单</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
		                        layui.use(['form', 'step','element'], function() {
		                            var $ = layui.$,
		                            form = layui.form,
		                            step = layui.step;
		                            
		                            step.render({
		                                elem: '#stepForm',
		                                filter: 'stepForm',
		                                width: '100%',
		                                stepWidth: '600px',
		                                height: '500px',
		                                stepItems: [{
		                                    title: '填写'
		                                }, {
		                                    title: '确认'
		                                }, {
		                                    title: '完成'
		                                }]
		                            });
		                         
		                            form.on('submit(formStep)', function(data) {
		                                step.next('#stepForm');
		                                return false;
		                            });
		                         
		                            form.on('submit(formStep2)', function(data) {
		                                step.next('#stepForm');
		                                return false;
		                            });
		                         
		                            $('.pre').click(function() {
		                                step.pre('#stepForm');
		                                return false;
		                            });
		                         
		                            $('.next').click(function() {
		                                step.next('#stepForm');
		                                return false;
		                            });
		                        })
							</pre>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
		<script>
			layui.use(['form', 'step','code','element'], function() {
				var $ = layui.$,
					form = layui.form,
					step = layui.step;
					
				layui.code();

				step.render({
					elem: '#stepForm',
					filter: 'stepForm',
					width: '100%',
					stepWidth: '600px',
					height: '500px',
					stepItems: [{
						title: '填写'
					}, {
						title: '确认'
					}, {
						title: '完成'
					}]
				});

				form.on('submit(formStep)', function(data) {
					step.next('#stepForm');
					return false;
				});

				form.on('submit(formStep2)', function(data) {
					step.next('#stepForm');
					return false;
				});

				$('.pre').click(function() {
					step.pre('#stepForm');
					return false;
				});

				$('.next').click(function() {
					step.next('#stepForm');
				    return false;
				});
			})
		</script>
	</body>
</html>
