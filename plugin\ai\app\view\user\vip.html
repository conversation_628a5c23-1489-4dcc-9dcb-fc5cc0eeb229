<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/app/ai/css/bootstrap.min.css?v=5.3" rel="stylesheet">
    <link href="/app/ai/css/app.css?v=<?=ai_css_version()?>" rel="stylesheet">
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>
    <title>webman AI助手充值</title>
</head>

<body class="rounded-end" data-bs-theme="light">

<div class="header">会员充值</div>
<div class="container overflow-scroll" style="height: calc(100% - 45px)">

    <div class="row">

        <div class="col-12 pt-4" id="app">

            <div class="mb-4 card border-0 shadow-sm" style="min-height:70vh;">
                <div class="card-body p-lg-5">

                    <h5 class="pb-2">AI助手会员充值</h5>

                    <div class="row" v-cloak>
                        <div class="col-6 col-sm-6 col-md-4 d-flex" v-for="(item, index) in setting.plans">
                            <div class="p-0 plan-box mb-5 black-bg" :class="{'plan-selected':plan==index}" @click="plan=index">
                                <div class="d-flex" v-if="item.months">
                                    <div class="text-center text-white plan-tip">有效期{{item.months}}个月</div>
                                </div>
                                <div class="d-flex" v-else>
                                    <div class="text-center text-white plan-tip">有效期{{item.days}}天</div>
                                </div>

                                <div class="d-flex align-items-center justify-content-center my-2 mx-2 mx-md-3 my-lg-4 mx-lg-4">
                                    <div class="text-center">
                                        <div class="f1 d-flex align-items-center">
                                            <span class="d-flex align-items-baseline"><b>￥</b><b class="f2" v-html="item.price"></b></span>/{{item.name}}
                                        </div>
                                        <del class="text-gray">原价 ￥{{item.price*2}}</del>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row" v-cloak>
                        <div class="col-6 col-md-4 d-flex" v-if="setting.enabledWechat">
                            <div class="p-0 plan-box mb-5" :class="{'type-selected':paymentMethod=='wechat'}" @click="paymentMethod='wechat'">
                                <div class="d-flex align-items-center justify-content-center my-2 mx-3 my-md-3 mx-md-4">
                                    <div class="text-center">
                                        <div class="f1 d-flex align-items-center icon">
                                            <span class="f2 me-2 text-success">&#xe69d;</span> 微信支付
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-md-4 d-flex" v-if="setting.enabledAlipay">
                            <div class="p-0 plan-box mb-5" :class="{'type-selected':paymentMethod=='alipay'}" @click="paymentMethod='alipay'">
                                <div class="d-flex align-items-center justify-content-center  my-2 mx-3 my-md-3 mx-md-4">
                                    <div class="text-center">
                                        <div class="f1 d-flex align-items-center icon">
                                            <span class="f2 me-2 text-primary">&#xe67e;</span>支付宝支付
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <template v-for="(value, name) in setting.plans[plan]">
                            <div v-if="setting.enabledModelTypes[name]" class="col-12 col-md-6">
                                <div class="alert alert-success cursor-default border-0 dark-bg">
                                    &#xe9da; {{setting.enabledModelTypes[name]}} {{(<?=config('plugin.ai.env.unlimited', 0)?>&&value/setting.plans[plan]['months'])>=3000 ? '无限' : value}} 次
                                </div>
                            </div>
                        </template>
                    </div>

                    <div class="mt-4 float-right">
                        <input type="hidden" name="plan" v-model="plan">
                        <input type="hidden" name="redirect">
                        <button class="btn px-5 btn-primary" @click="pay">确认支付</button>
                    </div>

                </div>
            </div>

            <div class="modal fade" id="myModal">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">

                        <!-- Modal 头部 -->
                        <div class="modal-header">
                            <h4 class="modal-title">AI会员充值</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>

                        <!-- Modal 主体 -->
                        <div class="modal-body d-flex justify-content-center align-items-center">
                            <div v-if="!isMobile&&!success&&!waitTimeout&&orderId&&paymentMethod=='alipay'" style="border:10px solid #fff;height:223px;" class="bg-white">
                                <iframe :src="'/app/ai/order/alipay-qr?orderId='+orderId" width="205" height="205" style="border:none"></iframe>
                            </div>
                            <div v-if="!isMobile&&!success&&!waitTimeout&&orderId&&paymentMethod=='wechat'" class="bg-white" style="height:220px;">
                            <iframe :src="'/app/ai/order/wechat-qr?orderId='+orderId" width="220" height="220" style="border:none"></iframe>
                            </div>
                            <div v-if="waitTimeout||success" class="border d-flex justify-content-center align-items-center" style="height:200px;width:200px">
                                <span v-show="waitTimeout" class="text-warning">二维码已过期</span>
                                <span v-show="success" class="text-success">支付成功</span>
                            </div>
                        </div>

                        <!-- Modal 底部 -->
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="modal">完成</button>
                        </div>

                    </div>
                </div>
            </div>

        </div>

    </div>
</div>


<!-- vue -->
<script type="text/javascript" src="/app/ai/js/vue.global.js"></script>

<script>
    const App = {
        data() {
            return {
                plan: 1,
                orderId: '',
                paymentMethod: "alipay",
                waitTimeout: false,
                statusCount: 0,
                success: false,
                orderCreateTime: 0,
                setting: {
                    enabledAlipay: false,
                    enabledWechat: false,
                    enabledModelTypes: {},
                    plans: {}
                },
                isMobile: false,
            }
        },
        mounted() {
            $('#myModal').on('hide.bs.modal', (e) => {
                this.done();
            })
            this.loadSetting();
            this.isMobile = screen.width < 768;
        },
        methods: {
            loadSetting() {
                $.ajax({
                    url: "/app/ai/setting",
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        this.setting = res.data;
                        this.paymentMethod = this.setting.enabledWechat ? "wechat" : "alipay";
                    }
                });
            },
            pay() {
                this.waitTimeout = false;
                this.success = false;
                this.clearTimer();
                $.ajax({
                    url: "/app/ai/order/create",
                    data: {plan: this.plan, paymentMethod: this.paymentMethod},
                    type: "post",
                    success: (res) => {
                        if (res.code) {
                            alert(res.msg);
                            return;
                        }
                        this.orderId = res.data.orderId;
                        this.orderCreateTime = new Date().getTime();
                        if (this.isMobile) {
                            if (navigator.userAgent.toLowerCase().match(/MicroMessenger/i) == 'micromessenger') {
                                window.parent.location.href = "/app/ai/order/jsapi-pay?orderId=" + this.orderId;
                                return;
                            }
                            window.parent.location.href = "/app/ai/order/h5-pay?orderId=" + this.orderId;
                            return
                        }
                        $('#myModal').modal('show');
                        this.checkOrderStatus();
                    }
                });
            },
            checkOrderStatus(once) {
                $.ajax({
                    url: "/app/ai/order/status",
                    data: {orderId: this.orderId},
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        if (res.data.status === 'paid') {
                            this.success = true;
                            setTimeout(() => {
                                window.parent.ai.switchModule('me');
                            }, 2000);
                            return;
                        }
                        if (!once) {
                            this.timer = setTimeout(()=> {
                                this.checkOrderStatus();
                            }, 3000);
                        }
                    },
                    complete: () => {
                        // 二维码10分钟后过期
                        if(this.orderCreateTime && new Date().getTime() - this.orderCreateTime > 10*60*1000) {
                            this.clearTimer();
                            this.waitTimeout = true;
                        }
                    }
                });
            },
            getOrderStatus() {
                $.ajax({
                    url: "/app/ai/order/get-status",
                    data: {orderId: this.orderId},
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        if (res.data.status === 'paid') {
                            this.success = true;
                            setTimeout(() => {
                                window.parent.ai.switchModule('me');
                            }, 1000);
                        }
                    }
                });
            },
            clearTimer() {
                if (this.timer) {
                    clearTimeout(this.timer);
                    this.timer = 0;
                }
                this.statusCount = 0;
                this.orderCreateTime = 0;
            },
            done() {
                this.clearTimer();
                if (!this.orderId) {
                    return;
                }
                this.getOrderStatus();
            }
        }
    }
    Vue.createApp(App).mount('#app');

    $(document).click(function () {
        try {window.parent.ai.hideAll();} catch (e) {}
    });
    try {
        $(document.body).attr("data-bs-theme", window.parent.ai.theme);
        parent.ai.showIframe();
    } catch (e) {}
</script>

<style>
    [data-bs-theme=light]{
        --bs-form-control-bg: var(--ai-light-bg)
    }
    .f1 {
        font-size: 18px;
    }
    .f2 {
        font-size: 30px;
    }
    .text-gray {
        color: #aaa !important;
    }
    .plan-box {
        border-radius:.5rem;
        cursor:pointer;
        background: var(--bs-form-control-bg);
        border: 1px solid var(--bs-border-color);
    }
    .plan-selected, .type-selected {
        box-shadow: inset 0 0 0 1px var(--bs-primary) !important;
    }
    .plan-tip {
        background-color:red;border-top-left-radius:.45rem;border-bottom-right-radius:.45rem;padding:.2rem .35rem;font-size:12px;
    }
    .alert-success {
        font-family: iconfont;
        background: var(--bs-form-control-bg);
    }
    [v-cloak] {
        display: none;
    }
    /* 手机适配 */
    @media (max-width: 768px) {
        .f1 {
            font-size: 16px;
        }
        .f2 {
            font-size: 22px;
        }
    }
</style>

</body>
</html>
