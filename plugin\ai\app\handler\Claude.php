<?php

declare(strict_types=1);

namespace plugin\ai\app\handler;


class Claude extends Base
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = '<PERSON>';

    /**
     * @var string 模型类型
     */
    protected static $type = 'claude';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'claude-3-opus-20240229',
        'claude-3-sonnet-20240229',
        'claude-3-haiku-20240307',
        'claude-2.1',
        'claude-2.0',
        'claude-instant-1.2'
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'api' => [
            'name' => 'API',
            'type' => 'text',
            'value' => 'https://api.anthropic.com',
            'desc' => 'API 地址',
        ],
        'apikey' => [
            'name' => 'ApiKey',
            'type' => 'text',
            'value' => '',
        ],
        'version' => [
            'name' => 'Version',
            'type' => 'text',
            'value' => 3.0,
            'desc' => 'claude-2.0',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 0,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Claude::class;

    /**
     * 对话
     * @param $data
     * @param $options
     * @return void
     */
    public function completions($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->completions($data, $options);
    }
}