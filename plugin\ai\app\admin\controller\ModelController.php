<?php

namespace plugin\ai\app\admin\controller;

use plugin\ai\app\model\AiModel;
use plugin\ai\app\service\Common;
use plugin\ai\app\service\Model;
use support\Request;
use support\Response;

/**
 * 模型配置
 */
class ModelController extends SettingBase
{
    /**
     * 服务名
     * @var string
     */
    protected $service = Model::class;

    /**
     * 更新设置
     * @param Request $request
     * @return Response
     */
    public function update(Request $request): Response
    {
        $plans = current($request->post());
        $json = json_decode($plans, true);
        if (!$json) {
            return $this->json(1, 'json格式错误');
        }
        Model::saveSetting($json);
        $models = Common::getModels();
        foreach (array_keys($json) as $model) {
            if (!in_array($model, $models)) {
                $this->saveModel($model);
            }
        }
        return $this->json(0);
    }

    /**
     * @param $model
     * @return bool
     */
    protected function saveModel($model): bool
    {
        $modelType = Common::getModelType($model);
        $item = AiModel::where('type', $modelType)->first();
        if (!$item) {
            return false;
        }
        $models = $item->models;
        $models[] = $model;
        $item->models = implode("\n", array_unique($models));
        $item->save();
        return true;
    }

}
