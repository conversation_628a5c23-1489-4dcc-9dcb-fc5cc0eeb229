<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

class Kimi extends Base
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = 'Kimi';

    /**
     * @var string 模型类型
     */
    protected static $type = 'kimi';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'moonshot-v1-8k',
        'moonshot-v1-32k',
        'moonshot-v1-128k',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'apikey' => [
            'name' => 'ApiKey',
            'type' => 'text',
            'value' => '',
        ],
        'allowNetwork' => [
            'name' => '允许联网',
            'type' => 'checkbox',
            'value' => false,
        ],
        'networkModel' => [
            'name' => '联网模型',
            'type' => 'text',
            'value' => 'ernie-bot',
            'desc' => '需要联网时使用的模型，例如ernie-bot',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 0,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Kimi::class;

    /**
     * 对话
     * @param $data
     * @param $options
     * @return void
     */
    public function completions($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->completions($data, $options);
    }
}