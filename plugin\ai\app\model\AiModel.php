<?php

namespace plugin\ai\app\model;

use plugin\admin\app\model\Base;

/**
 * @property integer $id 主键(主键)
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property string $name 名称
 * @property string $type 类型
 * @property string $handler 模型处理器
 * @property string $models 支持的模型
 * @property integer $priority 优先级
 * @property string $settings 设置
 * @property integer $status 禁用
 */
class AiModel extends Base
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ai_models';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * @param $value
     * @return mixed
     */
    public function getModelsAttribute($value)
    {
        if (is_array($value)) {
            return $value;
        }
        return $value ? explode("\n", $value) : [];
    }

    /**
     * @param $value
     * @return mixed
     */
    public function getSettingsAttribute($value)
    {
        if (is_array($value)) {
            return $value;
        }
        return $value ? json_decode($value, true) : [];
    }

    /**
     * @return void
     */
    public static function init()
    {
        // 遍历 handler下的所有文件，获取类名，并执行init静态方法
        $handlerPath = base_path('/plugin/ai/app/handler');
        $handlerFiles = array_diff(scandir($handlerPath), ['.', '..', 'Base.php', 'Gpt.php']);
        foreach ($handlerFiles as $file) {
            $class = 'plugin\\ai\\app\\handler\\' . pathinfo($file, PATHINFO_FILENAME);
            if (class_exists($class)) {
                $class::init();
            }
        }
    }
    
}
