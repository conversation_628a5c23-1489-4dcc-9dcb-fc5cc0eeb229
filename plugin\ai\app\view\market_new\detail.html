<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模仿页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow: hidden; /* 禁止滚动条 */
        }
        .container {
            display: flex;
            flex: 1;
            padding: 20px;
            overflow: hidden; /* 禁止滚动条 */
        }
        .sidebar-container {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 250px;
            background-color: #fff;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            margin-right: 20px;
            overflow-y: auto;
        }
        .main-content-container {
            flex: 1;
            background-color: #fff;
            padding: 20px;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between; /* 使内容与输入框之间有空间 */
        }
        .new-chat, .clear-chat {
            background: white;
            color: black;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }
        .new-chat {
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            color: white;
        }
        .main-content {
            text-align: center;
            width: 100%;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .title {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .sections {
            display: flex;
            justify-content: center;
            gap: 20px;
            width: 100%;
            max-width: 1000px;
            margin: 0 auto; /* 居中对齐 */
        }
        .section {
            text-align: center;
            padding: 20px;
            width: 30%;
        }
        .section img {
            width: 50px;
            margin-bottom: 10px;
        }
        .section div {
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
        }
        .section button {
            background-color: #fff;
            color: black;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 10px;
            cursor: pointer;
            width: 100%;
            text-align: left;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .footer {
            background-color: #fff;
            padding: 10px 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 5px;
            width: calc(100% - 40px); /* 保持与 main-content-container 的 padding 一致 */
            box-sizing: border-box;
            margin-top: 20px;
        }
        .footer input {
            width: 70%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        .footer button {
            background-color: #10b981;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .footer .clear-chat-footer {
            background-color: #fff;
            color: black;
            padding: 10px 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar-container">
            <div class="new-chat">+ 新建会话</div>
            <div class="clear-chat">清除所有会话</div>
        </div>
        <div class="main-content-container">
            <div class="main-content">
                <div class="title">核桃AI</div>    
                <div class="sections">
                    <div class="section">
                        <img src="/app/ai/avatar/detail_xiezuo.png" alt="写作">
                        <div>写作</div>
                        <button>写一篇公众号文章模板</button>
                        <button>公司活动，怎么写一篇通用的策划案</button>
                        <button>怎么提高写作的水平，有什么技巧</button>
                    </div>
                    <div class="section">
                        <img src="/app/ai/avatar/detail_baike.png" alt="百科工具">
                        <div>百科工具</div>
                        <button>在各种文化中，有哪些令人惊奇的神话传说?</button>
                        <button>有哪些人从传说认为是真的，但后来被证实是错的历史观念或谣言?</button>
                        <button>我想系统的学习编程，帮助我详细规划一下</button>
                    </div>
                    <div class="section">
                        <img src="/app/ai/avatar/detail_shenghuo.png" alt="生活">
                        <div>生活</div>
                        <button>人生必读：打破财务困境的秘密方法!</button>
                        <button>单身狗福音！学会高级约会的绝招！</button>
                        <button>掌握这些技能，让你的职业生涯腾飞！</button>
                    </div>
                </div>
            </div>
            <div class="footer">
                <input type="text" placeholder="输入内容（Shift + Enter）发送">
                <button>发送</button>
                <button class="clear-chat-footer">清除对话</button>
            </div>
        </div>
    </div>
</body>
</html>
