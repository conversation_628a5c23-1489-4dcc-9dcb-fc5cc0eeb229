<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="UTF-8">
        <title>新增页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/component/jsoneditor/css/jsoneditor.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body>

        <form class="layui-form" action="">

            <div class="mainBox">
                <div class="main-container mr-5">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">apikey</label>
                        <div class="layui-input-block">
                            <input type="text" name="apikey" value="" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">api地址</label>
                        <div class="layui-input-block">
                            <input type="text" name="api" value="" placeholder="默认请留空" class="layui-input"/>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">gp3</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="gpt3" lay-filter="gpt3" lay-skin="switch" />
                            <input type="text" style="display:none" name="gpt3" value="1" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">gpt4</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="gpt4" lay-filter="gpt4" lay-skin="switch" />
                            <input type="text" style="display:none" name="gpt4" value="0" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">dalle</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="dalle" lay-filter="dalle" lay-skin="switch" />
                            <input type="text" style="display:none" name="dalle" value="0" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">tts</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="tts" lay-filter="tts" lay-skin="switch" />
                            <input type="text" style="display:none" name="tts" value="1" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">embedding</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="embedding" lay-filter="embedding" lay-skin="switch" />
                            <input type="text" style="display:none" name="embedding" value="1" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">错误信息</label>
                        <div class="layui-input-block">
                            <textarea name="last_error" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">错误次数</label>
                        <div class="layui-input-block">
                            <input type="number" name="error_count" value="0" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">消息数</label>
                        <div class="layui-input-block">
                            <input type="number" name="message_count" value="" class="layui-input">
                        </div>
                    </div>
                    
                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                        lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
        </form>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/component/jsoneditor/jsoneditor.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        
        <script>

            // 相关接口
            const INSERT_API = "/app/ai/admin/ai-apikey/insert";
            
            // 字段 禁用 state
            layui.use(["form"], function() {
                layui.$("#state").attr("checked", layui.$('input[name="state"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(state)", function(data) {
                    layui.$('input[name="state"]').val(this.checked ? 1 : 0);
                });
            })
            
            // 字段 gpt4 gpt4
            layui.use(["form"], function() {
                layui.$("#gpt4").attr("checked", layui.$('input[name="gpt4"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(gpt4)", function(data) {
                    layui.$('input[name="gpt4"]').val(this.checked ? 1 : 0);
                });
            })
            
            // 字段 embedding embedding
            layui.use(["form"], function() {
                layui.$("#embedding").attr("checked", layui.$('input[name="embedding"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(embedding)", function(data) {
                    layui.$('input[name="embedding"]').val(this.checked ? 1 : 0);
                });
            })
            
            // 字段 dalle dalle
            layui.use(["form"], function() {
                layui.$("#dalle").attr("checked", layui.$('input[name="dalle"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(dalle)", function(data) {
                    layui.$('input[name="dalle"]').val(this.checked ? 1 : 0);
                });
            })

            // 字段 gp3 gpt3
            layui.use(["form"], function() {
                layui.$("#gpt3").attr("checked", layui.$('input[name="gpt3"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(gpt3)", function(data) {
                    layui.$('input[name="gpt3"]').val(this.checked ? 1 : 0);
                });
            })
            
            // 字段 tts tts
            layui.use(["form"], function() {
                layui.$("#tts").attr("checked", layui.$('input[name="tts"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(tts)", function(data) {
                    layui.$('input[name="tts"]').val(this.checked ? 1 : 0);
                });
            })
            
            //提交事件
            layui.use(["form", "popup"], function () {
                // 字段验证允许为空
                layui.form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });
                layui.form.on("submit(save)", function (data) {
                    layui.$.ajax({
                        url: INSERT_API,
                        type: "POST",
                        dateType: "json",
                        data: data.field,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>
</html>
