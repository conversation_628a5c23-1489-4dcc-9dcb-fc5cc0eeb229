<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/app/ai/css/bootstrap.min.css?v=5.3" rel="stylesheet">
    <link href="/app/ai/css/app.css?v=<?=ai_css_version()?>" rel="stylesheet">
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>

    <title>用户扫码</title>


</head>
<body class="light-bg" data-bs-theme="light">
<script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
<div class="container">
    <div class="layui-card">
        <div class="layui-card-header"></div>
        <div class="layui-card-body">
            <h1><b><img width="25px" src="data:image/svg+xml;charset=utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='24' height='24' viewBox='0 0 24 24'>  <defs>    <path id='0f20791c-6774-4e52-920f-6b6d8404b4dc-a' d='M6.724 0h10.552c2.338 0 3.186.243 4.04.7A4.766 4.766 0 0 1 23.3 2.684c.458.855.701 1.703.701 4.04v10.553c0 2.338-.243 3.186-.7 4.04a4.766 4.766 0 0 1-1.983 1.983c-.855.458-1.703.701-4.04.701H6.723c-2.338 0-3.186-.243-4.04-.7A4.766 4.766 0 0 1 .7 21.316c-.457-.854-.7-1.702-.7-4.039V6.723c0-2.338.243-3.186.7-4.04A4.766 4.766 0 0 1 2.684.7C3.538.243 4.386 0 6.723 0z'/>    <linearGradient id='0f20791c-6774-4e52-920f-6b6d8404b4dc-b' x1='50%25' x2='50%25' y1='0%25' y2='100%25'>      <stop offset='0%25' stop-color='%2302E36F'/>      <stop offset='100%25' stop-color='%2305CD65'/>      <stop offset='100%25' stop-color='%2307C160'/>    </linearGradient>  </defs>  <g fill='none' fill-rule='evenodd'>    <mask id='0f20791c-6774-4e52-920f-6b6d8404b4dc-c' fill='%23fff'>      <use xlink:href='%230f20791c-6774-4e52-920f-6b6d8404b4dc-a'/>    </mask>    <path fill='url(%230f20791c-6774-4e52-920f-6b6d8404b4dc-b)' d='M0 0h24v24H0z' mask='url(%230f20791c-6774-4e52-920f-6b6d8404b4dc-c)'/>    <path fill='%23FFF' d='M19.095 17.63c1.141-.826 1.87-2.05 1.87-3.408 0-2.49-2.423-4.51-5.411-4.51-2.989 0-5.411 2.02-5.411 4.51 0 2.49 2.422 4.51 5.41 4.51.618 0 1.214-.089 1.767-.248a.543.543 0 0 1 .447.06l1.184.683c.033.02.065.034.104.034.1 0 .18-.08.18-.18 0-.045-.017-.09-.028-.132l-.244-.91a.36.36 0 0 1 .132-.409M13.75 13.5a.721.721 0 1 1 0-1.442.721.721 0 0 1 0 1.443M9.493 4.734c3.24 0 5.925 1.977 6.414 4.562a7.206 7.206 0 0 0-.353-.01c-3.27 0-5.922 2.21-5.922 4.936 0 .46.077.904.218 1.326a7.687 7.687 0 0 1-2.476-.288.651.651 0 0 0-.536.071l-1.421.82a.245.245 0 0 1-.125.041.216.216 0 0 1-.217-.216c0-.054.021-.107.035-.158l.292-1.092a.433.433 0 0 0-.159-.49C3.876 13.243 3 11.775 3 10.145c0-2.989 2.907-5.412 6.493-5.412zm7.865 7.323a.721.721 0 1 1 0 1.443.721.721 0 0 1 0-1.443zM7.328 7.548a.866.866 0 1 0 0 1.732.866.866 0 0 0 0-1.732zm4.33 0a.866.866 0 1 0 0 1.731.866.866 0 0 0 0-1.73z' mask='url(%230f20791c-6774-4e52-920f-6b6d8404b4dc-c)'/>  </g></svg>"> 使用微信扫一扫登录</b></h1>
            <h2>【AI问答系统】</h2>
            <h2>
                <span style="position: relative;display: inline-block;background: #d7d7d7;height: 180px;width: 180px;">
                    <div id="chatQr3" style="display: none">
                        <div style="color: red;margin-top: 40px;">二维码已过期</div>
                        <a class="qr_restart" id="qr_restart">请刷新二维码</a>
                    </div>
                    <!-- 加载动画的模态框 -->
                   <div id="spinnerborder" class="spinner-border" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <img id="qrcode" style="display: none" alt="扫码登录" width="180px" src="">
                </span>
            </h2>
            <h6 style="margin-top: 40px" id="scantishi">微信扫码登录/注册</h6>
            <h6 id="scaninfo" style="color: #b7b3b3">首次扫码关注公众号后将自动注册新账号</h6>
        </div>
    </div>


</div>
<style>
    h1 {
        margin-top: 200px;
        color: #5FB878;
        font-size: 20px;
    }
    h2 {
        font-size: 15px;
    }

    .layui-card-body {
        text-align: center;
        height: 500px;
    }
    .spinner-border {
        margin: 67px;
    }
    .fixed-table-body {
        height: auto;
    }

    #chatQr3 {
        width: 100%;
        position: absolute;
        height: 180px;
        background-color: rgba(255, 255, 255, 0.8);
        text-align: center;
        border: 1px solid #dedede;
        z-index: 99;
    }

    .qr_restart {
        display: inline-block;
        width: 110px;
        height: 35px;
        background-color: #ff3c3c;
        border-radius: 4px;
        color: #fff;
        text-align: center;
        line-height: 35px;
        font-size: 12px;
        margin-top: 20px;
    }



</style>
<script src="/app/user/js/webman.js"></script>
<script>
    // 定时检查扫码状态
    var endi = 180;
    var i = 0;
    function checkScanStatus(sceneStr) {
        $.ajax({
            url: "/app/user/checkscan",
            type: "POST",
            dataType: "json",
            data: {'scene_str':sceneStr},
            success: function (e) {
                // console.log(555)
                if (e.code === 1) {

                    webman.success('登录成功', function () {
                        let url = new URL(window.location.href);
                        let redirect = url.searchParams.get('redirect');
                        // console.log(url)
                        // console.log(redirect)
                        console.log(redirect && redirect.startsWith('/') && !redirect.startsWith('//') ? redirect : '/app/user')
                        location.href = redirect && redirect.startsWith('/') && !redirect.startsWith('//') ? redirect : '/app/user';
                        try {
                            window.parent.ai.switchModule('chat');
                            window.parent.ai.loadUserInfo();
                        } catch (e) {}
                    });
                    // 成功页面跳转
                    // webman.success('登录成功', function () {
                    //     window.parent.ai.switchModule('chat');
                    //     window.parent.ai.loadUserInfo();
                    //     // location.href = "/app/ai/user";
                    // });
                    // window.location.href = `https://yd.baimalive.com/WeChatQRLogin/wechat_template.php?scene_str=${sceneStr}&ip=${ipAddress}&ua=${navigator.userAgent}`;
                    // window.location.href = 'https://yd.baimalive.com/WeChatQRLogin/wechat_template.php';
                } else {
                    var timer = setTimeout(() => checkScanStatus(sceneStr), 2000);

                    i = i + 2;
                    if (i > endi){
                        console.log("二维码已过期")
                        $("#chatQr3").css("display", "block")
                        clearTimeout(timer); // timer 为计时器的ID
                        i = 0;
                    }
                }
            }
        });
    }

    // 重新刷新二维码
    document.getElementById('qr_restart').onclick = function(){
        getscan();
    };

    // 调用请求
    getscan();

    function getscan() {
        $.ajax({
            url: "/app/user/getscan",
            type: "POST",
            dataType: "json",
            data: {'a':1},
            success: function (e) {
                if (e.code == 0){
                    document.getElementById('qrcode').src = e.srcImg;
                    // document.getElementById('scantishi').innerText = "等待二维码显示，";
                    // document.getElementById('scantishi').style.color = "red";
                    $("#chatQr3").css("display", "none")
                    document.getElementById('qrcode').style.display = "block";
                    document.getElementById('spinnerborder').style.display = "none";
                    // document.getElementsByClassName("spinner-border").style.display = 'none';
                    checkScanStatus(e.scene_str);
                }else{
                    console.log("错误提示",e)
                }
            }
        });
    }




</script>

</body>
</html>
