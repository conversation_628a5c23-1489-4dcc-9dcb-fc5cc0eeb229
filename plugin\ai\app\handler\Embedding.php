<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

class Embedding extends Gpt3
{
    /**
     * @var string
     */
    protected static $name = 'Embedding';

    /**
     * @var string 模型类型
     */
    protected static $type = 'embedding';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'text-embedding-ada-002',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'api' => [
            'name' => 'API',
            'type' => 'text',
            'value' => 'https://api.ailard.com',
            'desc' => 'API 地址',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 100,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = \Webman\Openai\Embedding::class;

    /**
     * Embedding
     * @param array $data
     * @param array $options
     * @return void
     */
    public function create(array $data, array $options)
    {
        $settings = $this->getRealSettings($data['model']);
        $this->driver = new $this->driverClass($settings);
        $this->driver->create($data, $options);
    }

}