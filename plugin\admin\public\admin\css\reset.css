button {
    line-height: 100% !important;
}

.pear-btn-md, .pear-btn-sm, .pear-btn-xs, .pear-btn {
    line-height: 100%;
    letter-spacing: 2px;
    padding: 0 15px;
    font-weight: 400;
    font-size: 14px;
}

.layui-layer-btn {
    padding: 0 15px 19px;
    letter-spacing: 2px;
}

.pear-btn i {
    font-size: 14px;
}

.layui-table-cell .pear-btn i {
    font-size: 15px;
}

.layui-table-tips-main {
    overflow: hidden;
}

.layui-table th {
    font-weight: 500;
}

.layui-laypage a, .layui-laypage span {
    font-size: 14px;
}

.layui-table-tool .layui-inline[lay-event], .layui-table-tool .layui-inline[lay-event]:hover {
    border:none;
}

.layui-table-tool-self .layui-icon {
    font-size: 18px;
}

.layui-table, .pear-tab .layui-tab-title li {
    color: #333;
}

.tool-btn.pear-btn-xs {
    background: transparent;
    font-weight: normal;
    font-size: 14px;
    color: #333;
    border: none;
    padding: 0 2px;
    height: auto !important;
}

.layui-iconpicker {
    max-width: 290px !important;
}

.layui-iconpicker .layui-anim {
    max-height: 220px !important;
}

.layui-input:disabled {
    color: rgba(0,0,0,.25);
    background: #f5f5f5;
    cursor: not-allowed;
}

.layui-form-label.required:after {
    content: "*";
    color: red;
    display: inline-block;
    padding: 7px 5px;
    line-height: 20px;
    text-align: right;
    position: absolute;
    right: 0;
    top: 5px;
}

.layui-bg-green {
    background-color: #36b368 !important;
}

a {
    cursor: pointer;
}

*[permission] {
    display: none;
}

@media screen and (max-width:768px) {
    /**  **/
    .layui-table-tool-self .layui-inline:nth-child(3){
        display: none;
    }
}

/** 右间隔 **/
.mr-1
{
    margin-right: 4px;
}
.mr-2
{
    margin-right: 8px;
}
.mr-3
{
    margin-right: 16px;
}
.mr-4
{
    margin-right: 24px;
}
.mr-5
{
    margin-right: 48px;
}

/** 图片尺寸 **/
.img-1
{
    max-width: 16px;
    max-height: 16px;
    border-radius: 6px;
}
.img-2
{
    max-width: 32px;
    max-height: 32px;
    border-radius: 6px;
}
.img-3
{
    max-width: 64px;
    max-height: 64px;
    border-radius: 6px;
}
.img-4
{
    max-width: 128px;
    max-height: 128px;
    border-radius: 6px;
}

/** 左侧菜单 Start **/

.pear-nav-tree .layui-nav-item a span {
    font-size: 14px;
    letter-spacing: 1.5px;
}

.light-theme .pear-nav-tree a, .light-theme .pear-nav-tree .layui-nav-more {
    color: #333;
}

/** 左侧菜单 End **/


/** 表详情顶部搜索框 Start **/

.top-search-from .layui-form-item {
    position: relative;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: middle;
}
.top-search-from .layui-form-item .layui-input-block {
    float: left;
    width: 270px;
    margin-right: 10px;
    margin-left: 0;
    display: inline-block;
    vertical-align: middle;
    position: relative;
}
.top-search-from .layui-input, .top-search-from .layui-select, .top-search-from .layui-textarea {
    width: 270px;
}
.top-search-from .item-inline .layui-form-item .layui-input-block {
    width: 270px;
}
.top-search-from .layui-form-mid {
    margin-left: 7px;
    margin-right: 7px;
}
.top-search-from .layui-form-label {
    width: 60px;
    white-space: nowrap;
}
.top-search-from .inline-block {
    width:128px;
    display:inline-block
}
.top-search-from .layui-form-item {
    width: 370px;
}
.top-search-from .toggle-btn {
    text-align: center;
}
.top-search-from .toggle-btn a {
    cursor: pointer;
    color: #999;
}
.top-search-from .toggle-btn a:hover {
    color: #333;
}
@media screen and (max-width:768px) {
    .top-search-from .layui-input {
        max-width: 234px;
    }
    .top-search-from .layui-input.inline-block {
        max-width: 110px;
    }
    .layui-table-tool-self .layui-inline:nth-child(3){
        display: none;
    }
}

/** 表详情顶部搜索框 End **/

/* tinymce 样式优化 Start */

.layui-input-block .tox.tox-tinymce.tox-fullscreen {
    z-index: 1;
}

.tox.tox-tinymce-aux {
	z-index: 90000000 !important;
}

/* tinymce 样式优化 End */
