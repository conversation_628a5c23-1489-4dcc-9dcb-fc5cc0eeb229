<?php

namespace plugin\ai\app\model;

use plugin\admin\app\model\Base;

/**
 * @property integer $roleId 主键(主键)
 * @property string $name 名称
 * @property string $avatar 头像
 * @property string $desc 简介
 * @property string $rolePrompt 角色提示
 * @property string $greeting 问候语
 * @property string $model 模型
 * @property integer $contextNum 上下文数
 * @property integer $maxTokens 最大tokens
 * @property mixed $temperature 温度
 * @property string $dataset 数据集
 * @property integer $preinstalled 预安装
 * @property integer $installed 安装量
 * @property integer $category 分类
 */
class AiRole extends Base
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ai_roles';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'roleId';

    /**
     * @param $column
     * @param $amount
     * @param array $extra
     * @return false|int
     */
    public function increment($column, $amount = 1, array $extra = [])
    {
        return parent::increment($column, $amount, $extra); // TODO: Change the autogenerated stub
    }

    /**
     * @return void
     */
    public static function init()
    {
        // 没有数据的时候从roles.json生成
        if (!static::first()) {
            $roles = json_decode(file_get_contents(base_path('plugin/ai/roles.json')), true);
            foreach ($roles as $item) {
                $role = new AiRole();
                foreach (['roleId', 'model', 'name', 'desc', 'rolePrompt', 'avatar', 'maxTokens', 'contextNum', 'greeting', 'temperature', 'category', 'language', 'speaker'] as $name) {
                    if (isset($item[$name])) {
                        $role->$name = $item[$name];
                    }
                }
                $role->save();
            }
        }
    }
}
