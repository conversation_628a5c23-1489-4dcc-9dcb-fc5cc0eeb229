CREATE TABLE `ai_apikeys` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `apikey` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'apikey',
  `state` tinyint DEFAULT '0' COMMENT '禁用',
  `last_error` text COLLATE utf8mb4_general_ci COMMENT '错误信息',
  `error_count` int DEFAULT '0' COMMENT '错误次数',
  `last_message_at` datetime DEFAULT NULL COMMENT '消息时间',
  `message_count` int DEFAULT NULL COMMENT '消息数',
  PRIMARY KEY (`id`),
  KEY `error_count` (`error_count`),
  KEY `last_message_at` (`last_message_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APIKEY';

CREATE TABLE `ai_orders` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` varchar(50) NOT NULL COMMENT '订单id',
  `user_id` int NOT NULL COMMENT '用户id',
  `total_amount` decimal(10,2) NOT NULL COMMENT '须支付金额',
  `paid_amount` decimal(10,2) DEFAULT NULL COMMENT '已支付总额',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `state` enum('unpaid','paid') NOT NULL DEFAULT 'unpaid' COMMENT '状态',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `data` text COMMENT '业务数据',
  `payment_method` enum('wechat','alipay') NOT NULL DEFAULT 'alipay' COMMENT '支付方式',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI订单';

CREATE TABLE `ai_roles` (
  `roleId` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `avatar` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '/app/ai/avatar/ai.png' COMMENT '头像',
  `desc` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '简介',
  `rolePrompt` text COLLATE utf8mb4_general_ci COMMENT '角色提示',
  `greeting` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问候语',
  `model` varchar(255) COLLATE utf8mb4_general_ci DEFAULT 'gpt-3.5-turbo-16k' COMMENT '模型',
  `contextNum` int DEFAULT '12' COMMENT '上下文数',
  `maxTokens` int DEFAULT '2000' COMMENT '最大tokens',
  `temperature` double(8,2) DEFAULT '0.50' COMMENT '温度',
  PRIMARY KEY (`roleId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI角色';

CREATE TABLE `ai_users` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int NOT NULL COMMENT '用户id',
  `expired_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI用户表';

alter table ai_users add column `message_count` int DEFAULT '0' COMMENT '消息数';
alter table ai_apikeys add column `suspended` tinyint DEFAULT '0' COMMENT '停用';

alter table ai_roles add column `preinstalled` tinyint DEFAULT '1' COMMENT '预安装';
alter table ai_roles add column `installed` int DEFAULT NULL COMMENT '安装量';
alter table ai_roles add column `category` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分类';

CREATE TABLE `ai_messages` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `user_id` int DEFAULT NULL COMMENT '用户id',
  `session_id` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'session id',
  `role_id` bigint DEFAULT NULL COMMENT '角色id',
  `model` varchar(32) NOT NULL default 'gpt-3.5-turbo',
  `chat_id` int DEFAULT NULL COMMENT '对话id',
  `message_id` bigint DEFAULT NULL COMMENT '消息id',
  `role` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色',
  `content` mediumtext COLLATE utf8mb4_general_ci COMMENT '内容',
  `ip` varchar(32) DEFAULT NULL COMMENT 'ip',
  PRIMARY KEY (`id`),
  KEY `message_id` (`message_id`),
  KEY `user_id-session_id-role_id-chat_id` (`user_id`,`session_id`,`chat_id`),
  KEY `user_id-role_id-chat_id` (`user_id`,`chat_id`),
  KEY `session_id-role_id-chat_id` (`session_id`,`chat_id`),
  KEY `ip` (`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI消息';

CREATE TABLE `ai_ban` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `type` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
  `value` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '值',
  `log` mediumtext COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志',
  `expired_at` datetime DEFAULT NULL COMMENT '有效期',
  PRIMARY KEY (`id`),
  KEY `item` (`type`,`value`,`expired_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='封禁表';

alter table ai_apikeys add column `gpt4` tinyint DEFAULT '0' COMMENT '支持gpt4' after message_count;
alter table ai_messages modify `chat_id` bigint DEFAULT NULL COMMENT '对话id';
alter table ai_messages modify `ip` varchar(64) DEFAULT NULL COMMENT 'ip';

alter table ai_roles add column `language` varchar(32) DEFAULT 'zh_cn' COMMENT '语言';
alter table ai_roles add column `speaker` varchar(32) DEFAULT 'yingying' COMMENT '朗读者';
alter table ai_roles add column `created_at` datetime DEFAULT '2023-11-11 00:00:00' COMMENT '创建时间';
alter table ai_roles add column `updated_at` datetime DEFAULT '2023-11-11 00:00:00' COMMENT '更新时间';

alter table ai_apikeys add column `gpt3` tinyint DEFAULT '1' COMMENT '支持gpt3';
alter table ai_roles add column `dataset` varchar(32) DEFAULT NULL COMMENT '数据集';

CREATE TABLE `ai_datasets` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `name` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `desc` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '介绍',
  `model` varchar(64) COLLATE utf8mb4_general_ci DEFAULT 'text-embedding-ada-002' COMMENT 'embedding模型',
  `status` enum('stopped','training') COLLATE utf8mb4_general_ci DEFAULT 'stopped' COMMENT '状态',
  `delay` int DEFAULT '0' COMMENT '延迟间隔',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据集';

CREATE TABLE `ai_embeddings` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `filename` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件名',
  `text` mediumtext COLLATE utf8mb4_general_ci COMMENT '内容',
  `text_embedding` text COLLATE utf8mb4_general_ci COMMENT '内容向量',
  `dataset_id` int DEFAULT NULL COMMENT '数据集ID',
  `log` text COLLATE utf8mb4_general_ci COMMENT '日志',
  PRIMARY KEY (`id`),
  KEY `dataset_id` (`dataset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据集向量';

alter table ai_apikeys add column `dalle` tinyint DEFAULT '1' COMMENT '支持dalle' after gpt4;
alter table ai_apikeys add column `embedding` tinyint DEFAULT '0' COMMENT '支持embedding' after gpt4;

alter table ai_apikeys add column `tts` tinyint DEFAULT '1' COMMENT 'tts';
alter table ai_apikeys add column `api` varchar(255) DEFAULT NULL COMMENT 'api地址';
alter table ai_roles add column `status` tinyint DEFAULT '0' COMMENT '停用';
alter table ai_users add column `balance` text COLLATE utf8mb4_general_ci COMMENT '余额';
CREATE TABLE `ai_models` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
  `handler` varchar(190) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型处理器',
  `models` text COLLATE utf8mb4_general_ci COMMENT '支持的模型',
  `priority` int NOT NULL DEFAULT '0' COMMENT '优先级',
  `settings` text COLLATE utf8mb4_general_ci COMMENT '设置',
  `status` tinyint DEFAULT '0' COMMENT '禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `handler` (`handler`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='models';
UPDATE ai_users SET balance = CONCAT(
'{',
  '\"gpt3\": ', available_gpt3, ',',
  '\"gpt4\": ', available_gpt4, ',',
  '\"dalle\": ', available_dalle, ',',
  '\"midjourney\": ', available_midjourney, ',',
  '\"ernie\": ', available_ernie, ',',
  '\"qwen\": ', available_qwen, ',',
  '\"spark\": ', available_spark, ',',
  '\"chatglm\": ', available_chatglm, ',',
  '\"gemini\": ', available_gemini,
'}'
) where balance is null;
UPDATE ai_users SET balance = CONCAT(
'{',
  '\"gpt3\": ', available_gpt3, ',',
  '\"gpt4\": ', available_gpt4, ',',
  '\"dalle\": ', available_dalle, ',',
  '\"midjourney\": ', available_midjourney, ',',
  '\"ernie\": ', available_ernie, ',',
  '\"qwen\": ', available_qwen, ',',
  '\"spark\": ', available_spark, ',',
  '\"chatglm\": ', available_chatglm,
'}'
) where balance is null;
UPDATE ai_users SET balance = CONCAT(
'{',
  '\"gpt3\": ', available_gpt3, ',',
  '\"gpt4\": ', available_gpt4, ',',
  '\"dalle\": ', available_dalle, ',',
  '\"midjourney\": ', available_midjourney, ',',
  '\"ernie\": ', available_ernie, ',',
  '\"qwen\": ', available_qwen, ',',
  '\"spark\": ', available_spark,
'}'
) where balance is null;
alter table ai_users drop column available_gpt3;
alter table ai_users drop column available_gpt4;
alter table ai_users drop column available_dalle;
alter table ai_users drop column available_midjourney;
alter table ai_users drop column available_ernie;
alter table ai_users drop column available_qwen;
alter table ai_users drop column available_spark;
alter table ai_users drop column available_chatglm;
alter table ai_users drop column available_azure;
alter table ai_users drop column available_gemini;
delete from wa_rules where `key` like 'plugin\\\\ai\\\\app\\\\admin\\\\controller\\\\ApiKey%';
delete from wa_rules where `key` like 'plugin\\\\gpt\\\\app\\\\admin\\\\controller\\\\ApiKey%';

alter table `ai_users` add column `expired_updated_at` datetime COMMENT '过期更新时间';

CREATE TABLE `ai_images` (
 `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
 `task_id` bigint DEFAULT NULL COMMENT '任务id',
 `created_at` datetime DEFAULT NULL COMMENT '创建时间',
 `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
 `model` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型',
 `type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
 `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '提示词',
 `thumb_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '缩略图',
 `small_url` text COLLATE utf8mb4_general_ci COMMENT '预览图',
 `image_url` text COLLATE utf8mb4_general_ci COMMENT '原始图',
 `image` text COLLATE utf8mb4_general_ci COMMENT '图像数据',
 `data` mediumtext COLLATE utf8mb4_general_ci COMMENT '结果数据',
 `user_id` int DEFAULT NULL COMMENT '用户id',
 `session_id` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'session_id',
 `ip` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ip',
 `gallery` tinyint DEFAULT '0' COMMENT '广场显示',
 PRIMARY KEY (`id`),
 UNIQUE KEY `task_id` (`task_id`),
 KEY `created_at` (`created_at`),
 KEY `model` (`model`),
 KEY `type` (`type`),
 KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图片';

alter table ai_models drop KEY `handler`;