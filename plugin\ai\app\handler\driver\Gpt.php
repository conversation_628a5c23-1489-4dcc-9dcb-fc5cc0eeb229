<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler\driver;

use Webman\Openai\Chat;
use Workerman\Http\Response;

class Gpt extends Chat
{

    /**
     * @param array $data
     * @param array $options
     * @return void
     */
    public function completions(array $data, array $options)
    {
        $data = $this->formatData($data);
        if (isset($options['complete'])) {
            $options['complete'] = function ($result, Response $response) use ($data, $options) {
                if (isset($result['error'])) {
                    return $options['complete']($result, $response);
                }
                if (!empty($result['choices'][0]['message']['tool_calls'][0])) {
                    $options['complete']($result['choices'][0]['message']['tool_calls'][0], $response);
                } else {
                    $options['complete']($result['choices'][0]['message']['content'], $response);
                }
            };
        }
        if (isset($options['stream'])) {
            $options['stream'] = function ($data) use ($options) {
                $data = array_merge(['content' => ''], $data);
                unset($data['model']);
                $data['content'] = $data['choices'][0]['delta']['content'] ?? '';
                $options['stream']($data);
            };
        }
        parent::completions($data, $options);
    }

    /**
     * @param $data
     * @return array
     */
    protected function formatData($data): array
    {
        $messages = $data['messages'];
        $model = $data['model'];
        if (strpos($model, 'gpt') === false) {
            return $data;
        }
        $isGptVision = strpos($model, 'gpt-4-vision') !== false || strpos($model, 'gpt-4-turbo') !== false || strpos($model, 'gpt-4o') !== false;
        foreach ($messages as $key => $message) {
            if (is_array($message['content']) && (!$isGptVision || !isset($message['content'][0]['type']))) {
                $messages[$key]['content'] = json_encode($message['content']);
            }
        }
        $data['messages'] = $messages;
        if ($model === 'gpt-4-vision-preview' && !isset($data['max_tokens'])) {
            $data['max_tokens'] = 2000;
        }
        return $data;
    }

}