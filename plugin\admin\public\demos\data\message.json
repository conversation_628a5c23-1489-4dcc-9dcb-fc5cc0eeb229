[{"id": 1, "title": "通知", "children": [{"id": 11, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png", "title": "你收到了 14 份新周报", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}, {"id": 12, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png", "title": "曲妮妮 已通过第三轮面试", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}, {"id": 11, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/kISTdvpyTAhtGxpovNWd.png", "title": "可以区分多种通知类型", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}, {"id": 12, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png", "title": "左侧图标用于区分不同的类型", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}, {"id": 11, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png", "title": "内容不要超过两行字", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}]}, {"id": 2, "title": "消息", "children": [{"id": 11, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png", "title": "你收到了 14 份新周报", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}, {"id": 12, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png", "title": "曲妮妮 已通过第三轮面试", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}, {"id": 11, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/kISTdvpyTAhtGxpovNWd.png", "title": "可以区分多种通知类型", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}, {"id": 12, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png", "title": "左侧图标用于区分不同的类型", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}, {"id": 11, "avatar": "https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png", "title": "内容不要超过两行字", "context": "这是消息内容。", "form": "就眠仪式", "time": "刚刚"}]}, {"id": 3, "title": "代办", "children": []}]