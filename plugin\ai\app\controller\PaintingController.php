<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\controller;

use plugin\ai\app\service\Midjourney;
use support\Request;
use support\Response;

/**
 * 绘画
 */
class PaintingController extends Base
{

    /**
     * 不需要登录的方法
     *
     * @var string[]
     */
    protected $noNeedLogin = ['index'];

    /**
     * 绘画首页
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        return view('painting/index');
    }

}