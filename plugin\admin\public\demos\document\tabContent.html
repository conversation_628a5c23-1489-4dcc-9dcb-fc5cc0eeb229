<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>多选项卡内容</title>
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
	</head>
	<body class="pear-container">
		<div class="layui-card">
			<div class="layui-card-header"></div>
			<div class="layui-card-body">
				<button class="pear-btn pear-btn-primary add">新增 Demo</button>
				<button class="pear-btn pear-btn del">关闭 当前</button>
			</div>
		</div>
	</body>
		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
	<script>
		layui.use(['tab','jquery','layer'],function(){
			var $ = layui.jquery,
				layer = layui.layer,
				tab = layui.tab;
				
				$(".add").click(function(){
					// ADD 添加方法
					parent.layui.tab.addTabOnlyByElem('contents',{id:'demo',title:'Demo',url:'../system/space.html',close: true})
				})
				
				$(".del").click(function(){
					parent.layui.tab.delCurrentTabByElem('contents',function(id){
						layer.msg("已删除 '"+id+"' 标签页")
					});
				})
				
		})
	</script>
</html>
