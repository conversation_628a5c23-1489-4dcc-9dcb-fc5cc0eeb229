<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>立即支付</title>
</head>
<link rel="stylesheet" href="/app/ai/layui-v2.7.6/layui/css/layui.css">
<script src="/app/ai/js/jquery.min.js"></script>
<body>

<div class="layui-form-pane pay-list" style="margin: 25px">
    <style>


        #chatQr1, #chatQr3 {
            position: absolute; /* 绝对定位 */
            width: 200px; /* 宽度 */
            /*height: 100px; !* 高度 *!*/
            margin: 0px 80px;
            /*top: 50px; !* 顶部位置 *!*/
            /*left: 50px; !* 左侧位置 *!*/
        }
        #chatQr1 {
            /*background-color: red; !* div1的背景颜色 *!*/
            z-index: 1; /* 层叠顺序，1层在下 */
        }

        .qr_restart {
            display: inline-block;
            width: 110px;
            height: 35px;
            background-color: #ff3c3c;
            border-radius: 4px;
            color: #fff;
            text-align: center;
            line-height: 35px;
            font-size: 12px;
        }

        #chatQr3 {
            position: absolute;
            height: 200px;
            top: 100px;
            background-color: rgba(255, 255, 255, 0.8);
            text-align: center;
            border: 1px solid #dedede;
            z-index: 99;
        }
    </style>
    <div id="chatQr1" style="text-align: center;display: none">
        <div>订单号：<span id="payOrder"></span></div>
        <img width="200px" height="200px" id="createId" src="https://wenhairu.com/static/api/qr/?size=300&text=weixin://wxpay/bizpayurl?pr=Qn7XdSbz1">
        <div style="color: #b7b7b7;">使用微信扫一扫付款</div>
    </div>
    <div id="chatQr3" style="display: none">
        <div style="color: red;margin-top: 40px;">二维码已过期</div>
        <a class="qr_restart" id="qr_restart">请刷新二维码</a>
    </div>
    <div id="chatQr2" style="display: block">
        <div><span class="pay-title">套餐名称</span> <span><?php echo $name;?></span></div>
        <div><span class="pay-title">支付金额</span> <span>￥<?php echo $price;?></span></div>
        <div><span class="pay-title">实付金额</span> <span class="sub-sty">￥<?php echo $price;?></span></div>
        <div><span class="pay-title">支付方式</span> <span> <img width="20px" src="https://hetao-1306534873.cos.ap-shanghai.myqcloud.com/resource/image/adminapi/pay/wechatpay.png"> 微信支付</span></div>
        <div style="text-align: center;margin-top: 45px">
            <button type="button" id="payNowBtn" class="layui-btn layui-btn-sm">立即支付</button>
        </div>
    </div>


    <input name="id" type="hidden" id="tcId" value="<?php echo $id;?>">
    <input name="type" type="hidden" id="tcType" value="<?php echo $type;?>">
</div>
</body>
<script src="/app/ai/layui-v2.7.6/layui/layui.js"></script>
</html>
<style>
    .pay-list div{
        line-height: 40px;
    }
    .pay-title{
        margin-right: 15px;
    }
    .sub-sty{
        color: red;
    }
    .layui-btn{
        background: #43CEA2 !important;
    }
</style>

<script>

    // 引入Layui的layer模块
    layui.use('layer', function(){
        var layer = layui.layer;

        function create_order() {
            // 创建遮罩层
            var index = layer.load(0, {shade: [0.5, '#393D49']}); //0.5 是遮罩层的透明度，'#393D49' 是遮罩层的颜色
            // 获取套餐id
            var id = $("#tcId").val()
            var type = $("#tcType").val()
            // alert(id)
            // return false;

            $.ajax({
                url: "/app/ai/user/create_order",
                type: "POST",
                dataType: 'json',
                data: {id:id,type:type},
                success: function (e) {
                    if (e.code == 200){
                        // 关闭遮罩层
                        // console.log(111111)
                        layer.close(index);
                        // 旋转加载中
                        // 请求二维码
                        $("#createId").attr('src', e.qr_url);
                        $("#payOrder").html(e.order)
                        $("#chatQr3").css("display", "none")
                        $("#chatQr2").css("display", "none")
                        $("#chatQr1").css("display", "block")
                        checkScan(e.order);
                    }else{
                        layer.msg("信息有误")
                    }
                }
            });
        }

        // 给按钮绑定点击事件
        document.getElementById('payNowBtn').onclick = function(){
            create_order();
        };


        // 定时查询
        var i = 0;
        var endi = 180;
        function checkScan(sceneStr) {
            console.log("时间：",i)
            $.ajax({
                url: "/app/ai/user/check_pay",
                type: "POST",
                dataType: "json",
                data: {'scene_str':sceneStr},
                success: function (e) {
                    // console.log(555)
                    if (e.code === 1) {
                        // 成功页面跳转
                        layer.msg("支付成功",{
                            icon: 1,
                            time: 2000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            var index = parent.layer.getFrameIndex(window.name); // 获取当前弹框的索引
                            parent.layer.close(index); // 关闭当前弹框
                        });
                    } else {

                        var timer = setTimeout(() => checkScan(sceneStr), 2000);

                        i = i + 2;
                        if (i > endi){
                            console.log("微信支付二维码已过期")
                            $("#chatQr3").css("display", "block")
                            clearTimeout(timer); // timer 为计时器的ID
                            i = 0;
                        }

                    }
                }
            });
        }

        // 重新刷新二维码
        document.getElementById('qr_restart').onclick = function(){
            create_order();
        };

    });
</script>