<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\controller;

use Exception;
use Illuminate\Database\Eloquent\Model;
use plugin\ai\app\model\AiOrder;
use plugin\ai\app\model\AiUser;
use plugin\ai\app\service\Common;
use plugin\ai\app\service\Setting;
use plugin\user\api\User;
use plugin\user\app\service\Register;
use support\Db;
use support\Request;
use support\Response;
use Yansongda\Pay\Pay;

class UserController extends Base
{

    /**
     * 不需要登录的方法
     *
     * @var string[]
     */
    protected $noNeedLogin = ['info', 'register', 'login','scan'];

    protected $reqPostUrl = "https://yd.baimalive.com/WeChatQRLogin/weixinPay/native.php";

    /**
     * 用户首页
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {

        $userId = session('user.id') ?? session('user.uid');

        $aiUser = \plugin\ai\app\service\User::getOrCreateUser($userId);

        \plugin\ai\app\service\User::resetBalanceIfExpired($userId);
        $expired = false;
        $isVip = \plugin\ai\app\service\User::isVip($userId, $expired);

        $balance = $aiUser->balance;
        if ($isVip) {
            $aiUser->vipStr = '<sapn class="text-success">已开通</sapn>';
            $aiUser->vip = 1;
            if (config('plugin.ai.env.unlimited', false)) {
                $availableDays = ceil((strtotime($aiUser->expired_at) - time()) / 86400);
                foreach ($aiUser->balance as $type => $count) {
                    if ($count/$availableDays >= 90) {
                        $balance[$type] = '无限';
                    }
                }
            }
        } else {
            $aiUser->vip = 0;
            $aiUser->vipStr = $expired ? '<span class="text-danger">已过期</span>' : '<span class="text-danger">未开通</span>';
        }
        $user = session('user');

        $userInfo = \plugin\admin\app\model\User::find($userId,['id','score','money']);

        return view('user/index', [
            'aiUser' => $aiUser,
            'modelTypes' => Common::getModelTypes(),
            'user' => $user,
            'userinfo' => $userInfo,
            'balance' => $balance,
            'vipEnabled' => Setting::getSetting()['enable_payment'] ?? false
        ]);
    }

    /**
     * 会员支付
     *
     * @param Request $request
     * @return Response
     */
    public function vip(Request $request): Response
    {
        if (!session('user')) {
            return redirect('/app/ai/user/login?redirect=' . urlencode($request->uri()));
        }
        if (!class_exists(Pay::class)) {
            return \response('支付功能需要执行命令 composer require -W yansongda/pay:~3.3.0 并重启');
        }
        return view('user/vip');
    }

    /**
     * 会员充值
     *
     * @param Request $request
     * @return Response
     */
    public function recharge(Request $request): Response
    {
        $data = Db::connection('mysql2')->table('tp_package')->where('status','=',1)->where('type','=',1)->get()->toJson(JSON_UNESCAPED_UNICODE);
        $data1 = Db::connection('mysql2')->table('tp_package')->where('status','=',1)->where('type','=',2)->get()->toJson(JSON_UNESCAPED_UNICODE);
        return view('user/recharge',[
            'data' => $data,
            'data1' => $data1,
        ]);
    }


    /**
     * 教程指导
     *
     * @param Request $request
     * @return Response
     */
    public function classs(Request $request): Response
    {
        return view('user/classs',[]);
    }



    /**
     * 会员充值
     *
     * @param Request $request
     * @return Response
     */
    public function recharge_pay(Request $request): Response
    {

        $proId = (int)$request->get('id');
        $name = $request->get('name');
        $price = $request->get('price');
        $type = $request->get('type');


        $arr['id'] = $proId;
        $arr['name'] = $name;
        $arr['price'] = $price;
        $arr['type'] = $type;
        return view('user/recharge_pay',$arr);
    }

    public function get_order_str($num = 22){
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $string = time();
        for ($len = $num; $len >= 1; $len--) {
            $position = rand() % strlen($chars);
            $position2 = rand() % strlen($string);
            $string = substr_replace($string, substr($chars, $position, 1), $position2, 0);

        }
        return $string;
    }


    /**
     * 创建支付订单号
     *
     * @param Request $request
     * @return Response
     */
    public function create_order(Request $request): Response
    {
        $proId = (int)$request->post('id');
        $type = (int)$request->post('type');
        $userId = session('user.id') ?? session('user.uid');
        $data = Db::connection('mysql2')->table('tp_package')->where('id','=',$proId)->first();


        $time = time();
        $order = 'wxPay_'.$this->get_order_str(12);
        $p['totalFee'] = $data->price;
        $p['orderName'] = $data->name;
        $p['price'] = $data->price;
        $p['package_id'] = $data->id;
        $p['outTradeNo'] = $order;
        $p['createTime'] = $time;
        $p['create_time'] = $time;
        $p['system_user_id'] = $userId;
        $p['status'] = 0; // 待支付
        $p['type'] = $type;
        $p['number'] = 1;
        $p['totalIntegral'] = $data->shop_count;
        $p['time_expire'] = 0;
        $inresu = Db::connection('mysql2')->table('tp_pay')->insert($p);
        if ($inresu){
            $result = $this->httpRequest($this->reqPostUrl,['order'=>$order]);
            $res = $this->jsonData($result);
            return json($res);
        }
    }

    // 查询是否有openid
    public function getInfoByOrder($scan){
        return Db::connection('mysql2')->table('tp_pay')->where('outTradeNo', $scan)->first();
    }

    // 定时查询支付状态
    public function check_pay(Request $request): Response
    {

        if ($request->method() === 'POST') {
            // 实现登录注册逻辑
            $order = trim($request->post('scene_str', ''));
            $resOpenid =  $this->getInfoByOrder($order);

//            $order = new AiOrder();
//            $orderRes = $order->where('order_id',$order)->first();
//            if ($orderRes){
//                return json(['code' => 0,'msg'=>'44444','time'=>time(),'od'=>$resOpenid]);
//            }


            // 创建模型
            $userid = $resOpenid->system_user_id;



            if (empty($resOpenid->outTradeNo)){
                return json(['code' => 0,'msg'=>'无法找到订单号','time'=>time()]);
            }
            if ($resOpenid->status == 0){
                return json(['code' => 0,'msg'=>'未扫码付款','time'=>time(),'od'=>$resOpenid]);
            }

            // 支付成功
            if ($resOpenid->status == 2){

                // 判断订单是否存在
                $order = new AiOrder();
                $orderRes = $order->where('order_id',$order)->first();
                if (!$orderRes){
                    // 付款成功，添加积分

                    $userAi = new AiUser();
                    // 假设你要更新用户 ID 为 1 的用户
                    $userId = $userid;
                    $incrementValue = $resOpenid->totalIntegral; // 要添加的数字
                    // 获取当前设置
                    $userRes = $userAi->where('user_id', $userId)->first();
                    if ($userRes) {
                        // 将 settings 字段解码为数组
                        $settings = $userRes->balance;
                        // 检查 points 是否存在，如果存在则增加值
                        if (isset($settings['gpt3'])) {
                            $settings['gpt3'] += $incrementValue;
                        } else {
                            // 如果 points 不存在，可以选择初始化
                            $settings['gpt3'] = $incrementValue;
                        }
                        // 将数组编码回 JSON
                        $newSettings = json_encode($settings);
                        // 更新数据库
                        $userAi->where('user_id', $userId)->update(['balance' => $newSettings]);
                    } else {
                        return json(['code'=>0,'msg'=>'用户不存在，请联系客服']);
                    }

                    // 添加积分记录
                    $order = new AiOrder();
                    $order->order_id = $resOpenid->outTradeNo;
                    $order->user_id = $userid;
                    $order->total_amount = $resOpenid->totalFee;
                    $order->paid_amount = $resOpenid->totalFee;
                    $order->state = 'paid';
                    $order->created_at = time();
                    $order->payment_method = 'wechat';
                    $order->save();

                    // 扫码付款支付业务
                    return json(['code'=>1,'msg'=>'支付成功','order'=>$resOpenid->outTradeNo]);
                }else{
                    return json(['code'=>1,'msg'=>'已支付，请勿重复支付','order'=>$resOpenid->outTradeNo]);
                }





            }


        }

    }


    function httpRequest($url, $data = "") {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        $headers = [
            'Accept: *',
            'Content-Type: application/json; charset=utf-8',
            'User-Agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36',
        ];
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }

    // 数据格式化
    public function jsonData($urlData){
        return json_decode($urlData,JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * 扫码登录
     *
     * @param Request $request
     * @return Response
     */
    public function scan(){
//        $us = new WaUsers();
//        $res = $us->where('openid','=','ooD536tasO402TC6BwfqVFtOEd-I')->find();
//        return $res;
        return view('user/scan');
    }

    /**
     * 获取用户信息
     * @param Request $request
     * @return Response
     * @throws Exception
     */
    public function info(Request $request): Response
    {
        $userId = session('user.id') ?? session('user.uid');
        $data = [
            'userid' => $userId,
            'username' => '',
            'nickname' => '',
            'avatar' => '/app/ai/avatar/user.png',
            'vip' => false,
            'vipExpiredAt' => '',
            'sid' => $request->session()->getId(),
            'apikey' => config('plugin.webman.push.app.app_key')
        ];
        if ($userId) {
            $aiUser = AiUser::where('user_id', $userId)->first();
            if ($aiUser) {
                $data['vip_expired_at'] = $aiUser->expired_at;
                $data['vip'] = $aiUser->expired_at && time() < strtotime($aiUser->expired_at);
            }
            $user = session('user');
            $data['username'] = $user['username'];
            $data['nickname'] = $user['nickname'];
            $data['avatar'] = $user['avatar'];
        }
        return $this->json(0, 'ok', $data);
    }

    /**
     * 注册
     *
     * @return Response
     */
    public function register(): Response
    {
        $settings = Register::getSetting();
        return view('user/register', [
            'settings' => $settings
        ]);
    }

    /**
     * 登录
     *
     * @return Response
     */
    public function login(): Response
    {
       /* $userData =  \plugin\user\app\model\User::where('openid', "1111");
        $users = $userData->get();
        $id = 0;
        if (count($users) == 0){
            $id = 1;
        }
        return json(['code' => 0,'msg'=>$id]);*/
//        return ;
        if (!class_exists(User::class)) {
            return \response('用户功能需要在 <a target="_blank" href="https://www.workerman.net/app/view/admin">webman/admin后台</a> 安装 <a target="_blank" href="https://www.workerman.net/app/view/user">用户模块</a>');
        }
        return view('user/login', [
            'name' => 'user'
        ]);
    }

}
