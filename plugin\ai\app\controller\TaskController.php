<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\controller;

use Exception;
use plugin\ai\api\Task;
use plugin\ai\app\event\data\EventData;
use plugin\ai\app\event\data\ModelRequestData;
use plugin\ai\app\event\data\ModelResponseData;
use plugin\ai\app\model\AiMessage;
use plugin\ai\app\service\Common;
use plugin\ai\app\service\User;
use plugin\ai\app\model\AiImage;
use support\Log;
use support\Request;
use support\Response;
use Throwable;
use Webman\Event\Event;
use Webman\Push\Api;
use Workerman\Protocols\Http\Chunk;

/**
 * midjourney画图模块
 */
class TaskController extends Base
{

    /**
     * 不需要登录的方法
     *
     * @var string[]
     */
    protected $noNeedLogin = ['imagine', 'notify', 'status', 'action', 'settings'];

    /**
     * 作图
     * @param Request $request
     * @return Response
     * @throws Exception
     */
    public function imagine(Request $request): ?Response
    {
        $imageData = $request->post('image');
        $prompt = $imageData['prompt'] ?? '';
        if (!$prompt || !is_string($prompt) || !trim($prompt)) {
            return json(['error' => ['message' => "请输入图片描述"]]);
        }
        $prompt = $this->formatPrompt($imageData, $prompt);
        $model = $imageData['model'] ?? 'midjourney';
        if ($error = static::tryReduceBalance('midjourney')) {
            return json(['error' => ['message' => $error]]);
        }

        $userId = session('user.id') ?? session('user.uid');
        $sessionId = $request->sessionId();
        $remoteIp = $request->getRealIp();

        $connection = $request->connection;
        $modelRequestData = new ModelRequestData();
        $modelRequestData->data = [
            'model' => $model,
            'images' => $imageData['refs'] ?? [],
            'notifyUrl' => $request->header('X-Forwarded-Proto', 'http') . '://' . $request->host() . '/app/ai/task/notify',
            'prompt' => $prompt,
            'allowFast' => $userId && User::isVip($userId),
        ];
        $modelRequestData->options = [
            'complete' => function($json) use ($connection, $userId, $sessionId, $remoteIp, $model, $modelRequestData, $imageData) {
                $responseData = new ModelResponseData();
                $responseData->data = $json;
                $responseData->modelRequestData = $modelRequestData;
                Event::dispatch('ai.task.imagine.response', $responseData);
                $content = json_encode($responseData->data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $connection->send(new Chunk($content));
                $connection->send(new Chunk(''));

                if (isset($json['error']) || ($json['code'] ?? 0)) {
                    Log::error($content);
                    $modelType = Common::getModelType($model);
                    if ($userId && User::getBalance($userId, $modelType) > 0) {
                        User::addBalance($userId, $modelType);
                    }
                } else {
                    $taskId = $json['taskId'];
                    $image = new AiImage();
                    $image->user_id = $userId;
                    $image->session_id = $sessionId;
                    $image->task_id = $taskId;
                    $image->model = $model;
                    $image->prompt = $imageData['text'] ?? '';
                    $image->image = json_encode($imageData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                    $image->ip = $remoteIp;
                    $image->type = 'IMAGINE';
                    $image->save();
                }
            }
        ];
        Event::dispatch('ai.task.imagine.request', $modelRequestData);
        Task::imagine($modelRequestData->data, $modelRequestData->options);

        // 向浏览器发送头部响应
        return response("\n")->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    public function action(Request $request): ?Response
    {
        $imageData = $request->post('image');
        $model = $imageData['model'] ?? 'midjourney';
        $taskId = $request->post('taskId');
        $customId = $request->post('customId');
        $userId = session('user.id') ?? session('user.uid');
        $sessionId = $request->sessionId();
        $remoteIp = $request->getRealIp();

        $prompt = $imageData['prompt'] ?? '';
        $needReduceBalance = !strpos($customId, '::upsample::');
        if ($needReduceBalance && $error = static::tryReduceBalance('midjourney')) {
            return json(['error' => ['message' => $error]]);
        }
        $prompt = $this->formatPrompt($imageData, $prompt);

        $connection = $request->connection;
        $modelRequestData = new ModelRequestData();
        $modelRequestData->data = [
            'model' => $model,
            'taskId' => $taskId,
            'customId' => $customId,
            'prompt' => $prompt,
            'mask' => $request->post('mask'),
            'allowFast' => $userId && User::isVip($userId),
            'notifyUrl' => $request->header('X-Forwarded-Proto', 'http') . '://' . $request->host() . '/app/ai/task/notify',
        ];
        $modelRequestData->options = [
            'complete' => function($json) use ($connection, $modelRequestData, $userId, $sessionId, $imageData, $remoteIp, $model, $needReduceBalance) {
                $responseData = new ModelResponseData();
                $responseData->data = $json;
                $responseData->modelRequestData = $modelRequestData;
                Event::dispatch('ai.task.action.response', $responseData);

                $content = json_encode($responseData->data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $connection->send(new Chunk($content));
                $connection->send(new Chunk(''));

                if (isset($json['error']) || ($json['code'] ?? 0)) {
                    Log::error($content);
                    $modelType = Common::getModelType($model);
                    if ($needReduceBalance && $userId && User::getBalance($userId, $modelType) > 0) {
                        User::addBalance($userId, $modelType);
                    }
                } else {
                    $taskId = $json['taskId'];
                    $image = new AiImage();
                    $image->user_id = $userId;
                    $image->session_id = $sessionId;
                    $image->task_id = $taskId;
                    $image->model = $model;
                    $image->prompt = $imageData['text'] ?? '';
                    $image->image = json_encode($imageData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                    $image->ip = $remoteIp;
                    $image->save();
                }
            }
        ];
        Event::dispatch('ai.task.action.request', $modelRequestData);
        Task::action($modelRequestData->data, $modelRequestData->options);

        // 向浏览器发送头部响应
        return response("\n")->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    public function status(Request $request): ?Response
    {
        $model = $request->get('model', 'midjourney');
        $taskId = $request->get('taskId');

        $connection = $request->connection;
        $modelRequestData = new ModelRequestData();
        $modelRequestData->data = [
            'model' => $model,
            'taskId' => $taskId
        ];
        $modelRequestData->options = [
            'complete' => function($json) use ($connection, $modelRequestData, $taskId) {
                $responseData = new ModelResponseData();
                $responseData->data = $json;
                $responseData->modelRequestData = $modelRequestData;
                Event::dispatch('ai.task.status.response', $responseData);
                $progress = $responseData->data['data']['progress'] ?? '';
                $url = $responseData->data['data']['imageUrl'] ?? '';
                $smallUrl = $responseData->data['data']['smallUrl'] ?? '';
                $thumbUrl = $responseData->data['data']['thumbUrl'] ?? '';
                if ($progress === '100%' && $url) {
                    $image = AiImage::where('task_id', $taskId)->first();
                    if ($image && !$image->image_url) {
                        $image->image_url = $image->image_url ?: $url;
                        $image->small_url = $image->small_url ?: $smallUrl;
                        $image->thumb_url = $image->thumb_url ?: $thumbUrl;
                        $image->data = json_encode($responseData->data['data'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                        $image->type = $responseData->data['data']['action'];
                        $image->save();
                    }
                }
                if ($responseData->data['data']['failReason']) {
                    $image = AiImage::where('task_id', $taskId)->first();
                    if ($image) {
                        $image->data = json_encode($responseData->data['data'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                        $image->type = $responseData->data['data']['action'];
                        $image->save();
                        $userId = $image->user_id;
                        $modelType = Common::getModelType($image->model);
                        if ($userId && User::getBalance($userId, $modelType) > 0) {
                            User::addBalance($userId, $modelType);
                        }
                    }
                }
                $content = json_encode($responseData->data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                if (isset($responseData->data['error']) || ($responseData->data['code'] ?? 0)) {
                    Log::error($content);
                }
                $connection->send(new Chunk($content));
                $connection->send(new Chunk(''));
            }
        ];

        Event::dispatch('ai.task.status.request', $modelRequestData);
        Task::status($modelRequestData->data, $modelRequestData->options);

        // 向浏览器发送头部响应
        return response("\n")->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    /**
     * 接收midjourney_porxy代理发来的通知
     * @param Request $request
     * @return Response
     */
    public function notify(Request $request): Response
    {
        $eventData = new EventData($request->post());
        Event::dispatch('ai.task.notify', $eventData);
        $data = $eventData->data;
        $progress = $data['progress'] ?? '';
        $url = $data['imageUrl'] ?? '';
        $smallUrl = $data['smallUrl'] ?? '';
        $thumbUrl = $data['thumbUrl'] ?? '';
        $taskId = $data['id'] ?? '';
        if ($progress === '100%' && $url) {
            $image = AiImage::where('task_id', $taskId)->first();
            if ($image && !$image->image_url) {
                $image->image_url = $image->image_url ?: $url;
                $image->small_url = $image->small_url ?: $smallUrl;
                $image->thumb_url = $image->thumb_url ?: $thumbUrl;
                $image->data = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $image->type = $data['action'];
                $image->save();
            }
        }
        if ($data['failReason']) {
            $image = AiImage::where('task_id', $taskId)->first();
            if ($image) {
                $image->data = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $image->type = $data['action'];
                $image->save();
                $userId = $image->user_id;
                $modelType = Common::getModelType($image->model);
                if ($userId && User::getBalance($userId, $modelType) > 0) {
                    User::addBalance($userId, $modelType);
                }
            }
        }

        try {
            $api = new Api(
                'http://127.0.0.1:3232',
                config('plugin.webman.push.app.app_key'),
                config('plugin.webman.push.app.app_secret')
            );
            $api->trigger($data['state'], 'mj-state-change', $data);
        } catch (Throwable $e) {}
        return response(json_encode([
            'code' => 0,
            'msg' => 'ok'
        ]));
    }

    /**
     * @return Response
     */
    public function settings(): Response
    {
        $model = Common::getModelInfo('midjourney');
        $settings = $model->settings;
        $userId = session('user.id') ?? session('user.uid');
        return json([
            'code' => 0,
            'msg' => 'ok',
            'data' => [
                'allowAllFast' => ($settings['allowAllFast']['value'] ?? false) || User::isVip($userId),
                'hideSpeedOptions' => $settings['hideSpeedOptions']['value'] ?? false,
            ]
        ]);
    }

    /**
     * 格式化提示词
     * @param $image
     * @param $text
     * @return string
     */
    protected function formatPrompt($image, $text) {
        $params = "";
        if ($text) {
            $params .= $text;
        }
        if (!empty($image['styles'])) {
            $params .= ". " . implode(", ", $image['styles']);
        }
        $prompt = $image['prompt'] ?? '';
        $prompt = preg_replace(['/--fast/i', '/--relax/'], '', $prompt);
        if (strpos($prompt, '--ar') === false) {
            if (isset($image['ar']) && $image['ar'] !== "customized") {
                if ($image['ar'] !== "1:1") {
                    $params .= " --ar " . $image['ar'];
                }
            } else if (!empty($image['widthRatio']) && !empty($image['heightRatio'])) {
                $params .= " --ar " . $image['widthRatio'] . ":" . $image['heightRatio'];
            }
        }
        if (strpos($prompt, '--c') === false) {
            if (!empty($image['chaos'])) {
                $params .= " --c " . $image['chaos'];
            }
        }
        if (strpos($prompt, '--s') === false) {
            if (isset($image['stylize']) && $image['stylize'] !== 100) {
                $params .= " --s " . $image['stylize'];
            }
        }
        if (strpos($prompt, '--iw') === false) {
            if (!empty($image['refs'])) {
                $params .= " --iw " . $image['iw'];
            }
        }
        if (strpos($prompt, '--zoom') === false) {
            if (isset($image['zoom'])) {
                $params .= " --zoom " . $image['zoom'];
                $image['zoom'] = "";
            }
        }
        if (strpos($prompt, '--niji') === false) {
            if (isset($image['model']) && $image['model'] === "niji") {
                $params .= " --niji";
            }
        }
        if (isset($image['others'])) {
            $params .= " " . $image['others'];
        }
        if (strpos($prompt, '--v') === false) {
            if (isset($image['version'])) {
                $params .= " --v " . $image['version'];
            }
        }
        $params .= !empty($image['fast']) ? ' --fast' : ' --relax';
        return $params;
    }

}