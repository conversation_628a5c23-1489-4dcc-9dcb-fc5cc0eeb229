<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\api;

use plugin\ai\app\model\AiModel;
use plugin\ai\app\service\User as UserService;

class User extends Base
{
    /**
     * 是否是vip
     * @param $userId
     * @return bool
     */
    public static function isVip($userId): bool
    {
        return UserService::isVip($userId);
    }

    /**
     * 根据套餐数据增加余额
     * @param $userId
     * @param $planData
     * @return void
     */
    public static function addBalanceByPlanData($userId, $planData)
    {
        $months = $planData['months'] ?? 0;
        $days = $months ? 0 : ($planData['days'] ?? 0);
        if ($months) {
            static::vipMonthsIncrease($userId, $months);
        } else if ($days) {
            static::vipDaysIncrease($userId, $days);
        }
        static::addBalance($userId, $planData);
    }

    /**
     * 增加余额
     * @param $userId
     * @param $balance
     * @return void
     */
    public static function addBalance($userId, $balance)
    {
        UserService::resetBalanceIfExpired($userId);
        $user = UserService::getOrCreateUser($userId);
        $oldBalance = $user->balance ?: [];
        $modelTypes = AiModel::pluck('type')->toArray();
        foreach ($balance as $key => $value) {
            if (in_array($key, $modelTypes)) {
                $oldBalance[$key] = ($oldBalance[$key] ?? 0) + $value;
            }
        }
        $user->balance = json_encode($oldBalance, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $user->save();
    }

    /**
     * 增加VIP月数
     * @param $userId
     * @param $months
     * @return void
     */
    public static function vipMonthsIncrease($userId, $months)
    {
        UserService::resetBalanceIfExpired($userId);
        $user = UserService::getOrCreateUser($userId);
        $hasExpired = !$user->expired_at || strtotime($user->expired_at) < time();
        $startTimestamp = strtotime($hasExpired ? date('Y-m-d H:i:s') : $user->expired_at);
        $expirationTimestamp = strtotime("+" . $months . " months", $startTimestamp);
        $user->expired_at = date("Y-m-d H:i:s", $expirationTimestamp);
        $user->save();
    }

    /**
     * 增加VIP天数
     * @param $userId
     * @param $days
     * @return void
     */
    public static function vipDaysIncrease($userId, $days)
    {
        UserService::resetBalanceIfExpired($userId);
        $user = UserService::getOrCreateUser($userId);
        $hasExpired = !$user->expired_at || strtotime($user->expired_at) < time();
        $startTimestamp = strtotime($hasExpired ? date('Y-m-d H:i:s') : $user->expired_at);
        $expirationTimestamp = strtotime("+" . $days . " days", $startTimestamp);
        $user->expired_at = date("Y-m-d H:i:s", $expirationTimestamp);
        $user->save();
    }
}