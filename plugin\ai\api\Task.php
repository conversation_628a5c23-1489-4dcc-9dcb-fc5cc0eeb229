<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\api;

class Task extends Base
{
    public static function imagine($data, $options)
    {
        static::call('imagine', $data, $options);
    }

    public static function action($data, $options)
    {
        static::call('action', $data, $options);
    }

    public static function status($data, $options)
    {
        static::call('status', $data, $options);
    }
}