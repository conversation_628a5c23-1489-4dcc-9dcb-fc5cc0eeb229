<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */


namespace plugin\ai\app\service;

use plugin\admin\app\model\Option;

/**
 * 听写朗读线管
 */
class Audio extends Base
{
    /**
     * options表对应的name字段
     */
    const OPTION_NAME = 'plugin_ai.audio-setting';

    /**获取配置
     *
     * @return array|mixed
     */
    public static function getSetting($name = '', $default = null)
    {
        $setting = Option::where('name', static::OPTION_NAME)->value('value');
        $setting = $setting ? json_decode($setting, true) : [];
        if (!$setting) {
            $setting = [
                'enable_xunfei_iat' => true,
                'xunfei_iat_appid' => '',
                'xunfei_iat_apikey' => '',
                'xunfei_iat_secret_key' => '',
            ];
            static::saveSetting($setting);
        }
        return $name ? $setting[$name] ?? $default : $setting;
    }
}