<?php

namespace plugin\ai\app\admin\controller;

use support\Redis;
use support\Request;
use support\Response;
use plugin\ai\app\model\AiEmbedding;
use plugin\admin\app\controller\Crud;
use support\exception\BusinessException;

/**
 * AI训练数据 
 */
class AiEmbeddingController extends Crud
{
    
    /**
     * @var AiEmbedding
     */
    protected $model = null;

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->model = new AiEmbedding;
    }
    
    /**
     * 浏览
     * @return Response
     */
    public function index(): Response
    {
        return view('ai-embedding/index');
    }

    /**
     * 插入
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function insert(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return parent::insert($request);
        }
        return view('ai-embedding/insert');
    }

    /**
     * 更新
     * @param Request $request
     * @return Response
     * @throws BusinessException
    */
    public function update(Request $request): Response
    {
        if ($request->method() === 'POST') {
            // 更新数据集时，如果数据集ID变了，需要删除redis里的数据
            $id = $request->post('id');
            $embedding = AiEmbedding::find($id);
            if ($embedding) {
                $newDatasetId = $request->post('dataset_id');
                if ($embedding->dataset_id != $newDatasetId) {
                    $key = "ai-embedding-$embedding->dataset_id:$embedding->id";
                    Redis::connection('plugin.ai.default')->del($key);
                    if ($embedding->text_embedding) {
                        $key = "ai-embedding-$newDatasetId:$embedding->id";
                        $value = [
                            "id" => $embedding->id,
                            "text_embedding" => $embedding->text_embedding
                        ];
                        Redis::connection('plugin.ai.default')->rawCommand('JSON.SET', $key, '$', json_encode($value, JSON_UNESCAPED_UNICODE));
                    }
                }
            }
            return parent::update($request);
        }
        return view('ai-embedding/update');
    }

    /**
     * 删除
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function delete(Request $request): Response
    {
        $id = $request->post('id');
        $embeddings = AiEmbedding::whereIn('id', (array)$id)->get();
        foreach ($embeddings as $embedding) {
            $key = "ai-embedding-$embedding->dataset_id:$embedding->id";
            Redis::connection('plugin.ai.default')->del($key);
        }
        return parent::delete($request);
    }
}
