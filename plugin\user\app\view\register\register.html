<?=plugin\user\api\Template::header('用户注册')?>

<?=plugin\user\api\Template::nav()?>

<div class="container">
    <div class="row justify-content-center d-flex align-items-center">

        <div style="width: 380px;" class="my-4">

            <form method="post">
                <h3 class="mb-3">注册2</h3>
                <?php if($settings['nickname_enable']){ ?>
                <div class="form-group">
                    <input type="text" name="nickname" class="form-control"  placeholder="昵称" required>
                </div>
                <?php } ?>
                <div class="form-group">
                    <input type="text" name="username" class="form-control" placeholder="用户名" required>
                </div>
                <div class="form-group">
                    <input type="password" name="password" class="form-control" placeholder="密码" required>
                </div>
                <div class="form-group">
                    <input type="password" name="password_confirm" class="form-control" placeholder="重复密码" required>
                </div>
                <?php if($settings['email_enable']){ ?>
                <div class="form-group">
                    <input type="email" name="email" class="form-control"  placeholder="邮箱" required>
                </div>
                <?php if($settings['email_verify']){ ?>
                <div class="form-group d-flex justify-content-between">
                    <input type="text" name="email_code" class="form-control w-50" placeholder="输入邮箱验证码" required>
                    <button type="button" id="send_email_code_btn" class="btn btn-primary btn-block ml-2 w-50">获取邮箱验证码</button>
                </div>
                <?php } ?>
                <?php } ?>
                <?php if($settings['mobile_enable']){ ?>
                <div class="form-group">
                    <input type="tel" name="mobile" class="form-control"  placeholder="手机" required>
                </div>
                <?php if($settings['mobile_verify']){ ?>
                <div class="form-group d-flex justify-content-between">
                    <input type="text" name="mobile_code" class="form-control w-50" autocomplete="off" placeholder="输入手机验证码" required>
                    <button type="button" id="send_mobile_code_btn" class="btn btn-primary btn-block ml-2 w-50">获取手机验证码</button>
                </div>
                <?php } ?>
                <?php } ?>
                <?php if($settings['captcha_enable']){ ?>
                <div class="form-group d-flex justify-content-between">
                    <input type="text" name="image_code" class="form-control w-50" autocomplete="off" placeholder="输入图形验证码" required>
                    <img class="rounded" src="/app/user/captcha/image/register"/>
                </div>
                <?php } ?>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block w-100">注册</button>
                </div>

                <div class="form-group d-flex justify-content-between">
                    <a class="text-decoration-none" id="loginLink" href="/app/user/login">已有账号？点这里登录</a>
                    <a class="text-decoration-none" href="/app/user/password/reset">找回密码</a>
                </div>
            </form>
        </div>

    </div>

</div>

<style>
    html, body {
        height: 90%;
    }
    .container, .row {
        height: 100%;
    }
</style>

<script>

    // email mobile 验证码
    ["email", "mobile"].forEach(function (item) {
        $("#send_"+item+"_code_btn").on('click', function () {
            let btn = $(this);
            let input = $("input[name='"+item+"']");
            $("input[name='"+item+"_code']").val("");
            let value = input.val();
            if (!value){
                input.addClass("is-invalid").focus();
                return;
            }
            let data = {};
            data[item] = value;
            btn.attr('disabled', true);
            if (item === 'mobile') {
                let num = 60;
                let id = setInterval(function () {
                    btn.html("获取手机验证码(" + num + ")");
                    if (--num <= 0) {
                        clearInterval(id);
                        btn.attr("disabled", false);
                        btn.html("获取手机验证码");
                    }
                }, 1000);
            }
            $.ajax({
                url: "/app/user/captcha/"+item+"/register",
                type: "POST",
                data: data,
                success: function (res) {
                    if (res.code) {
                        input.addClass("is-invalid").focus();
                        return webman.error(res.msg);
                    }
                    webman.success('发送成功');
                },
                complete: function () {
                    if (item === "email") {
                        btn.attr('disabled', false);
                    }
                }
            });
        });
    });

    // 点击切换图形验证码
    $('form img').on("click", function (e) {
        e.stopPropagation();
        e.preventDefault();
        $(this).attr("src", "/app/user/captcha/image/register?r="+ Math.random());
        $('input[name="image_code"]').val('');
    });

    // 提交注册
    $('form').submit(function(event) {
        event.preventDefault();
        if ($('input[name="password"]').val() !== $('input[name="password_confirm"]').val()) {
            return webman.error("两次密码不一致");
        }
        $.ajax({
            url: "/app/user/register",
            type: "POST",
            dataType: "json",
            data: $(this).serialize(),
            success: function (e) {
                if (e.code !== 0) {
                    let field = e.data ? e.data.field : false;
                    field !== 'image_code' && $('form img').trigger('click');
                    field && $('input[name="'+field+'"]').addClass('is-invalid').focus();
                    return webman.error(e.msg);
                }
                webman.success('注册成功', function() {
                    location.href = "/app/user/login" + location.search;
                });
            }
        });
    });

    // 更新数据移除红框
    $('input').keyup(function () {
        $(this).removeClass('is-invalid');
    });

    $("#loginLink").attr('href', '/app/user/login' + location.search);

</script>

<?=plugin\user\api\Template::footer()?>
