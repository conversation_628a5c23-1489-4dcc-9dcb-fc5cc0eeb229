.index_container {
    box-sizing: border-box;
    display: flex;
    flex: 1;
    flex-basis: auto;
    flex-direction: row;
    min-width: 0
}
.flex_column {
    flex-direction: column
}
.bg-white {
    background-color: var(--color-white)
}
.creation-container[index_1] {
    background: radial-gradient(farthest-side at 100% 0,var(--el-color-primary-light-9) 0,#fff 40%)
}

.cunstom-header[index_1] {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%
}

.cunstom-header .header-1[index_1] {
    background: linear-gradient(90deg,#43cea2,#2d929f 50%,#185a9d);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-size: 30px;
    font-weight: 800;
    margin: 0 0 20px
}

.cunstom-header .header-2[index_1] {
    font-size: 16px;
    margin: 0 0 15px
}
