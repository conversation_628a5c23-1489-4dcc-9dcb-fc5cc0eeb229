<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/app/ai/css/bootstrap.min.css?v=5.3" rel="stylesheet">
    <link rel="stylesheet" href="/app/ai/layui-v2.7.6/layui/css/layui.css">
    <link href="/app/ai/css/app.css?v=<?=ai_css_version()?>" rel="stylesheet">
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>

    <title>充值界面</title>
</head>
<body data-bs-theme="light">
<div class="header">充值界面</div>

<div class="container overflow-scroll" style="height: calc(100% - 45px)">

    <div class="row">
        <div class="col-12 pt-4">
            <div class="mb-4 card border-0 shadow-sm" style="min-height:80vh">
                <div class="card-body pb-3">
                    <div class="layui-tab" lay-filter="test">
                        <ul class="layui-tab-title">
                            <li lay-id="11">开通会员</li>
                            <li class="layui-this"  lay-id="22">余额充值</li>
                            <li lay-id="33">卡密兑换</li>
                        </ul>


                        <div class="layui-tab-content">
                            <div style="margin-top: 50px" class="layui-tab-item ">
                                <div style="text-align: center">
                                    <div id="openVip" lay-filter="demo" class="layui-row card1">

                                        <?php
                                            // 遍历数组并输出每个键值对
                                            $arr = $data;
                                            $arr = json_decode($arr, true);

                                            $css_str = '';
                                            foreach ($arr as $key => $value) {
                                        ?>
                                        <div id="<?php echo $value['id'];?>" class="layui-col-md3 <?php if($key==0){ echo 'this-thr';};?>">
                                            <span class="money-tip" ><?php echo $value['describe']?></span>
                                            <h5 class="money-font" data-value="<?php echo $value['name'];?>"><?php echo $value['name']?></h5>
                                            <h3 class="money-tag" data-value="<?php echo $value['price'];?>">￥<?php echo $value['price']?></h3>
                                        </div>
                                        <?php
                                            }
                                        ?>

                                    </div>
                                    <div style="text-align: left;margin: 10px;">会员权益</div>
                                    <div class="layui-row card2">
                                        <div class="layui-col-md3" >
                                            <div>
                                                <img style="margin-right: 10px;" width="40px" src="https://hetao-1306534873.cos.ap-shanghai.myqcloud.com/uploads/images/20240118/20240118213842e63e08391.png">
                                            </div>
                                            <div>
                                                <span>清华智谱Pro</span>
                                                <div class="md3name">对话无限用</div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md3" >
                                            <div>
                                                <img style="margin-right: 10px;" width="40px" src="https://hetao-1306534873.cos.ap-shanghai.myqcloud.com/uploads/images/20240118/20240118213935468fa0972.png">
                                            </div>
                                            <div>
                                                <span class="">通义千问</span>
                                                <div class="md3name">对话无限用</div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md3" >
                                            <div>
                                                <img style="margin-right: 10px;" width="40px" src="https://hetao-1306534873.cos.ap-shanghai.myqcloud.com/uploads/images/20240118/20240118214003bd3e68809.png">
                                            </div>
                                            <div>
                                                <span class="">讯飞星火3.1</span>
                                                <div class="md3name">对话无限用</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="margin-top: 20px">
<!--                                        <button data-method="offset" data-type="auto" class="layui-btn layui-btn-normal">居中弹出</button>-->
                                        <button type="button" class="layui-btn" id="nowKaiTong">立即开通</button>
                                    </div>
                                    <div style="font-size: 14px;margin-top: 18px;color: #d1cccc;"> 该服务为虚拟产品，支付成功后不支持退款。 </div>
                                </div>
                            </div>
                            <div class="layui-tab-item layui-show">
                                <div class="layui-bg-gray" style="padding: 30px;">
                                    <div id="Yuchatge" class="layui-row layui-col-space15">
                                        <?php
                                            // 遍历数组并输出每个键值对
                                            $arr1 = $data1;
                                            $arr1 = json_decode($arr1, true);

                                            $css_str = '';
                                            foreach ($arr1 as $key => $va) {
                                        ?>
                                        <div class="layui-col-md4">
                                            <div id="<?php echo $va['id'];?>" class="layui-card <?php if($key==0){ echo 'this-cards';};?>">
                                                <div class="layui-card-body">
                                                    <img class="imginfo" src="https://hetao-1306534873.cos.ap-shanghai.myqcloud.com/uploads/images/20240220/20240220203952673ac3615.jpg">
                                                    <div class="imginfotext">
                                                        <h4 data-value="<?php echo $va['price']?>">￥<?php echo $va['price']?></h4>
                                                        <h6 data-value="<?php echo $va['name']?>" class="title"><?php echo $va['name']?></h6>
                                                        <div class="titledes"><?php echo $va['describe']?></div>
                                                        <div class="titlecli"> 对话次数: <?php echo $va['shop_count']?></div>
<!--                                                        <div class="titlecli"> GTP3: 0次</div>-->
<!--                                                        <div class="titlecli"> GTP4.0: 0次</div>-->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                            }
                                        ?>

                                    </div>
                                    <div style="text-align: center">
                                        <button type="button" class="layui-btn" id="nowchonzhi">立即充值</button>
                                        <div style="font-size: 14px;margin-top: 18px;color: #d1cccc;"> 该服务为虚拟产品，支付成功后不支持退款。 </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item" style="text-align: center">该功能暂未开放，敬请期待</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script src="/app/ai/layui-v2.7.6/layui/layui.js"></script>
</html>
<script>

    // 开通会员
    $(document).ready(function () {

        var $my = $("#openVip div")
        $my.click(function ($this) {
            // console.log($this)
            // 移除this-thr
            $('#openVip .this-thr').removeClass('this-thr');
            $(this).addClass('this-thr')
            // $this.addClass('this-thr')
            // .addClass('new-class');
        })
        var $btnkt = $("#nowKaiTong");
        $btnkt.click(function ($this) {
            // 假设你有一个元素的class为"my-class"
            layer.msg("暂不支持购买福利套餐，请前往余额充值进行积分充值！")
            return false;
            var idValue = $('#openVip .this-thr').attr('id');
            console.log(idValue); // 这会输出该元素的id值
            // layer.msg(idValue)
            var name = $('#openVip .this-thr h5').attr('data-value');
            var price = $('#openVip .this-thr h3').attr('data-value');
            var type = 1;
            layer.open({
                type: 2,
                title:"开通会员",
                area: ['400px', '400px'],
                fixed: false, //不固定
                maxmin: true,
                content: "recharge_pay?id="+idValue+"&name="+name+"&price="+price + "&type="+ type
            });
        });
    })

    // 余额充值
    $(document).ready(function () {
        var $my = $("#Yuchatge div div")
        $my.click(function ($this) {
            // 移除this-thr
            $('#Yuchatge .this-cards').removeClass('this-cards');
            $(this).addClass('this-cards')
        })

        // 余额充值附加套餐
        var $btncz = $("#nowchonzhi")
        $btncz.click(function () {
            // layer.msg("只针对永久会员的福利套餐，请开通永久会员后购买！")
            // return false;

            // 假设你有一个元素的class为"my-class"
            var idValue = $('#Yuchatge .this-cards').attr('id');
            // console.log(idValue); // 这会输出该元素的id值
            // layer.msg(idValue);
            // return false;
            var name = $('#Yuchatge .this-cards h6').attr('data-value');
            var price = $('#Yuchatge .this-cards h4').attr('data-value');
            var type = 2;
            layer.open({
                type: 2,
                title:"余额充值",
                area: ['400px', '400px'],
                fixed: false, //不固定
                maxmin: true,
                content: "recharge_pay?id="+idValue+"&name="+name+"&price="+price + "&type="+ type
            });
        });
    })





</script>
<style>
    .layui-tab-title li {
        border-radius: 10px;
        margin-left: 2px;
        background: #ededed;
        line-height: 60px;
        min-width: 33%;
        padding: 0px 15px;
    }
    .layui-tab-title .layui-this {
        border-radius: 15px;
        background: #43CEA2;
        color: #fff;
    }
    .layui-tab-title {
        height: 60px;
        border-bottom-style: hidden;
    }
    .layui-tab-title .layui-this:after {
        border: none;
        height: 61px;
    }
    .layui-card{
        border: 1px solid #e1e1e1;
        height: 350px;
    }

    .layui-bg-gray {
        background-color: #ffffff!important;
    }
    .layui-col-space15>* {
        padding: 15px;
    }

    .layui-col-space15{
        display: flex;
        margin-left: 5%;
    }

    .layui-col-md4 {
        width: 30%;
    }
    .layui-card-body {
        padding: unset;
    }
    .imginfo{
        max-height: 230px;
        width: 100%;
        height: 189px;
        object-fit: cover;
    }
    .imginfotext{
        padding: 15px;
    }
    .imginfotext h4{
        font-size: 24px;
        color: red;
    }
    .title{
        font-family: cursive;
        font-weight: bold;
    }
    .titledes{
        font-size: 13px;
        color: #b3b3b3;
    }
    .titlecli{
        font-size: 12px;
    }
    .layui-btn{
        background: #43CEA2 !important;
    }
    .this-cards{
        border: 1px solid #43cea2;
    }

    .card1 .layui-col-md3{
        border: 1px solid #e0e0e0;
        /*background: #fffaf0;*/
        margin-left: 5px;
        width: 24%;
        height: 110px;
        padding: 5px;
        border-radius: 10px;
        max-width: 200px;
        font-family: fantasy;
        min-width: 120px;
    }
    .card1{
        display: flex;
    }
    .card2 .layui-col-md3{
        border: 1px solid #e0e0e0;
        /*background: #fffaf0;*/
        margin-left: 5px;
        width: 12%;
        height: 86px;
        padding: 5px;
        border-radius: 10px;
        max-width: 200px;
        font-family: fantasy;
        display: inline-flex;
        align-items: center;
        min-width: 120px;
    }
    .card2{
        display: flex;
    }
    .money-tag{
        font-family: fantasy;
    }
    .money-font{
        margin-top: 20px;
    }
    .money-tip{
        min-width: 79px;
        padding: 2px;
        position: absolute;
        /*min-width: 155px;*/
        background: red;
        color: #fff;
        display: block;
        margin-top: -15px;
        margin-left: -6px;
        font-size: 13px;
    }
    .this-thr{
        border: 1px solid #ffb324!important;
        background:#fffaf0;
    }
    .md3name{
        font-size: 13px;
        color: #b3b3b3;
    }

</style>

