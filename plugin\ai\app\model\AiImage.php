<?php

namespace plugin\ai\app\model;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use plugin\admin\app\model\Base;
use plugin\user\api\User;

/**
 * @property integer $id 主键(主键)
 * @property integer $task_id 任务id
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property string $model 模型
 * @property string $type 类型
 * @property string $prompt 提示词
 * @property string $thumb_url 缩略图
 * @property string $small_url 预览图
 * @property string $image_url 原始图
 * @property string $image 图像数据
 * @property mixed $data 结果数据
 * @property integer $user_id 用户id
 * @property string $session_id session_id
 * @property string $ip ip
 */
class AiImage extends Base
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ai_images';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->select('id', 'nickname');
    }

    /**
     * @param $value
     * @return mixed
     */
    public function getDataAttribute($value)
    {
        return $value ? json_decode($value, true) : null;
    }
    
}
