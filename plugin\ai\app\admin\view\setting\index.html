<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AI设置</title>
    <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
    <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
</head>
<body class="pear-container">

<div class="layui-card">
    <div class="layui-card-body">

        <div class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this">通用设置</li>
                <li>Azure设置</li>
                <li>语音设置</li>
                <li>模型列表</li>
                <li>套餐设置</li>
                <li>角色分类设置</li>
                <li>敏感词设置</li>
            </ul>

            <div class="layui-tab-content">

                <div class="layui-tab-item layui-show">
                    <form class="layui-form" lay-filter="setting" onsubmit="return false">
                        <div class="layui-form-item">
                            <label class="layui-form-label">开启支付</label>
                            <div class="layui-input-inline">
                                <input type="checkbox" name="enable_payment" lay-skin="switch">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">必须登录</label>
                            <div class="layui-input-inline">
                                <input type="checkbox" name="need_login" lay-skin="switch">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">默认主题</label>
                            <div class="layui-input-inline">
                                <select name="theme" lay-verify="required">
                                    <option value="light">浅色主题</option>
                                    <option value="dark">深色主题</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">中文翻译</label>
                            <div class="layui-input-inline">
                                <select name="translate_model" lay-verify="required">
                                    <option value="gpt-3.5-turbo">gpt-3.5-turbo</option>
                                    <option value="gpt-4">gpt-4.0</option>
                                    <option value="gpt-4o">gpt-4o（默认选择）</option>
                                    <option value="ernie-bot-turbo">文心一言</option>
                                    <option value="qwen-plus">通义千问</option>
                                    <option value="gemini-pro">gemini</option>
                                    <option value="spark">讯飞星火</option>
                                    <option value="deepseek-chat">DeepSeek</option>
                                </select>
                            </div>
                            <div class="layui-form-mid layui-font-orange">供Midjourney等需要翻译的应用使用</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">翻译提示词</label>
                            <div class="layui-input-inline">
                                <input type="text" name="translate_prompt" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">GPT伪装</label>
                            <div class="layui-input-inline">
                                <input type="text" name="gpt_mask" autocomplete="off" class="layui-input" placeholder="请记住你是webman公司开发的大模型叫webman-ai,版本号是{version}。除此之外不要暴露关于自己的任何信息。">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站标题</label>
                            <div class="layui-input-inline">
                                <input type="text" name="site_title" placeholder="网站标题" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站介绍</label>
                            <div class="layui-input-inline">
                                <input type="text" name="site_desc" placeholder="网站介绍" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">ICP备案</label>
                            <div class="layui-input-inline">
                                <input type="text" name="icp" placeholder="ICP备案号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">公安网备</label>
                            <div class="layui-input-inline">
                                <input type="text" name="beian" placeholder="公安网备" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">其它</label>
                            <div class="layui-input-inline">
                                <input type="text" name="footer_txt" placeholder="其它展示在页面底部的内容" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="pear-btn pear-btn-primary" lay-submit="" lay-filter="setting">保 存</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Azure设置 -->
                <div class="layui-tab-item">
                    <form class="layui-form" lay-filter="azure" onsubmit="return false">
                        <div class="layui-form-item">
                            <label class="layui-form-label">开启</label>
                            <div class="layui-input-inline">
                                <input type="checkbox" name="enable" lay-skin="switch">
                            </div>
                            <div class="layui-form-mid layui-font-orange" style="margin-left:7rem">开启后映射的模型将走Azure OpenAI接口，而不是OpenAI</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">接口地址</label>
                            <div class="layui-input-inline">
                                <input type="text" name="api_host" required  lay-verify="required" autocomplete="off" style="width:22em;" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">密钥1</label>
                            <div class="layui-input-inline">
                                <input type="text" name="apikey1" required  lay-verify="required" autocomplete="off" style="width:22em;" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">密钥2</label>
                            <div class="layui-input-inline">
                                <input type="text" name="apikey2" required  autocomplete="off" style="width:22em;" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">模型映射</label>
                            <div class="layui-input-inline">
                                <textarea name="map" placeholder="json数据" style="width:22em;height:20em;" class="layui-textarea"></textarea>
                            </div>
                            <span>
                <div class="layui-font-orange">示例值</div>
                <pre class="layui-font-orange" style="line-height: 135%">
{
  "gpt-3.5-turbo": "gpt-35-turbo",
  "gpt-3.5-turbo-0613": "gpt-35-turbo",
  "gpt-3.5-turbo-16k": "gpt-35-turbo",
  "gpt-3.5-turbo-1106": "gpt-35-turbo",
  "gpt-4": "gpt-4",
  "gpt-4-32k": "gpt-4",
  "gpt-4-vision-preview": "gpt-4-vision",
  "gpt-4-1106-preview": "gpt-4"
}
                </pre>
                <div class="layui-font-orange">含义: "实际模型名":"Azure模型部署名"</div>
              </span>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="pear-btn pear-btn-primary" lay-submit="" lay-filter="azure">保 存</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 语音设置 -->
                <div class="layui-tab-item">
                    <form class="layui-form" lay-filter="audio" onsubmit="return false">

                        <div class="layui-form-item">
                            <label class="layui-form-label">开启讯飞听写</label>
                            <div class="layui-input-inline">
                                <input type="checkbox" name="enable_xunfei_iat" lay-skin="switch">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">讯飞AppID</label>
                            <div class="layui-input-inline">
                                <input type="text" name="xunfei_iat_appid" required  lay-verify="required" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">讯飞ApiKey</label>
                            <div class="layui-input-inline">
                                <input type="text" name="xunfei_iat_apikey" required  lay-verify="required" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">讯飞SecretKey</label>
                            <div class="layui-input-inline">
                                <input type="text" name="xunfei_iat_secret_key" required  lay-verify="required" autocomplete="off" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="pear-btn pear-btn-primary" lay-submit="" lay-filter="audio">保 存</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="layui-tab-item">
                    <form class="layui-form" lay-filter="model" action="" onsubmit="return false">
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">模型配置</label>
                            <div class="layui-input-inline">
                                <textarea name="model" placeholder="json数据" style="width:30em;height:32em;" class="layui-textarea"></textarea>
                            </div>
                            <span>
                <div class="layui-font-orange">提示：模型列表用于设定用户可以看到的模型列表</div>
                <div class="layui-font-orange">示例值</div>
                <pre class="layui-font-orange" style="line-height: 135%">
{
    "gpt-3.5-turbo": "gpt-3.5-turbo",
    "gpt-3.5-turbo-16k": "gpt-3.5-turbo-16k",
    "gpt-4": "gpt-4",
    "gpt-4-32k": "gpt-4-32k",
    "moonshot-v1-8k": "kimi-8k",
    "gpt-4-vision-preview": "gpt-4-vision-preview",
    "gemini-pro": "谷歌Gemini",
    "qwen-plus": "通义千问",
    "ernie-bot-turbo": "文心一言",
    "ernie-bot-4": "文心一言4.0",
    "spark": "讯飞星火",
    "chatglm": "清华智普",
    "midjourney": "Midjourney作图",
    "dall.e": "DALL.E作图"
}
                </pre>
                <div class="layui-font-orange">含义: "实际模型名": "显示模型名"</div>
              </span>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="pear-btn pear-btn-primary" lay-submit="" lay-filter="model">保 存</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="layui-tab-item">
                    <form class="layui-form" lay-filter="plan" action="" onsubmit="return false">
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">套餐配置</label>
                            <div class="layui-input-inline">
                                <textarea name="plan" placeholder="json数据" style="width:30em;height:68em;" class="layui-textarea"></textarea>
                            </div>
                            <span>
                <div class="layui-font-orange">示例值</div>
                <pre class="layui-font-orange" style="line-height: 135%">
{
    "1": {
        "plan": 1,
        "price": 28,
        "months": 1,
        "name": "月度会员",
        "gpt3": 3000,
        "ernie": 3000,
        "qwen": 3000,
        "spark": 3000,
        "chatglm": 3000,
        "gemini": 3000,
        "dalle": 100,
        "midjourney": 100
    },
    "2": {
        "plan": 2,
        "price": 58,
        "months": 3,
        "name": "季度会员",
        "gpt3": 10000,
        "ernie": 10000,
        "qwen": 10000,
        "spark": 10000,
        "chatglm": 10000,
        "gemini": 10000,
        "dalle": 500,
        "midjourney": 500
    },
    "3": {
        "plan": 3,
        "price": 198,
        "months": 12,
        "name": "年度会员",
        "gpt3": 50000,
        "ernie": 50000,
        "qwen": 50000,
        "spark": 50000,
        "chatglm": 50000,
        "dalle": 3000,
        "gemini": 50000,
        "midjourney": 3000
    }
}
                </pre>
                <div class="layui-font-orange">
                  含义: gpt3:gpt3系列，gpt4:gpt4系列，ernie:文心一言系列，gemini:谷歌gemini，qwen:通义千问，spark:讯飞星火，chatglm:清华智普，dalle:OpenAI Dall.E作图，midjourney: Midjourney作图
                </div>
              </span>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="pear-btn pear-btn-primary" lay-submit="" lay-filter="plan">保 存</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="layui-tab-item">
                    <form class="layui-form" lay-filter="category"  action="" onsubmit="return false">
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">角色分类</label>
                            <div class="layui-input-inline">
                                <textarea name="category" placeholder="每行一个" style="width:30em;height:30em;" class="layui-textarea"></textarea>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="pear-btn pear-btn-primary" lay-submit="" lay-filter="category">保 存</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="layui-tab-item">
                    <form class="layui-form" lay-filter="sensitive-word"  action="" onsubmit="return false">
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">敏感词设置</label>
                            <div class="layui-input-inline">
                                <textarea name="sensitive-word" placeholder="每行一个" style="width:30em;height:30em;" class="layui-textarea"></textarea>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="pear-btn pear-btn-primary" lay-submit="" lay-filter="sensitive-word">保 存</button>
                            </div>
                        </div>
                    </form>
                </div>

            </div>

        </div>

    </div>
</div>


<script src="/app/admin/component/layui/layui.js"></script>
<script src="/app/admin/component/pear/pear.js"></script>
<script src="/app/admin/admin/js/permission.js"></script>
<script src="/app/admin/admin/js/common.js"></script>


<style>
    .layui-form-label {
        width: 100px;
    }
</style>

<script>

    function getSetting() {
        let form = layui.form;
        let $ = layui.jquery;

        // 基础配置
        $.ajax({
            url: "/app/ai/admin/setting/select",
            success: function (res) {
                if (res.code) {
                    return layui.popup.failure(res.msg);
                }
                for (let name in res.data) {
                    if (["category", "model", "plan", "sensitive-word"].includes(name)) {
                        let obj = {};
                        obj[name] = res.data[name];
                        form.val(name, obj);
                    } else {
                        form.val(name, res.data[name]);
                    }
                    if (!getSetting.inited) {
                        form.on("submit(" + name + ")", function (data) {
                            $.ajax({
                                url: "/app/ai/admin/" + name + "/update",
                                dataType: "json",
                                type: "POST",
                                data: data.field,
                                success: function (res) {
                                    if (res.code) {
                                        return layui.popup.failure(res.msg);
                                    }
                                    getSetting();
                                    return layui.popup.success("操作成功");
                                }
                            });
                            return false;
                        });
                    }
                }
                getSetting.inited = true;
            }
        });
    }

    layui.use(["form", "popup"], getSetting);
</script>
</body>
</html>

