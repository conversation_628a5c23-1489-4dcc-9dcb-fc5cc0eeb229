<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/app/ai/css/bootstrap.min.css?v=5.3" rel="stylesheet">
    <link href="/app/ai/css/app.css?v=<?=ai_css_version()?>" rel="stylesheet">
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>
    <title>webman AI - 市场</title>
    
</head>

<body data-bs-theme="light">

<div class="header">应用市场</div>
<div class="container-fluid p-4 overflow-scroll light-bg" style="height: calc(100% - 45px)">
    <div class="row">
        <div class="col-12 pt-2" id="app">
            <div v-cloak>
                <div class="d-inline-block me-3">
                    <div class="d-flex align-items-center flex-wrap">
                        <button class="btn btn-sm btn-outline-secondary me-3 mb-3 white-bg border" @click="category=''" :class="{selected:category===''}">全部</button>
                        <button v-for="item in categories" @click="category=item" class="btn btn-sm btn-outline-secondary me-3 mb-3 white-bg border" :class="{selected:category===item}">{{item}}</button>
                    </div>
                </div>

                <div class="d-inline-block mb-3">
                    <div class="d-flex align-items-center">
                        <input class="form-control me-3 d-inline-block border" v-model="keyword" type="text" placeholder="搜索" style="width:11em">
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle border-0 bg-transparent" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                {{sort==="hot"?"热门":"最新"}}
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" @click="sort='hot'">热门</a>
                                <a class="dropdown-item" @click="sort='new'">最新</a>
                            </div>
                        </div>
                        <div class="dropdown d-inline-block ms-2">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle border-0 bg-transparent" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                {{{all:"全部",installed:"已添加",notInstalled:"未添加"}[display]}}
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" @click="display='all'">全部</a>
                                <a class="dropdown-item" @click="display='notInstalled'">未添加</a>
                                <a class="dropdown-item" @click="display='installed'">已添加</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" v-cloak>
                <div v-for="role in filteredRoles" class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-3">
                    <div class="white-bg shadow-sm mt-3 rounded p-3" @mouseover="hover=role.roleId" @mouseout="hover=0">
                        <div class="d-flex align-items-center justify-content-between role position-relative">
                            <div class="d-flex align-items-center">
                                <img :src="role.avatar" class="avatar me-2" @click="installAndSwitch(role.roleId)"/>
                                <div>
                                    <div>{{role.name}}</div>
                                    <div class="text-secondary-sm">{{role.desc}}</div>
                                </div>
                            </div>
                            <div v-if="installed(role.roleId)" class="installed" title="点击卸载" @click="uninstall(role.roleId)">
                                &#xe9dd;
                            </div>
                            <div v-else class="install" title="点击安装" v-show="isMobile||hover==role.roleId" @click="install(role.roleId)">
                                &#xe9dc;
                            </div>
                        </div>
                        <div class="mt-3 d-flex justify-content-between align-items-center">
                            <span class="text-secondary-sm"># {{role.category}}</span> <span class="text-secondary-sm icon">&#xe9e3; {{role.installed}}</span>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>



<!-- vue -->
<script type="text/javascript" src="/app/ai/js/vue.global.js"></script>

<script>
    const App = {
        data() {
            return {
                roles: [],
                installedRoles: [],
                categories: [],
                hover: 0,
                category: '',
                keyword: '',
                display: 'all', // all installed notInstalled
                sort: 'hot', // hot new
                isMobile: false
            }
        },
        computed: {
            filteredRoles() {
                return this.roles.filter((item) => {
                    return (item.status === 0 && (this.display==='all' || (this.display==='installed'&&this.installed(item.roleId)) || (this.display==='notInstalled'&&!this.installed(item.roleId))) &&
                        (!this.category || (item.category && item.category.includes(this.category))) &&
                        (!this.keyword || (item.name.includes(this.keyword) || (item.desc&&item.desc.includes(this.keyword)))));
                }).sort((a, b) => {
                    return this.sort === "hot" ? (b.installed||0) - (a.installed||0) : b.roleId - a.roleId;
                });
            }
        },
        mounted() {
            this.loadRoles();
            this.loadInstalledRoles();
            this.loadCategories();
            this.checkMobile();
        },
        methods: {
            loadRoles() {
                $.ajax({
                    url: "/app/ai/roles?type=all",
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        this.roles = res.data;
                    }
                });
            },
            loadCategories() {
                $.ajax({
                    url: "/app/ai/setting/categories",
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        for (let category of res.data) {
                            this.categories.push(category.value);
                        }
                    }
                });
            },
            loadInstalledRoles() {
                const data = JSON.parse(localStorage.getItem("ai.data") || "{}");
                this.installedRoles = data.roleList || [];
            },
            install(roleId) {
                const role = this.roles.find((role) => role.roleId === roleId);
                if (role) {
                    window.parent.ai.saveRole(role);
                    this.loadInstalledRoles();
                    $.ajax({
                        url: "/app/ai/role/installed",
                        data: {roleId},
                        type: "post"
                    });
                }
            },
            installAndSwitch(roleId) {
                this.install(roleId);
                window.parent.ai.switchRoleId(roleId);
                window.parent.ai.switchModule('chat');
            },
            uninstall(roleId) {
                window.parent.ai.deleteRole(roleId);
                this.loadInstalledRoles();
            },
            installed(roleId) {
                return this.installedRoles.find((role) => !role.deleted && role.roleId === roleId);
            },
            checkMobile() {
                this.isMobile = window.innerWidth <= 768; // 假设小于768px的宽度为移动端
            },
        }
    }
    Vue.createApp(App).mount('#app');

    $(document).click(function () {
        try {window.parent.ai.hideAll();} catch (e) {}
    });

    try {
        $(document.body).attr("data-bs-theme", window.parent.ai.theme);
        parent.ai.showIframe();
    } catch (e) {}
</script>

<style>
    [data-bs-theme=light] .btn {
        --bs-btn-hover-bg: var(--ai-white-bg);
        --bs-btn-active-bg: var(--bs-btn-hover-bg);
        --bs-btn-active-color: var(--bs-primary);
    }
    [data-bs-theme=dark] .btn {
        --bs-btn-hover-bg: var(--ai-white-bg);
        --bs-btn-active-bg: var(--ai-white-bg);
        --bs-btn-active-color: var(--bs-body-color);
    }
    .container-fluid {
        max-width: 1660px;
    }
    .install, .installed {
        color: rgb(var(--bs-secondary-rgb));
        font-size: 1.6rem;
        border-radius: .5rem;
        width: 2rem;
        height: 2rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: iconfont;
    }
    .install:hover {
        color: var(--bs-primary);
    }
    .installed {
        color: var(--bs-primary) !important;
    }
    .btn-sm {
        font-size: 1rem !important;
    }
    .selected {
        color: var(--bs-primary);
        border-color: var(--bs-primary) !important;
    }
    [data-bs-theme=dark] .selected {
        color: var(--bs-body-color);
        border-color: var(--bs-primary) !important;
    }
    a {
        cursor: pointer;
    }
    .btn-outline-secondary {
        --bs-btn-color: var(--bs-btn-color);
        --bs-btn-hover-bg: var(--bs-btn-hover-bg);
        --bs-btn-hover-color: var(--bs-btn-hover-color);
    }
    body {
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
    }
</style>

</body>
</html>


