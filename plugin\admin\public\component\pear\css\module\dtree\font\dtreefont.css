@font-face {
  font-family: 'dtreefont';
  src:  url('dtreefont.eot?x3m8fp');
  src:  url('dtreefont.eot?x3m8fp#iefix') format('embedded-opentype'),
    url('dtreefont.ttf?x3m8fp') format('truetype'),
    url('dtreefont.woff?x3m8fp') format('woff'),
    url('dtreefont.svg?x3m8fp#dtreefont') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="dtree-icon-"], [class*=" dtree-icon-"] {
  /* use !important to prevent issues with browser extensions that change font */
  font-family: 'dtreefont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
}

.dtree-icon-xiangxia1:before {
  content: "\e771";
}
.dtree-icon-normal-file:before {
  content: "\e60c";
}
.dtree-icon-xiangyou:before {
  content: "\e78f";
}
.dtree-icon-ok-circle:before {
  content: "\1005";
}
.dtree-icon-close1:before {
  content: "\1006";
}
.dtree-icon-close-fill:before {
  content: "\1007";
}
.dtree-icon-jian1:before {
  content: "\e600";
}
.dtree-icon-jia1:before {
  content: "\e601";
}
.dtree-icon-bianji:before {
  content: "\e602";
}
.dtree-icon-yonghu:before {
  content: "\e603";
}
.dtree-icon-shijian:before {
  content: "\e606";
}
.dtree-icon-fuxuankuang-banxuan:before {
  content: "\e607";
}
.dtree-icon-star:before {
  content: "\e608";
}
.dtree-icon-wenjianjiazhankai:before {
  content: "\e60e";
}
.dtree-icon-xiangmuxiaoxi:before {
  content: "\e60f";
}
.dtree-icon-search2:before {
  content: "\e615";
}
.dtree-icon-weibiaoti5:before {
  content: "\e618";
}
.dtree-icon-layim-theme:before {
  content: "\e61b";
}
.dtree-icon-shuye1:before {
  content: "\e61e";
}
.dtree-icon-add-circle:before {
  content: "\e61f";
}
.dtree-icon-xinxipilu:before {
  content: "\e620";
}
.dtree-icon-set-sm:before {
  content: "\e621";
}
.dtree-icon-about:before {
  content: "\e623";
}
.dtree-icon-chart-screen:before {
  content: "\e62a";
}
.dtree-icon-delete1:before {
  content: "\e640";
}
.dtree-icon-share3:before {
  content: "\e641";
}
.dtree-icon-youjian:before {
  content: "\e642";
}
.dtree-icon-check:before {
  content: "\e645";
}
.dtree-icon-close:before {
  content: "\e646";
}
.dtree-icon-favorfill:before {
  content: "\e64b";
}
.dtree-icon-favor:before {
  content: "\e64c";
}
.dtree-icon-fuxuankuangxuanzhong:before {
  content: "\e652";
}
.dtree-icon-fenguangbaobiao:before {
  content: "\e655";
}
.dtree-icon-jian:before {
  content: "\e656";
}
.dtree-icon-jia:before {
  content: "\e657";
}
.dtree-icon-fenzhijigou:before {
  content: "\e658";
}
.dtree-icon-roundcheckfill:before {
  content: "\e659";
}
.dtree-icon-roundcheck:before {
  content: "\e65a";
}
.dtree-icon-roundclosefill:before {
  content: "\e65b";
}
.dtree-icon-roundclose:before {
  content: "\e65c";
}
.dtree-icon-roundrightfill:before {
  content: "\e65d";
}
.dtree-icon-roundright:before {
  content: "\e65e";
}
.dtree-icon-like:before {
  content: "\e66c";
}
.dtree-icon-samefill:before {
  content: "\e671";
}
.dtree-icon-same:before {
  content: "\e672";
}
.dtree-icon-evaluate:before {
  content: "\e674";
}
.dtree-icon-circle1:before {
  content: "\e687";
}
.dtree-icon-radio:before {
  content: "\e688";
}
.dtree-icon-caidan_xunzhang:before {
  content: "\e68e";
}
.dtree-icon-pulldown:before {
  content: "\e6a0";
}
.dtree-icon-pullup:before {
  content: "\e6a1";
}
.dtree-icon-refresh:before {
  content: "\e6a4";
}
.dtree-icon-qrcode1:before {
  content: "\e6b0";
}
.dtree-icon-profile1:before {
  content: "\e6b7";
}
.dtree-icon-home1:before {
  content: "\e6b8";
}
.dtree-icon-homefill:before {
  content: "\e6bb";
}
.dtree-icon-roundaddfill:before {
  content: "\e6d8";
}
.dtree-icon-roundadd:before {
  content: "\e6d9";
}
.dtree-icon-fuxuankuang:before {
  content: "\e6f2";
}
.dtree-icon-wefill:before {
  content: "\e6f5";
}
.dtree-icon-sort:before {
  content: "\e701";
}
.dtree-icon-repair:before {
  content: "\e738";
}
.dtree-icon-shujudaping:before {
  content: "\e742";
}
.dtree-icon-dian:before {
  content: "\e7a5";
}
.dtree-icon-search_list_light:before {
  content: "\e807";
}
.dtree-icon-round_list_light:before {
  content: "\e82b";
}
.dtree-icon-star-fill:before {
  content: "\e832";
}
.dtree-icon-rate:before {
  content: "\e833";
}
.dtree-icon-move-up:before {
  content: "\ea47";
}
.dtree-icon-move-down:before {
  content: "\ea48";
}
