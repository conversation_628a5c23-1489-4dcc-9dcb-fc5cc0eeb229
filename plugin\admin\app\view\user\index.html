
<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="utf-8">
        <title>浏览页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body class="pear-container">
    
        <!-- 顶部查询表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form top-search-from">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">主键</label>
                        <div class="layui-input-block">
                            <input type="number" name="id" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户名</label>
                        <div class="layui-input-block">
                            <input type="text" name="username" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">昵称</label>
                        <div class="layui-input-block">
                            <input type="text" name="nickname" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">性别</label>
                        <div class="layui-input-block">
                            <div name="sex" id="sex" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">邮箱</label>
                        <div class="layui-input-block">
                            <input type="text" name="email" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">手机</label>
                        <div class="layui-input-block">
                            <input type="text" name="mobile" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">等级</label>
                        <div class="layui-input-block">
                            <input type="number" name="level" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">生日</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block" id="birthday">
                                <input type="text" autocomplete="off" name="birthday[]" id="birthday-date-start" class="layui-input inline-block" placeholder="开始时间">
                                -
                                <input type="text" autocomplete="off" name="birthday[]" id="birthday-date-end" class="layui-input inline-block" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">余额(元)</label>
                        <div class="layui-input-block">
                            <input type="number" name="money" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">积分</label>
                        <div class="layui-input-block">
                            <input type="number" name="score" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">登录时间</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block" id="last_time">
                                <input type="text" autocomplete="off" name="last_time[]" id="last_time-date-start" class="layui-input inline-block" placeholder="开始时间">
                                -
                                <input type="text" autocomplete="off" name="last_time[]" id="last_time-date-end" class="layui-input inline-block" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">登录ip</label>
                        <div class="layui-input-block">
                            <input type="text" name="last_ip" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">注册时间</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block" id="join_time">
                                <input type="text" autocomplete="off" name="join_time[]" id="join_time-date-start" class="layui-input inline-block" placeholder="开始时间">
                                -
                                <input type="text" autocomplete="off" name="join_time[]" id="join_time-date-end" class="layui-input inline-block" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">注册ip</label>
                        <div class="layui-input-block">
                            <input type="text" name="join_ip" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-inline">
                        <label class="layui-form-label"></label>
                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="table-query">
                            <i class="layui-icon layui-icon-search"></i>查询
                        </button>
                        <button type="reset" class="pear-btn pear-btn-md" lay-submit lay-filter="table-reset">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                    <div class="toggle-btn">
                        <a class="layui-hide">展开<i class="layui-icon layui-icon-down"></i></a>
                        <a class="layui-hide">收起<i class="layui-icon layui-icon-up"></i></a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="data-table" lay-filter="data-table"></table>
            </div>
        </div>

        <!-- 表格顶部工具栏 -->
        <script type="text/html" id="table-toolbar">
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add" permission="app.admin.user.insert">
                <i class="layui-icon layui-icon-add-1"></i>新增
            </button>
            <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove" permission="app.admin.user.delete">
                <i class="layui-icon layui-icon-delete"></i>删除
            </button>
        </script>

        <!-- 表格行工具栏 -->
        <script type="text/html" id="table-bar">
            <button class="pear-btn pear-btn-xs tool-btn" lay-event="edit" permission="app.admin.user.update">编辑</button>
            <button class="pear-btn pear-btn-xs tool-btn" lay-event="remove" permission="app.admin.user.delete">删除</button>
        </script>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        <script src="/app/admin/admin/js/common.js"></script>
        <script>

            // 相关常量
            const PRIMARY_KEY = "id";
            const SELECT_API = "/app/admin/user/select";
            const UPDATE_API = "/app/admin/user/update";
            const DELETE_API = "/app/admin/user/delete";
            const INSERT_URL = "/app/admin/user/insert";
            const UPDATE_URL = "/app/admin/user/update";
            
            // 字段 性别 sex
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.$.ajax({
                    url: "/app/admin/dict/get/sex",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#sex").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#sex",
                            name: "sex",
                            initValue: initValue,
                            data: res.data,
                            model: {"icon":"hidden","label":{"type":"text"}},
                            clickClose: true,
                            radio: true,
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            // 字段 生日 birthday
            layui.use(["laydate"], function() {
                layui.laydate.render({
                    elem: "#birthday",
                    range: ["#birthday-date-start", "#birthday-date-end"],
                });
            })
            
            // 字段 登录时间 last_time
            layui.use(["laydate"], function() {
                layui.laydate.render({
                    elem: "#last_time",
                    range: ["#last_time-date-start", "#last_time-date-end"],
                    type: "datetime",
                });
            })
            
            // 字段 注册时间 join_time
            layui.use(["laydate"], function() {
                layui.laydate.render({
                    elem: "#join_time",
                    range: ["#join_time-date-start", "#join_time-date-end"],
                    type: "datetime",
                });
            })
            
            // 表格渲染
            layui.use(["table", "form", "common", "popup", "util"], function() {
                let table = layui.table;
                let form = layui.form;
                let $ = layui.$;
                let common = layui.common;
                let util = layui.util;
                
				// 表头参数
				let cols = [
					{
						type: "checkbox"
					},{
						title: "主键",
						field: "id",
						sort: true,
					},{
						title: "用户名",
						field: "username",
					},{
						title: "昵称",
						field: "nickname",
					},{
						title: "密码",
						field: "password",
						hide: true,
					},{
						title: "性别",
						field: "sex",
						templet: function (d) {
							let field = "sex";
							if (typeof d[field] == "undefined") return "";
							let items = [];
							layui.each((d[field] + "").split(","), function (k , v) {
								items.push(apiResults[field][v] || v);
							});
							return util.escape(items.join(","));
						}
					},{
						title: "头像",
						field: "avatar",
						templet: function (d) {
							return '<img src="'+encodeURI(d['avatar'])+'" style="max-width:32px;max-height:32px;" alt="" />'
						}
					},{
						title: "邮箱",
						field: "email",
					},{
						title: "手机",
						field: "mobile",
					},{
						title: "等级",
						field: "level",
						hide: true,
					},{
						title: "生日",
						field: "birthday",
						hide: true,
					},{
						title: "余额(元)",
						field: "money",
						hide: true,
					},{
						title: "积分",
						field: "score",
						hide: true,
					},{
						title: "登录时间",
						field: "last_time",
						hide: true,
					},{
						title: "登录ip",
						field: "last_ip",
						hide: true,
					},{
						title: "注册时间",
						field: "join_time",
						hide: true,
					},{
						title: "注册ip",
						field: "join_ip",
						hide: true,
					},{
						title: "token",
						field: "token",
						hide: true,
					},{
						title: "创建时间",
						field: "created_at",
						hide: true,
					},{
						title: "更新时间",
						field: "updated_at",
						hide: true,
					},{
						title: "角色",
						field: "role",
						hide: true,
					},{
						title: "禁用",
						field: "status",
						templet: function (d) {
							let field = "status";
							form.on("switch("+field+")", function (data) {
								let load = layer.load();
								let postData = {};
								postData[field] = data.elem.checked ? 1 : 0;
								postData[PRIMARY_KEY] = this.value;
								$.post(UPDATE_API, postData, function (res) {
									layer.close(load);
									if (res.code) {
                                        return layui.popup.failure(res.msg, function () {
                                            data.elem.checked = !data.elem.checked;
                                            form.render();
                                        });
				                    }
									return layui.popup.success("操作成功");
								})
							});
							let checked = d[field] === 1 ? "checked" : "";
							return '<input type="checkbox" value="'+util.escape(d[PRIMARY_KEY])+'" lay-filter="'+util.escape(field)+'" lay-skin="switch" lay-text="'+util.escape('')+'" '+checked+'/>';
						}
					},{
						title: "操作",
						toolbar: "#table-bar",
						align: "center",
						fixed: "right",
                        width: 130,
					}
				];
				
				// 渲染表格
				function render()
				{
				    table.render({
				        elem: "#data-table",
				        url: SELECT_API,
				        page: true,
				        cols: [cols],
				        skin: "line",
				        size: "lg",
				        toolbar: "#table-toolbar",
				        autoSort: false,
				        defaultToolbar: [{
				            title: "刷新",
				            layEvent: "refresh",
				            icon: "layui-icon-refresh",
				        }, "filter", "print", "exports"],
				        done: function () {
				            layer.photos({photos: 'div[lay-id="data-table"]', anim: 5});
				        }
				    });
				}
				
				// 获取表格中下拉或树形组件数据
				let apis = [];
				apis.push(["sex", "/app/admin/dict/get/sex"]);
				let apiResults = {};
				apiResults["sex"] = [];
				let count = apis.length;
				layui.each(apis, function (k, item) {
				    let [field, url] = item;
				    $.ajax({
				        url: url,
				        dateType: "json",
				        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
				            function travel(items) {
				                for (let k in items) {
				                    let item = items[k];
				                    apiResults[field][item.value] = item.name;
				                    if (item.children) {
				                        travel(item.children);
				                    }
				                }
				            }
				            travel(res.data);
				        },
				        complete: function () {
				            if (--count === 0) {
				                render();
				            }
				        }
				    });
				});
				if (!count) {
				    render();
				}
				
                // 编辑或删除行事件
                table.on("tool(data-table)", function(obj) {
                    if (obj.event === "remove") {
                        remove(obj);
                    } else if (obj.event === "edit") {
                        edit(obj);
                    }
                });

                // 表格顶部工具栏事件
                table.on("toolbar(data-table)", function(obj) {
                    if (obj.event === "add") {
                        add();
                    } else if (obj.event === "refresh") {
                        refreshTable();
                    } else if (obj.event === "batchRemove") {
                        batchRemove(obj);
                    }
                });

                // 表格顶部搜索事件
                form.on("submit(table-query)", function(data) {
                    table.reload("data-table", {
                        page: {
                            curr: 1
                        },
                        where: data.field
                    })
                    return false;
                });
                
                // 表格顶部搜索重置事件
                form.on("submit(table-reset)", function(data) {
                    table.reload("data-table", {
                        where: []
                    })
                });

                // 表格排序事件
                table.on("sort(data-table)", function(obj){
                    table.reload("data-table", {
                        initSort: obj,
                        scrollPos: "fixed",
                        where: {
                            field: obj.field,
                            order: obj.type
                        }
                    });
                });

                // 表格新增数据
                let add = function() {
                    layer.open({
                        type: 2,
                        title: "新增",
                        shade: 0.1,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: INSERT_URL
                    });
                }

                // 表格编辑数据
                let edit = function(obj) {
                    let value = obj.data[PRIMARY_KEY];
                    layer.open({
                        type: 2,
                        title: "修改",
                        shade: 0.1,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: UPDATE_URL + "?" + PRIMARY_KEY + "=" + value
                    });
                }

                // 删除一行
                let remove = function(obj) {
                    return doRemove(obj.data[PRIMARY_KEY]);
                }

                // 删除多行
                let batchRemove = function(obj) {
                    let checkIds = common.checkField(obj, PRIMARY_KEY);
                    if (checkIds === "") {
                        layui.popup.warning("未选中数据");
                        return false;
                    }
                    doRemove(checkIds.split(","));
                }

                // 执行删除
                let doRemove = function (ids) {
                    let data = {};
                    data[PRIMARY_KEY] = ids;
                    layer.confirm("确定删除?", {
                        icon: 3,
                        title: "提示"
                    }, function(index) {
                        layer.close(index);
                        let loading = layer.load();
                        $.ajax({
                            url: DELETE_API,
                            data: data,
                            dataType: "json",
                            type: "post",
                            success: function(res) {
                                layer.close(loading);
                                if (res.code) {
                                    return layui.popup.failure(res.msg);
                                }
                                return layui.popup.success("操作成功", refreshTable);
                            }
                        })
                    });
                }

                // 刷新表格数据
                window.refreshTable = function() {
                    table.reloadData("data-table", {
                        scrollPos: "fixed",
                        done: function (res, curr) {
                            if (curr > 1 && res.data && !res.data.length) {
                                curr = curr - 1;
                                table.reloadData("data-table", {
                                    page: {
                                        curr: curr
                                    },
                                })
                            }
                        }
                    });
                }
            })

        </script>
    </body>
</html>
