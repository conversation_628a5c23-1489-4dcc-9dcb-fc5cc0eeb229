<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>水印组件</title>
    <link rel="stylesheet" href="../../component/pear/css/pear.css" />
</head>
<body class="pear-container">
<div class="layui-row layui-col-space10">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-header">开发环境</div>
            <div class="layui-card-body">
                watermark 用于水印叠加
            </div>
        </div>
    </div>
    <div class="layui-col-md12">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">显示代码</h2>
                <div class="layui-colla-content">
                    <pre class="layui-code" lay-encode="true">
                        &lt;link rel="stylesheet" href="component/pear/css/pear.css" /&gt;
                         并
                        &lt;script src="component/layui/layui.js"&gt;&lt;/script&gt;
                         并
                        &lt;script src="component/pear/pear.js"&gt;&lt;/script&gt;
                    </pre>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-header">水印创建</div>
            <div class="layui-card-body">
                <button class="btntype1 pear-btn pear-btn-primary">单行水印</button>
                <button class="btntype2 pear-btn pear-btn-danger">多行水印</button>
                <button class="btntype3 pear-btn pear-btn-warming">叠加目标</button>
            </div>
        </div>
    </div>
    <div class="layui-col-md12">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">显示代码</h2>
                <div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(['watermark'], function() {
								    var watermark = layui.watermark;

    var mark_node=new watermark({
        content: "单行水印"
    });

    new watermark({
        content: "多行水印1<br>多行水印2",
        height:40
    });

    new watermark({
        content: "指定容器上",
        appendTo:'#water_div',
    });

								})
							</pre>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-header">指定容器水印</div>
            <div class="layui-card-body">
                <div id="water_div"
                     style="width: 600px;
								height: 300px;
								overflow: hidden;
								position: relative;
								border: 1px solid gainsboro;
								background-color: #EEE;
								margin-top: 20px;
								display: flex;
								justify-content: space-around;
								align-items: center">

            </div>
        </div>
    </div>
</div>
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-header">移除水印</div>
            <div class="layui-card-body">
                <button class="btnclose pear-btn pear-btn-primary">销毁水印</button>
            </div>
        </div>
    </div>
    <div class="layui-col-md12">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">显示代码</h2>
                <div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(['watermark'], function() {
								    var popup = layui.popup;
                                    mark_node.destroy();
								})
							</pre>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script src="../../component/layui/layui.js"></script>
<script src="../../component/pear/pear.js"></script>
<script>
    layui.use(['watermark', 'jquery', 'code'], function() {
        var popup = layui.popup;
        var $ = layui.jquery;
        var watermark = layui.watermark;
        layui.code();

        var mark_node = null;

        function remove_water_marker(){
            mark_node && mark_node.destroy();
            mark_node = null;
        }


        $(".btntype1").click(function() {
            remove_water_marker();
            mark_node=new watermark({
                content: "单行水印"
            });
        })
        $(".btntype2").click(function() {
            remove_water_marker();
            mark_node =new watermark({
                content: "多行水印1<br>第二行长多行水印2",
                height:40
            });
        })
        $(".btntype3").click(function() {
            remove_water_marker();
            mark_node = new watermark({
                content: "指定目标上",
                appendTo:'#water_div',
            });
        })
        $(".btnclose").click(function() {
            remove_water_marker();
        })

    })
</script>
</html>
