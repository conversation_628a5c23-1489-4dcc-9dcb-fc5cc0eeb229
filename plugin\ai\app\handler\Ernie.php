<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

class Ernie extends Base
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = '文心一言';

    /**
     * @var string 模型类型
     */
    protected static $type = 'ernie';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'ernie-bot',
        'ernie-bot-turbo',
        'ernie-speed-8k',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'apikey' => [
            'name' => 'ApiKey',
            'type' => 'text',
            'value' => '',
            'desc' => 'API Key',
        ],
        'secretKey' => [
            'name' => 'SecretKey',
            'type' => 'text',
            'value' => '',
            'desc' => 'Secret Key',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 0,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Ernie::class;

    /**
     * 对话
     * @param $data
     * @param $options
     * @return void
     */
    public function completions($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->completions($data, $options);
    }
}