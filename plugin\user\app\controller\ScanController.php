<?php


namespace plugin\user\app\controller;


use Illuminate\Database\Eloquent\Model;
use plugin\user\api\Captcha;
use plugin\user\api\FormException;
use plugin\user\api\Limit;
use plugin\user\app\model\User;
use plugin\user\app\model\WaUsers;
use plugin\user\app\service\Register;
use plugin\user\app\service\Wechat;
use support\Db;
use support\exception\BusinessException;
use support\Request;
use support\Response;
use support\view\ThinkPHP;
use Webman\Event\Event;
use function Couchbase\defaultDecoder;

class ScanController
{

    protected $reqUrl = "https://yd.baimalive.com/WeChatQRLogin/weixinLogin/index.php";
    protected $reqWechatUrl = "https://yd.baimalive.com/WeChatQRLogin/weixinLogin/wechat_template.php";
    /**
     * 不需要登录验证的方法
     * @var string[]
     */
    protected $noNeedLogin = ['index','getscan','checkscan'];

    /**
     * 登录
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function index(Request $request): Response
    {

//        $ip = $this->get_client_ip();
//        echo $ip;
        return view('scan/scan', []);
    }

    /**
     * 获取二维码
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function getscan(Request $request): Response
    {
        if ($request->method() === 'POST') {
            $urlData =  $this->httpRequest($this->reqUrl);
            $data = $this->jsonData($urlData);
            return json(['code' => 0, 'scene_str'=>$data['scene_str'],'srcImg' => $data['srcImg']]);
        }
    }


    // 查询是否有openid
    public function getOpenidByScene($scan){
        return Db::connection('mysql2')->table('ai_login_records')->where('event_key', $scan)->first();
    }

    // 查询用户信息
    public function getUserByOpenid($openid){
        return Db::connection('mysql2')->table('wa_users')->where('openid', $openid)->first();
    }


    // 推送微信模板
    public function sendWechatTemp($data = []){
        $urlData =  $this->httpRequest($this->reqWechatUrl,$data);
        $res = $this->jsonData($urlData);
        return $res;
    }

    /**
     * 检查是否登录
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function checkscan(Request $request): Response
    {
        if ($request->method() === 'POST') {
            // 实现登录注册逻辑
            $sceneStr = trim($request->post('scene_str', ''));
            $resOpenid =  $this->getOpenidByScene($sceneStr);
//            $openid
            if (empty($resOpenid->openid)){
                return json(['code' => 0,'msg'=>'未扫码','time'=>time()]);
            }else{
                // 已经扫码
                $openid = $resOpenid->openid;

                $userinfo = $this->getUserByOpenid($openid);
                // 查询客户信息
                $userData =  User::where('openid', $openid)->get();

                $ip = "**********";

                if (count($userData) == 0){
                    // 去掉id
                    unset($userinfo->id);
                    // 去掉id
                    $newArr = [];
                    $newArr['username'] = $userinfo->username;
                    $newArr['openid'] = $userinfo->openid;
                    $newArr['nickname'] = $userinfo->nickname;
                    $newArr['password'] = 0;
                    $newArr['sex'] = $userinfo->sex;
                    $newArr['avatar'] = $userinfo->avatar;
                    $newArr['last_time'] = $userinfo->last_time;
                    $newArr['last_ip'] = $userinfo->last_ip;
                    $newArr['join_time'] = $userinfo->join_time;
                    $newArr['join_ip'] = $userinfo->join_ip;
                    $newArr['created_at'] = $userinfo->created_at;
                    $newArr['status'] = $userinfo->status;
                    $id = Db::connection('mysql')->table('wa_users')->insertGetId($newArr);

                    $request->session()->set('user', [
                        'id' => $id,
                        'username' => $userinfo->username,
                        'nickname' => $userinfo->nickname,
                        'avatar' => $userinfo->avatar,
                        'email' => $userinfo->email,
                        'mobile' => $userinfo->mobile,
                    ]);

                    $useraa = User::where('id','=',$id)->get();
//                    // 发布登录事件
                    Event::emit('user.login', $useraa);
//                    $useraa->last_ip = $request->getRealIp();
//                    $useraa->last_time = date('Y-m-d H:i:s');
//                    $useraa->save();
                    $this->sendWechatTemp(['openid'=>$openid,'scene_str'=>$sceneStr,'name'=>$userinfo->nickname,'ip'=>$ip]);
                    return json(['code' => 1,'scene_str'=>$sceneStr,'msg'=>'首次注册成功','time'=>time()]);
                }else{
                    // 登录信息
                    foreach ($userData as $user) {
                        $request->session()->set('user', [
                            'id' => $user->id,
                            'username' => $user->username,
                            'nickname' => $user->nickname,
                            'avatar' => $user->avatar,
                            'email' => $user->email,
                            'mobile' => $user->mobile,
                        ]);
                        // 发布登录事件
                        Event::emit('user.login', $userData);
                        // 更新登录信息
//                        $userData->last_ip = $request->getRealIp();
//                        $userData->last_time = date('Y-m-d H:i:s');
//                        return json(['code' => 0,'msg'=>'未扫码','op'=>$userData,'time'=>time()]);
//                        $userData->save();
                        $this->sendWechatTemp(['openid'=>$openid,'scene_str'=>$sceneStr,'name'=>$user->nickname,'ip'=>$ip]);
                        return json(['code' => 1,'scene_str'=>$sceneStr,'msg'=>'登录成功','time'=>time()]);
                    }
                }

                // 推送消息模板


            }
        }
    }

    // 数据格式化
    public function jsonData($urlData){
        return json_decode($urlData,JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }


    // 内部请求
    private function httpRequest($url, $data = "") {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }


    /**
     * 扫码登录注册
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function scanlogin(Request $request): Response
    {
        $settings = Register::getSetting();

        if ($request->method() === 'GET') {
            $settings = Register::getSetting();
            return view('register/register', [
                'settings' => $settings,
            ]);
        }

        if (empty($settings['register_enable'])) {
            throw new FormException("注册功能已关闭", 1);
        }

        // 每个ip每分钟只能调用10次
        Limit::perMinute($request->getRealIp(), 10);

        // 收集数据
        $username = trim($request->post('username', ''));
        $password = $request->post('password');
        $nickname = trim($request->post('nickname', ''));
        $email = $request->post('email');
        $mobile = $request->post('mobile');
        $emailCode = $request->post('email_code');
        $mobileCode = $request->post('mobile_code');
        $imageCode = $request->post('image_code');
        $nickname = $nickname ?: $username;

        // 长度验证
        if (strlen($username) < 4) {
            return json(['code' => 1, 'msg' => '用户名至少4个字符', 'data' => [
                'field' => 'username'
            ]]);
        }

        // $username不能是纯数字
        if (is_numeric($username)) {
            return json(['code' => 1, 'msg' => '用户名不能是纯数字', 'data' => [
                'field' => 'username'
            ]]);
        }

        // $username不能带@符号
        if (strpos($username, '@') !== false) {
            return json(['code' => 1, 'msg' => '用户名不能带@符号', 'data' => [
                'field' => 'username'
            ]]);
        }

        if (strlen($password) < 6) {
            return json(['code' => 1, 'msg' => '密码至少6个字符', 'data' => [
                'field' => 'password'
            ]]);
        }

        // 用户数据
        $user = [
            'username' => $username,
            'password' =>  password_hash($password, PASSWORD_DEFAULT),
            'nickname' => $nickname,
        ];

        // 获取注册配置
        $settings = Register::getSetting();

        // 邮箱验证
        if ($settings['email_enable']) {
            $emailCode = $settings['email_verify'] ? $emailCode : false;
            Captcha::validate('email', $email, $emailCode, session("captcha-email-register"));
            if (User::where('email', $email)->first()) {
                throw new FormException("{$email}已经被占用", 1, 'email');
            }
            $user['email'] = $email;
        }

        // 手机验证
        if ($settings['mobile_enable']) {
            $mobileCode = $settings['mobile_verify'] ? $mobileCode : false;
            Captcha::validate('mobile', $mobile, $mobileCode, session("captcha-mobile-register"));
            if (User::where('mobile', $mobile)->first()) {
                throw new FormException("{$mobile}已经被占用", 1, 'mbile');
            }
            $user['mobile'] = $mobile;
        }

        // 图形验证码验证
        if ($settings['captcha_enable']) {
            $captchaData = session('captcha-image-register');
            try {
                Captcha::verify(null, $imageCode, $captchaData);
            } catch (BusinessException $exception) {
                return json(['code' => 1, 'msg' => '图形验证码错误', 'data' => [
                    'field' => 'image_code'
                ]]);
            }
        }

        // 用户名唯一性验证
        if (User::where('username', $username)->first()) {
            return json(['code' => 1, 'msg' => '用户名已经被占用', 'data' => [
                'field' => 'username'
            ]]);
        }

        // 注册用户
        $waUser = new User();
        foreach ($user as $key => $value) {
            $waUser->$key = $value;
        }
        $waUser->avatar = '/app/user/default-avatar.png';
        $waUser->join_time = date('Y-m-d H:i:s');
        $waUser->join_ip = $request->getRealIp();
        $waUser->save();

        // 发布注册事件
        Event::emit('user.register', $waUser);

        // 清理session
        $request->session()->delete('user');

        return json(['code' => 0, 'msg' => 'ok']);
    }




}