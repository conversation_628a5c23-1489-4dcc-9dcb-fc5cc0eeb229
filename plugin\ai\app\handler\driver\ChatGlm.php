<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler\driver;

use support\exception\BusinessException;
use Workerman\Http\Client;
use Workerman\Http\Response;

class ChatGlm extends Base
{

    /**
     * @var string api地址
     */
    protected $api = 'https://open.bigmodel.cn';

    /**
     * 请求
     * @param $data
     * @param $options
     * @return void
     * @throws BusinessException
     */
    public function completions($data, $options)
    {
        $token = $this->getToken();
        $headers = $this->getHeaders($options);
        $headers['Authorization'] = 'Bearer ' . $token;
        if (isset($options['stream'])) {
            $data['stream'] = true;
        }
        $options = $this->formatOptions($options);
        $data = static::formatData($data);
        $requestOptions = [
            'method' => 'POST',
            'data' => json_encode($data),
            'headers' => $headers,
            'progress' => function ($buffer) use ($options) {
                static $tmp = '';
                $tmp .= $buffer;
                if ($tmp[strlen($tmp) - 1] !== "\n") {
                    return;
                }
                preg_match_all('/data:(.*?)\n/', $tmp, $matches);
                $tmp = '';
                foreach ($matches[1] ?: [] as $match) {
                    $data = ['content' => $match === '' ? "\n" : $match];
                    $options['stream']($data);
                }
            },
            'success' => function (Response $response) use ($options) {
                $options['complete'](static::formatResponse((string)$response->getBody()), $response);
            },
            'error' => function ($exception) use ($options) {
                $options['complete']([
                    'error' => [
                        'code' => 'exception',
                        'message' => $exception->getMessage(),
                        'detail' => (string) $exception
                    ],
                ], new Response(0));
            }
        ];
        $http = new Client(['timeout' => 600]);
        $model = $data['model'];
        $http->request("$this->api/api/paas/v3/model-api/$model/sse-invoke", $requestOptions);
    }

    protected static function formatData($data)
    {
        if (isset($data['prompt'])) {
            return $data;
        }
        $model = $data['model'];
        $temperature = $data['temperature'] ?? null;
        $messages = static::formatMessages($data['messages']);
        $data = [
            'model' => $model,
            'prompt' => $messages,
            'return_type' => 'text'
        ];
        if ($temperature) {
            $data['temperature'] = $temperature;
        }
        return $data;
    }

    protected function getToken()
    {
        if (!strpos($this->apikey, '.')) {
            throw new BusinessException('apikey不正确');
        }
        [$appId, $apiKey] = explode('.', $this->apikey);
        $timestampMs = ceil(microtime(true) * 1000);
        return static::JwtEncode([
            'api_key'   => $appId,
            'exp'       => $timestampMs + 2 * 24 * 60 * 60 * 1000,
            'timestamp' => $timestampMs
        ], $apiKey, 'HS256', null, [
            'alg'       => 'HS256',
            'sign_type' => 'SIGN'
        ]);
    }

    public static function urlSafeB64Encode(string $input): string
    {
        return \str_replace('=', '', \strtr(\base64_encode($input), '+/', '-_'));
    }

    public static function JwtEncode(
        array $payload,
              $key,
        string $alg,
        string $keyId = null,
        array $head = null
    ): string {
        $header = ['typ' => 'JWT', 'alg' => $alg];
        if ($keyId !== null) {
            $header['kid'] = $keyId;
        }
        if (isset($head) && \is_array($head)) {
            $header = \array_merge($head, $header);
        }
        $segments = [];
        $segments[] = static::urlsafeB64Encode(static::jsonEncode($header));
        $segments[] = static::urlsafeB64Encode(static::jsonEncode($payload));
        $signing_input = \implode('.', $segments);
        $signature = \hash_hmac('SHA256', $signing_input, $key, true);
        $segments[] = static::urlsafeB64Encode($signature);
        return \implode('.', $segments);
    }

    public static function jsonEncode(array $input): string
    {
        $json = \json_encode($input, \JSON_UNESCAPED_SLASHES);
        if ($errno = \json_last_error()) {
            throw new BusinessException($errno);
        } elseif ($json === 'null') {
            throw new BusinessException('Null result with non-null input');
        }
        if ($json === false) {
            throw new BusinessException('Provided object could not be encoded to valid JSON');
        }
        return $json;
    }

    public static function formatResponse($buffer): string
    {
        if ($buffer && $buffer[0] === '{') {
            return $buffer;
        }
        $thunks = explode("\n\n", $buffer);
        $extractedData = '';
        for ($i = 0; $i < count($thunks); $i++) {
            $lines = explode("\n", $thunks[$i]);
            $dataFieldCount = 0;
            for ($j = 0; $j < count($lines); $j++) {
                $line = $lines[$j];
                if (strpos($line, "event:finish") === 0) {
                    break;
                } else if (strpos($line, "data:") === 0) {
                    // 多行data需要加一个换行
                    if ($dataFieldCount++ > 0) {
                        $extractedData .= "\n";
                    }
                    $extractedData .= substr($line, 5);
                }
            }
        }
        return $extractedData;
    }

}