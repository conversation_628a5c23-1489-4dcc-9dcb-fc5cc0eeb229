<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\controller;

use plugin\ai\api\Chat;
use plugin\ai\app\event\data\ChatStreamData;
use plugin\ai\app\event\data\ModelRequestData;
use plugin\ai\app\event\data\ModelResponseData;
use plugin\ai\app\service\Common;
use plugin\ai\app\service\Setting;
use plugin\ai\app\service\User;
use support\exception\BusinessException;
use support\Request;
use support\Response;
use Webman\Event\Event;
use support\Log;
use Workerman\Protocols\Http\Chunk;

class ChatController extends Base
{

    /**
     * 不需要登录的方法
     *
     * @var string[]
     */
    protected $noNeedLogin = ['completions', 'translate', 'summarize'];

    /**
     * 对话
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function completions(Request $request): Response
    {
        
        $model = $request->post('model');

        if ($error = static::tryReduceBalance($model)) {
            return json(['error' => ['message' => $error]]);
        }

        $stream = (bool)$request->post('stream');
        $messages = $request->post('messages');
        $content = last($messages)['content'] ?? '';
        $temperature = (float)$request->post('temperature', 0.5);
        $userMessageId = $request->post('user_message_id');
        $assistantMessageId = $request->post('assistant_message_id');
        $roleId = $request->post('role_id');
        $chatId = $request->post('chat_id');
        $userId = session('user.id') ?? session('user.uid');
        $sessionId = $request->sessionId();
        $realIp = $request->getRealIp();
        $this->saveMessage($userId, $sessionId, $userMessageId, $roleId, 'user', $content, $realIp, $model, $chatId);

        $connection = $request->connection;
        $modelRequestData = new ModelRequestData();
        $modelRequestData->data = [
            'model' => $model,
            'stream' => $stream,
            'messages' => $messages,
            'temperature' => $temperature,
        ];
        $modelRequestData->options = [
            'stream' => function ($data) use ($connection, $modelRequestData) {
                $chatStreamData = new ChatStreamData($modelRequestData, $data);
                Event::dispatch('ai.chat.completions.stream', $chatStreamData);
                $connection->send(new Chunk( json_encode($chatStreamData->streamData, JSON_UNESCAPED_UNICODE) . "\n"));
            },
            'complete' => function ($result, $response) use ($connection, $userId, $sessionId, $assistantMessageId, $roleId, $realIp, $model, $chatId, $modelRequestData) {
                $responseData = new ModelResponseData();
                $responseData->data = $result;
                $responseData->modelRequestData = $modelRequestData;
                Event::dispatch('ai.chat.completions.response', $responseData);
                $result = $responseData->data;
                if (isset($result['error'])) {
                    $result = json_encode($result, JSON_UNESCAPED_UNICODE);
                    Log::error($result);
                    $connection->send(new Chunk($result));
                    $modelType = Common::getModelType($model);
                    if ($userId && User::getBalance($userId, $modelType) > 0) {
                        User::addBalance($userId, $modelType);
                    }
                }
                $connection->send(new Chunk(''));
                $this->saveMessage($userId, $sessionId, $assistantMessageId, $roleId, 'assistant', $result, $realIp, $model, $chatId);
            }
        ];
        Event::dispatch('ai.chat.completions.request', $modelRequestData);
        Chat::completions($modelRequestData->data, $modelRequestData->options);
        return response()->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    public function translate(Request $request)
    {
        $content = $request->post('content');
        if (!$content) {
            return json(['error' => ['message' => '内容为空']]);
        }
        $setting = Setting::getSetting();
        $model = $setting['translate_model'] ?? 'gpt-3.5-turbo';
        $prompt = !empty($setting['translate_prompt']) ? $setting['translate_prompt'] : '请作为翻译官，将以下内容翻译成英文。注意只做翻译，不回复其它内容。例如"请翻译:画一只小狗"你应该返回"a dog"；例如"请翻译:你能画一只熊猫么?"，你应该返回"a panda"；例如"请翻译:一只猫 --ar:1:2 --s 200"你应该返回"a cat --ar:1:2 --s 200"。记住你的返回中无论如何不能包含中文';
        $connection = $request->connection;
        $modelRequestData = new ModelRequestData();
        $modelRequestData->data = [
            'model' => $model,
            'temperature' => 1,
            'messages' => [
                ['role' => 'user', 'content' => $prompt],
                ['role' => 'assistant', 'content' => '好的，请发送要翻译的内容'],
                ['role' => 'user', 'content' => "请翻译:$content"]
            ],
        ];
        $modelRequestData->options = [
            'complete' => function ($result) use ($connection, $modelRequestData) {
                if (isset($result['error'])) {
                    $result = json_encode($result, JSON_UNESCAPED_UNICODE);
                    Log::error($result);
                    $connection->send(new Chunk($result));
                } else {
                    $responseData = new ModelResponseData();
                    $responseData->data = $result;
                    $responseData->modelRequestData = $modelRequestData;
                    Event::dispatch('ai.chat.translate.response', $responseData);
                    $connection->send(new Chunk(json_encode(['content' => $result])));
                }
                $connection->send(new Chunk(''));
            }
        ];
        Event::dispatch('ai.chat.translate.request', $modelRequestData);
        Chat::completions($modelRequestData->data, $modelRequestData->options);
        return response()->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    /**
     * 总结问题
     * @param Request $request
     * @return Response|null
     */
    public function summarize(Request $request): ?Response
    {
        $messages = $request->post('messages');
        $content = last($messages)['content'] ?? '';
        if (!$content) {
            return json(['error' => ['message' => '内容为空']]);
        }
        $connection = $request->connection;
        $model = $request->post('model');
        $content = json_encode($messages, JSON_UNESCAPED_UNICODE);
        $lastContent = last($messages)['content'];
        $content = $content . "\n\n" . '以上是一段对话，请将对话中最后一句总结成一个具有清晰含义的提问，注意，是总结成一个有明确含义的提问。如果无法总结，请返回 ' . $lastContent;
        Chat::completions([
            'model' => $model,
            'temperature' => 1,
            'messages' => [
                ['role' => 'user', 'content' => $content],
            ]], [
            'complete' => function ($result, $response) use ($connection) {
                if (isset($result['error'])) {
                    $result = json_encode($result, JSON_UNESCAPED_UNICODE);
                    Log::error($result);
                    $connection->send(new Chunk($result));
                } else {
                    $connection->send(new Chunk(json_encode(['content' => $result])));
                }
                $connection->send(new Chunk(''));
            }
        ]);
        return response()->withHeaders([
            "Content-Type" => "application/json",
            "Transfer-Encoding" => "chunked",
        ]);
    }

    /**
     * 简单的语言检测函数
     */
    private function detectLanguage($text) {
        // 中文字符范围
        if (preg_match("/\p{Han}+/u", $text)) {
            return 'zh';
        }
        
        // 日文特有字符
        if (preg_match("/[\x{3040}-\x{309F}]|[\x{30A0}-\x{30FF}]/u", $text)) {
            return 'ja';
        }
        
        // 韩文特有字符
        if (preg_match("/[\x{3130}-\x{318F}]|[\x{AC00}-\x{D7AF}]/u", $text)) {
            return 'ko';
        }
        
        // 默认认为是英文
        return 'en';
    }

    /**
     * 爬取亚马逊商品信息
     */
    public function crawlAmazon(Request $request)
    {
        $urls = $request->post('urls', []);
        $results = [];
        $mainLanguage = 'en';
        
        foreach ($urls as $url) {
            try {
                // 验证是否为亚马逊链接
                if (!preg_match('/amazon\.com\/dp\/[A-Z0-9]{10}/', $url)) {
                    throw new \Exception('Invalid Amazon URL');
                }

                // 使用 cURL 替代 file_get_contents
                $ch = curl_init();
                curl_setopt_array($ch, [
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language: en-US,en;q=0.5',
                        'Cache-Control: no-cache',
                        'Pragma: no-cache',
                        'Cookie: session-id=123; session-id-time=' . time()
                    ],
                ]);

                $html = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($httpCode !== 200 || !$html) {
                    throw new \Exception("Failed to fetch page. HTTP Code: $httpCode");
                }

                // 使用 DOMDocument 解析 HTML
                $doc = new \DOMDocument();
                @$doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
                $xpath = new \DOMXPath($doc);
                
                // 获取标题 (尝试多个可能的选择器)
                $titleNode = $xpath->query('//*[@id="productTitle"]')->item(0) ?? 
                            $xpath->query('//h1[contains(@class, "product-title")]')->item(0);
                
                if (!$titleNode) {
                    throw new \Exception('Product title not found');
                }
                
                $title = trim($titleNode->textContent);
                
                // 获取特点列表 (尝试多个可能的选择器)
                $features = [];
                $featureNodes = $xpath->query('//*[@id="feature-bullets"]//li') ?:
                               $xpath->query('//div[contains(@class, "feature-bullets")]//li');
                
                foreach ($featureNodes as $node) {
                    $featureText = trim($node->textContent);
                    if ($featureText && !preg_match('/^\s*$/', $featureText)) {
                        $features[] = $featureText;
                    }
                }
                
                if (empty($features)) {
                    throw new \Exception('Product features not found');
                }

                // 检测标题的语言
                $titleLanguage = $this->detectLanguage($title);
                if ($titleLanguage !== 'en') {
                    $mainLanguage = $titleLanguage;
                }
                
                $results[] = [
                    'url' => $url,
                    'title' => $title,
                    'features' => $features,
                    'success' => true,
                    'language' => $titleLanguage
                ];
                
            } catch (\Exception $e) {
                $results[] = [
                    'url' => $url,
                    'error' => $e->getMessage(),
                    'success' => false
                ];
            }
            
            // 添加随机延迟，避免请求过快
            usleep(rand(500000, 1500000)); // 0.5-1.5秒延迟
        }
        
        return json([
            'success' => true,
            'data' => $results,
            'mainLanguage' => $mainLanguage
        ]);
    }

}
