<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>新增页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body>

        <form class="layui-form" action="">

            <div class="mainBox">
                <div class="main-container mr-5">
                    <div class="layui-form-item">
                        <label class="layui-form-label">apikey</label>
                        <div class="layui-input-block">
                            <textarea name="apikeys" rows="12" placeholder="每行一个apikey" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">api</label>
                        <div class="layui-input-block">
                            <input name="api" type="text" placeholder="默认请留空" class="layui-input"/>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">gpt3</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="gpt3" lay-filter="gpt3" lay-skin="switch" />
                            <input type="text" style="display:none" name="gpt3" value="1" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">gpt4</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="gpt4" lay-filter="gpt4" lay-skin="switch" />
                            <input type="text" style="display:none" name="gpt4" value="0" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">dall.e</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="dalle" lay-filter="dalle" lay-skin="switch" />
                            <input type="text" style="display:none" name="dalle" value="0" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">embedding</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="embedding" lay-filter="embedding" lay-skin="switch" />
                            <input type="text" style="display:none" name="embedding" value="0" />
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">tts</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="tts" lay-filter="tts" lay-skin="switch" />
                            <input type="text" style="display:none" name="tts" value="0" />
                        </div>
                    </div>

                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                        lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
        </form>

        <script src="/app/admin/component/layui/layui.js"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        
        <script>

            // 相关接口
            const INSERT_API = "/app/ai/admin/ai-apikey/batch-insert";

            // 字段 支持gpt3 gpt3
            layui.use(["form"], function() {
                layui.$("#gpt3").attr("checked", layui.$('input[name="gpt3"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(gpt3)", function(data) {
                    layui.$('input[name="gpt3"]').val(this.checked ? 1 : 0);
                });
            })

            // 字段 支持gpt4 gpt4
            layui.use(["form"], function() {
                layui.$("#gpt4").attr("checked", layui.$('input[name="gpt4"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(gpt4)", function(data) {
                    layui.$('input[name="gpt4"]').val(this.checked ? 1 : 0);
                });
            })

            // 字段 支持embedding embedding
            layui.use(["form"], function() {
                layui.$("#embedding").attr("checked", layui.$('input[name="embedding"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(embedding)", function(data) {
                    layui.$('input[name="embedding"]').val(this.checked ? 1 : 0);
                });
            })

            // 字段 支持tts tts
            layui.use(["form"], function() {
                layui.$("#tts").attr("checked", layui.$('input[name="tts"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(tts)", function(data) {
                    layui.$('input[name="tts"]').val(this.checked ? 1 : 0);
                });
            })

            // 字段 支持dalle dalle
            layui.use(["form"], function() {
                layui.$("#dalle").attr("checked", layui.$('input[name="dalle"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(dalle)", function(data) {
                    layui.$('input[name="dalle"]').val(this.checked ? 1 : 0);
                });
            })

            //提交事件
            layui.use(["form", "popup"], function () {
                layui.form.on("submit(save)", function (data) {
                    const regex = /(sk-|sess-)[a-zA-Z0-9]+/g;
                    const apikeys = data.field.apikeys.match(regex);
                    if (!apikeys) {
                        layui.popup.failure("没有匹配到apikey");
                        return false;
                    }
                    data.field.apikeys = apikeys;
                    layui.$.ajax({
                        url: INSERT_API,
                        type: "POST",
                        dateType: "json",
                        data: data.field,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>
</html>
