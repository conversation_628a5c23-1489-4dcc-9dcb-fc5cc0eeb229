<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>新增页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body>

        <form class="layui-form" action="">

            <div class="mainBox">
                <div class="main-container mr-5">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">头像</label>
                        <div class="layui-input-block">
                            <img class="img-3" src=""/>
                            <input type="text" style="display:none" name="avatar" value="/app/ai/avatar/ai.png" />
                            <button type="button" class="pear-btn pear-btn-primary pear-btn-sm" id="avatar"  permission="app.admin.upload.image">
                                <i class="layui-icon layui-icon-upload"></i>上传图片
                            </button>
                            <button type="button" class="pear-btn pear-btn-primary pear-btn-sm" id="attachment-choose-avatar"  permission="app.admin.upload.attachment">
                                <i class="layui-icon layui-icon-align-left"></i>选择图片
                            </button>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">简介</label>
                        <div class="layui-input-block">
                            <input type="text" name="desc" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">角色提示</label>
                        <div class="layui-input-block">
                            <textarea name="rolePrompt" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">问候语</label>
                        <div class="layui-input-block">
                            <input type="text" name="greeting" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">模型</label>
                        <div class="layui-input-block">
                            <div name="model" id="model" value="gpt-3.5-turbo-16k" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">上下文数</label>
                        <div class="layui-input-block">
                            <input type="number" name="contextNum" value="12" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">最大tokens</label>
                        <div class="layui-input-block">
                            <input type="number" name="maxTokens" value="2000" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">温度</label>
                        <div class="layui-input-block">
                            <input type="number" name="temperature" value="0.50" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">预安装</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="preinstalled" lay-filter="preinstalled" lay-skin="switch" />
                            <input type="text" style="display:none" name="preinstalled" value="1" />
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">安装量</label>
                        <div class="layui-input-block">
                            <input type="number" name="installed" value="" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">语言</label>
                        <div class="layui-input-inline">
                            <select name="language" lay-verify="required">
                                <option value="zh_cn">zh_cn</option>
                                <option value="en_us">en_us</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">朗读者</label>
                        <div class="layui-input-inline">
                            <select name="speaker" lay-verify="required">
                                <option value="yingying">莹莹</option>
                                <option value="alloy">Alloy</option>
                                <option value="echo">Echo</option>
                                <option value="fable">Fable</option>
                                <option value="onyx">Onyx</option>
                                <option value="nova">Nova</option>
                                <option value="shimmer">Shimmer</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">数据集</label>
                        <div class="layui-input-block">
                            <div name="dataset" id="dataset"></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">分类</label>
                        <div class="layui-input-block">
                            <div name="category" id="category" value="" ></div>
                        </div>
                    </div>
                    
                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                        lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
        </form>

        <script src="/app/admin/component/layui/layui.js"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        
        <script>

            // 相关接口
            const INSERT_API = "/app/ai/admin/ai-role/insert";
            
            // 字段 头像 avatar
            layui.use(["upload", "layer"], function() {
                let input = layui.$("#avatar").prev();
                input.prev().attr("src", input.val());
                layui.$("#attachment-choose-avatar").on("click", function() {
                    parent.layer.open({
                        type: 2,
                        title: "选择附件",
                        content: "/app/admin/upload/attachment?ext=jpg,jpeg,png,gif,bmp",
                        area: ["95%", "90%"],
                        success: function (layero, index) {
                            parent.layui.$("#layui-layer" + index).data("callback", function (data) {
                                input.val(data.url).prev().attr("src", data.url);
                            });
                        }
                    });
                });
                layui.upload.render({
                    elem: "#avatar",
                    url: "/app/ai/upload/avatar",
                    value: "/app/ai/avatar/ai.png",
                    acceptMime: "image/gif,image/jpeg,image/jpg,image/png",
                    field: "__file__",
                    done: function (res) {
                        if (res.code > 0) return layui.layer.msg(res.msg);
                        this.item.prev().val(res.data.url).prev().attr("src", res.data.url);
                    }
                });
            });
            
            // 字段 模型 model
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.$.ajax({
                    url: "/app/ai/setting/models",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#model").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#model",
                            name: "model",
                            initValue: initValue,
                            data: res.data, 
                            value: "gpt-3.5-turbo-16k",
                            model: {"icon":"hidden","label":{"type":"text"}},
                            clickClose: true,
                            radio: true,
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            // 字段 预安装 preinstalled
            layui.use(["form"], function() {
                layui.$("#preinstalled").attr("checked", layui.$('input[name="preinstalled"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(preinstalled)", function(data) {
                    layui.$('input[name="preinstalled"]').val(this.checked ? 1 : 0);
                });
            })

            // 字段 数据集 dataset
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.$.ajax({
                    url: "/app/ai/admin/ai-dataset/list",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#dataset").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#dataset",
                            name: "dataset",
                            initValue: initValue,
                            data: res.data,
                            model: {"icon":"hidden","label":{"type":"text"}},
                            clickClose: true,
                            radio: true,
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            // 字段 分类 category
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.$.ajax({
                    url: "/app/ai/setting/categories",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#category").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#category",
                            name: "category",
                            initValue: initValue,
                            data: res.data, 
                            toolbar: {"show":true,"list":["ALL","CLEAR","REVERSE"]},
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            //提交事件
            layui.use(["form", "popup"], function () {
                // 字段验证允许为空
                layui.form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });
                layui.form.on("submit(save)", function (data) {
                    layui.$.ajax({
                        url: INSERT_API,
                        type: "POST",
                        dateType: "json",
                        data: data.field,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>
</html>
