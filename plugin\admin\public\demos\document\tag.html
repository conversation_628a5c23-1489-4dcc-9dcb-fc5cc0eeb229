<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>标签组件</title>
		<meta name="renderer" content="webkit">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
	</head>
	<body class="pear-container">
		<div class="layui-row layui-col-space10">
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">开发环境</div>
					<div class="layui-card-body">
						Tag 标签组件
					</div>
				</div>
			</div>

			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								&lt;link rel="stylesheet" href="component/pear/css/pear.css" /&gt;
								 并
								&lt;script src="component/layui/layui.js"&gt;&lt;/script&gt;
								 并
								&lt;script src="component/pear/pear.js"&gt;&lt;/script&gt;
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">主题风格</div>
					<div class="layui-card-body">
						<div class="layui-btn-container tag">
							<button lay-id="11" type="button" class="tag-item tag-item-normal">网站设置</button>
							<button lay-id="22" type="button" class="tag-item">用户管理</button>
							<button lay-id="33" type="button" class="tag-item tag-item-warm">权限分配</button>
							<button lay-id="44" type="button" class="tag-item tag-item-danger">商品管理</button>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
							<div class="layui-btn-container tag">
							    <button lay-id="11" type="button" class="tag-item tag-item-normal">网站设置</button>
							    <button lay-id="22" type="button" class="tag-item">用户管理</button>
							    <button lay-id="33" type="button" class="tag-item tag-item-warm">权限分配</button>
							    <button lay-id="44" type="button" class="tag-item tag-item-danger">商品管理</button>
							</div>
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">动态操作</div>
					<div class="layui-card-body">
						<div class="layui-btn-container tag" lay-filter="demo" lay-allowclose="true" lay-newTag="true">
							<button lay-id="11" type="button" class="tag-item tag-item-normal">网站设置</button>
							<button lay-id="22" type="button" class="tag-item">用户管理</button>
							<button lay-id="33" type="button" class="tag-item tag-item-warm">权限分配</button>
							<button lay-id="44" type="button" class="tag-item tag-item-danger">商品管理</button>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								tag.add('demo', {text: '新选项',id: 12})
								 
								tag.delete('demo', '44');
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">圆角风格</div>
					<div class="layui-card-body">
						<div class="layui-btn-container tag" lay-filter="test" lay-newTag="true">
							<button lay-id="11" type="button" class="tag-item">网站设置</button>
							<button lay-id="22" type="button" class="tag-item">用户管理</button>
							<button lay-id="33" type="button" class="tag-item">权限分配</button>
							<button lay-id="44" type="button" class="tag-item">商品管理</button>
						    <button lay-id="55" type="button" class="tag-item">订单管理</button>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								<div class="layui-btn-container tag" lay-filter="test" lay-newTags="true">
								    <button lay-id="11" type="button" class="tag-item">网站设置</button>
								    <button lay-id="22" type="button" class="tag-item">用户管理</button>
								    <button lay-id="33" type="button" class="tag-item">权限分配</button>
								    <button lay-id="44" type="button" class="tag-item">商品管理</button>
								    <button lay-id="55" type="button" class="tag-item">订单管理</button>
								</div>
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">删除功能</div>
					<div class="layui-card-body">
						<div class="layui-btn-container tag" lay-allowclose="true">
							<button lay-id="11" type="button" class="tag-item tag-item-danger">网站设置</button>
							<button lay-id="22" type="button" class="tag-item tag-item-danger">用户管理</button>
							<button lay-id="33" type="button" class="tag-item tag-item-danger">权限分配</button>
							<button lay-id="44" type="button" class="tag-item tag-item-danger">商品管理</button>
							<button lay-id="55" type="button" class="tag-item tag-item-danger">订单管理</button>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								<div class="layui-btn-container tag" lay-allowclose="true">
								    <button lay-id="11" type="button" class="tag-item tag-item-danger">网站设置</button>
								    <button lay-id="22" type="button" class="tag-item tag-item-danger">用户管理</button>
								    <button lay-id="33" type="button" class="tag-item tag-item-danger">权限分配</button>
								    <button lay-id="44" type="button" class="tag-item tag-item-danger">商品管理</button>
								</div>
								  
								tag.on('delete(demo)', function(data) {
								    console.log('删除');
								    console.log(this); 
								    console.log(data.index);
								    console.log(data.elem);
								    console.log(data.othis);
								});
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">新建功能</div>
					<div class="layui-card-body">
						<div class="layui-btn-container tag" lay-newTag="true">
							<button lay-id="11" type="button" class="tag-item">网站设置</button>
							<button lay-id="22" type="button" class="tag-item">用户管理</button>
							<button lay-id="33" type="button" class="tag-item">权限分配</button>
							<button lay-id="44" type="button" class="tag-item">商品管理</button>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
							<div class="layui-btn-container tag" lay-newTags="true">
							    <button lay-id="11" type="button" class="tag-item">网站设置</button>
							    <button lay-id="22" type="button" class="tag-item">用户管理</button>
							    <button lay-id="33" type="button" class="tag-item">权限分配</button>
							    <button lay-id="44" type="button" class="tag-item">商品管理</button>
							</div>
							  
							tag.on('add(demo)', function(data) {
							    console.log('新建');
							    console.log(this); 
							    console.log(data.index); 
							    console.log(data.elem); 
							});
							</pre>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
		<script>
			layui.use(['tag', 'code', 'element'], function() {
				var $ = layui.jquery,
					tag = layui.tag; //Tag的切换功能，切换事件监听等，需要依赖tag模块

				layui.code();

				tag.render("test", {
					skin: 'layui-btn layui-btn-primary layui-btn-sm layui-btn-radius', //标签样式
					tagText: '<i class="layui-icon layui-icon-add-1"></i>添加标签' //标签添加按钮提示文本
				});

				var active = {
					tagAdd: function() {
						//新增一个Tag项
						tag.add('demo', {
							text: '新选项' + (Math.random() * 1000 | 0) //用于演示
								,
							id: new Date().getTime() //实际使用一般是规定好的id，这里以时间戳模拟下
						})
					},
					tagDelete: function(othis) {
						//删除指定Tag项
						tag.delete('demo', '44'); //删除：“商品管理”
						othis.addClass('layui-btn-disabled');
					}
				};

				$('.site-demo-active').on('click', function() {
					var othis = $(this),
						type = othis.data('type');
					active[type] ? active[type].call(this, othis) : '';
				});

				tag.on('click(demo)', function(data) {
					console.log('点击');
					console.log(this); //当前Tag标签所在的原始DOM元素
					console.log(data.index); //得到当前Tag的所在下标
					console.log(data.elem); //得到当前的Tag大容器
				});

				tag.on('add(demo)', function(data) {
					console.log('新增');
					console.log(this); //当前Tag标签所在的原始DOM元素
					console.log(data.index); //得到当前Tag的所在下标
					console.log(data.elem); //得到当前的Tag大容器
					console.log(data.othis); //得到新增的DOM对象
					//return false; //返回false 取消新增操作； 同from表达提交事件。
				});

				tag.on('delete(demo)', function(data) {
					console.log('删除');
					console.log(this); //当前Tag标签所在的原始DOM元素
					console.log(data.index); //得到当前Tag的所在下标
					console.log(data.elem); //得到当前的Tag大容器
				});
			});
		</script>
	</body>
</html>
