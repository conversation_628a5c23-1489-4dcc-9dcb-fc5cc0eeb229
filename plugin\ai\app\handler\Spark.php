<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

class Spark extends Base
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = '讯飞星火';

    /**
     * @var string 模型类型
     */
    protected static $type = 'spark';

    /**
     * @var string[] 支持的模型列表
     */
    public static $models = [
        'spark',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'appid' => [
            'name' => 'AppId',
            'type' => 'text',
            'value' => '',
            'desc' => 'AppId',
        ],
        'apikey' => [
            'name' => 'ApiKey',
            'type' => 'text',
            'value' => '',
            'desc' => 'API Key',
        ],
        'secretKey' => [
            'name' => 'SecretKey',
            'type' => 'text',
            'value' => '',
            'desc' => 'Secret Key',
        ],
        'version' => [
            'name' => 'Version',
            'type' => 'text',
            'value' => 3.0,
            'desc' => '版本',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 0,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Spark::class;

    /** 对话
     * @param $data
     * @param $options
     * @return void
     */
    public function completions($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->completions($data, $options);
    }

}