<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>更新页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body>

        <form class="layui-form">

            <div class="mainBox">
                <div class="main-container mr-5">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">订单id</label>
                        <div class="layui-input-block">
                            <input type="text" name="order_id" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">用户id</label>
                        <div class="layui-input-block">
                            <input type="number" name="user_id" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">须支付金额</label>
                        <div class="layui-input-block">
                            <input type="number" name="total_amount" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">已支付总额</label>
                        <div class="layui-input-block">
                            <input type="number" name="paid_amount" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">支付时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="paid_at" id="paid_at" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <div name="state" id="state" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">创建时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="created_at" id="created_at" required lay-verify="required" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">更新时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="updated_at" id="updated_at" required lay-verify="required" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">删除时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="deleted_at" id="deleted_at" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">业务数据</label>
                        <div class="layui-input-block">
                            <textarea name="data" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    
                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit="" lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
        </form>

        <script src="/app/admin/component/layui/layui.js"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        
        <script>

            // 相关接口
            const PRIMARY_KEY = "id";
            const SELECT_API = "/app/ai/admin/ai-order/select" + location.search;
            const UPDATE_API = "/app/ai/admin/ai-order/update";

            // 获取数据库记录
            layui.use(["form", "util", "popup"], function () {
                let $ = layui.$;
                $.ajax({
                    url: SELECT_API,
                    dataType: "json",
                    success: function (res) {
                        
                        // 给表单初始化数据
                        layui.each(res.data[0], function (key, value) {
                            let obj = $('*[name="'+key+'"]');
                            if (key === "password") {
                                obj.attr("placeholder", "不更新密码请留空");
                                return;
                            }
                            if (typeof obj[0] === "undefined" || !obj[0].nodeName) return;
                            if (obj[0].nodeName.toLowerCase() === "textarea") {
                                obj.val(layui.util.escape(value));
                            } else {
                                obj.attr("value", value);
                            }
                        });
                        
                        // 字段 支付时间 paid_at
                        layui.use(["laydate"], function() {
                            layui.laydate.render({
                                elem: "#paid_at",
                                type: "datetime",
                            });
                        })
                        
                        // 字段 状态 state
                        layui.use(["jquery", "xmSelect"], function() {
                            let value = layui.$("#state").attr("value");
                            let initValue = value ? value.split(",") : [];
                            layui.xmSelect.render({
                                el: "#state",
                                name: "state",
                                initValue: initValue,
                                data: [{"value":"paid","name":"已支付"},{"value":"unpaid","name":"未支付"}],
                                model: {"icon":"hidden","label":{"type":"text"}},
                                clickClose: true,
                                radio: true,
                            })
                        });
                        
                        // 字段 创建时间 created_at
                        layui.use(["laydate"], function() {
                            layui.laydate.render({
                                elem: "#created_at",
                                type: "datetime",
                            });
                        })
                        
                        // 字段 更新时间 updated_at
                        layui.use(["laydate"], function() {
                            layui.laydate.render({
                                elem: "#updated_at",
                                type: "datetime",
                            });
                        })
                        
                        // 字段 删除时间 deleted_at
                        layui.use(["laydate"], function() {
                            layui.laydate.render({
                                elem: "#deleted_at",
                                type: "datetime",
                            });
                        })
                        
                        
                        // ajax返回失败
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                        
                    }
                });
            });

            //提交事件
            layui.use(["form", "popup"], function () {
                // 字段验证允许为空
                layui.form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });
                layui.form.on("submit(save)", function (data) {
                    data.field[PRIMARY_KEY] = layui.url().search[PRIMARY_KEY];
                    layui.$.ajax({
                        url: UPDATE_API,
                        type: "POST",
                        dateType: "json",
                        data: data.field,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>

</html>
