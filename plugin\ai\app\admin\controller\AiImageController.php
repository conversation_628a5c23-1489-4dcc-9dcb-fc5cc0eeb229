<?php

namespace plugin\ai\app\admin\controller;

use support\Request;
use support\Response;
use plugin\ai\app\model\AiImage;
use plugin\admin\app\controller\Crud;
use support\exception\BusinessException;

/**
 * AI图片 
 */
class AiImageController extends Crud
{
    
    /**
     * @var AiImage
     */
    protected $model = null;

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->model = new AiImage;
    }
    
    /**
     * 浏览
     * @return Response
     */
    public function index(): Response
    {
        return view('ai-image/index');
    }

    /**
     * 插入
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function insert(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return parent::insert($request);
        }
        return view('ai-image/insert');
    }

    /**
     * 更新
     * @param Request $request
     * @return Response
     * @throws BusinessException
    */
    public function update(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return parent::update($request);
        }
        return view('ai-image/update');
    }

    /**
     * 查询
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function select(Request $request): Response
    {
        $field = $request->get('field', 'id');
        $order = $request->get('order', 'desc');
        [$where, $format, $limit] = $this->selectInput($request);
        $query = $this->doSelect($where, $field, $order);
        $query->with('user');
        return $this->doFormat($query, $format, $limit);
    }

}
