<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

class Midjourney extends Base
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = 'Midjourney';

    /**
     * @var string 模型类型
     */
    protected static $type = 'midjourney';

    /**
     * @var string[] 支持的模型名称
     */
    public static $models = [
        'midjourney',
        'niji',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'api' => [
            'name' => 'api',
            'type' => 'text',
            'value' => 'http://127.0.0.1:8686',
            'desc' => 'http://127.0.0.1:8686',
        ],
        'apiSecret' => [
            'name' => 'Api Secret',
            'type' => 'text',
            'value' => '',
            'desc' => '默认留空',
        ],
        'allowAllFast' => [
            'name' => '非vip快速',
            'type' => 'checkbox',
            'value' => false,
            'desc' => '允许非vip用户使用快速作图',
        ],
        'allRelax' => [
            'name' => '全员慢速',
            'type' => 'checkbox',
            'value' => false,
            'desc' => '强制所有用户使用慢速作图',
        ],
        'hideSpeedOptions' => [
            'name' => '隐藏选项',
            'type' => 'checkbox',
            'value' => false,
            'desc' => '隐藏快速/慢速选项',
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 0,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Midjourney::class;


    /**
     * @param $data
     * @param $options
     * @return void
     */
    public function imagine($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->imagine($data, $options);
    }

    /**
     * @param $data
     * @param $options
     * @return void
     */
    public function action($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->action($data, $options);
    }

    /**
     * @param $data
     * @param $options
     * @return void
     */
    public function status($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->status($data, $options);
    }

}