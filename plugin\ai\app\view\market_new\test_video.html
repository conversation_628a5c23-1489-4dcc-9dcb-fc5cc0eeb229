<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慕课章节列表</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
            height: 100vh; /* 设置页面高度为固定 */
            overflow: hidden; /* 隐藏页面滚动条 */
        }
        .top-image {
            width: 100%;
            height: auto;
        }
        .container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            margin-top: 5vh;
        }
        .content-container {
            display: flex;
            flex-direction: column; /* 竖向排列 */
            justify-content: flex-start;
            align-items: flex-start;
            background: #fff;
            box-shadow: 0 8px 16px 0 rgba(7, 17, 27, .1);
            border-radius: 12px;
            padding: 24px 32px;
            width: 800px; /* 设置宽度 */
            max-height: 70vh; /* 设置白色区域高度小于页面高度 */
            overflow-y: scroll; /* 添加滚动条 */
            scrollbar-width: none; /* Firefox隐藏滚动条 */
            position: relative; /* 相对定位 */
            top: -30px; /* 向上移动一些 */
        }
        .content-container::-webkit-scrollbar {
            display: none; /* Chrome/Safari隐藏滚动条 */
        }
        .chapter-list {
            margin-bottom: 20px; /* 章节之间的间距 */
        }
        .chapter-title {
            font-size: 16px;
            font-weight: 700;
            line-height: 24px;
            color: #1c1f21;
            margin-bottom: 8px;
        }
        .chapter-description {
            margin-top: 2px;
            font-size: 12px;
            color: #545c63;
            line-height: 18px;
            margin-bottom: 12px;
        }
        .chapter {
            margin: 10px 0;
            cursor: pointer;
            padding: 10px;
            border-radius: 5px;
            position: relative;
            display: flex;
            align-items: center;
            list-style: none;
            font-size: 14px;
        }
        .chapter img {
            width: 24px;
            height: 24px;
            margin-right: 10px;
        }
        .chapter span {
            flex-grow: 1;
        }
        .chapter:hover {
            background-color: #e0e0e0;
        }
        .start-button {
            display: none;
            position: absolute;
            right: 10px;
            margin: 12px;
            height: 24px;
            padding: 0 12px;
            font-size: 12px;
            background-color: red;
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            box-shadow: none;
        }
        .chapter:hover .start-button {
            display: inline-block;
        }
        .sidebar {
            margin-left: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
        }
        .sidebar-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .sidebar-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
        }
        .sidebar-item img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 10px;
        }
        .sidebar-item-title {
            font-size: 14px;
            font-weight: bold;
            margin: 5px 0;
        }
        .sidebar-item-price {
            font-size: 12px;
            color: #888;
        }
        .sidebar-item a {
            text-decoration: none;
            color: inherit;
            display: flex;
            align-items: center;
        }
    </style>
</head>
<body>
    <img src="/app/ai/avatar/top_back.png" alt="顶部图片" class="top-image">
    <div class="container">
        <div class="content-container">
            <div class="chapter-list">
                <div class="chapter-title">职业线路与技能提升</div>
                <div class="chapter-description">探讨前端工程师的职业发展路线，包括如何面对行业与职场的双重冲击，如何精通JavaScript，以及如何理解前端工程师的架构学习路线。深入探讨大型项目架构设计的演进，找准前端的核心竞争力，以及高级前端的进阶路径。</div>
                <li class="chapter" onclick="openModal('video1.m3u8')">
                    <img src="/app/ai/avatar/video.png" alt="视频图标">
                    <span>视频1</span>
                    <button class="start-button" onclick="openModal('video1.m3u8'); event.stopPropagation();">开始学习</button>
                </li>
                <li class="chapter" onclick="openModal('video2.m3u8')">
                    <img src="/app/ai/avatar/video.png" alt="视频图标">
                    <span>视频2</span>
                    <button class="start-button" onclick="openModal('video2.m3u8'); event.stopPropagation();">开始学习</button>
                </li>
                <li class="chapter" onclick="openModal('video3.m3u8')">
                    <img src="/app/ai/avatar/video.png" alt="视频图标">
                    <span>视频3</span>
                    <button class="start-button" onclick="openModal('video3.m3u8'); event.stopPropagation();">开始学习</button>
                </li>
            </div>
            <div class="chapter-list">
                <div class="chapter-title">项目管理与协作</div>
                <div class="chapter-description">学习如何有效管理和协作前端开发项目，包括使用敏捷开发方法、任务分配、进度管理和团队协作工具。</div>
                <li class="chapter" onclick="openModal('video4.m3u8')">
                    <img src="/app/ai/avatar/video.png" alt="视频图标">
                    <span>视频4</span>
                    <button class="start-button" onclick="openModal('video4.m3u8'); event.stopPropagation();">开始学习</button>
                </li>
                <li class="chapter" onclick="openModal('video5.m3u8')">
                    <img src="/app/ai/avatar/video.png" alt="视频图标">
                    <span>视频5</span>
                    <button class="start-button" onclick="openModal('video5.m3u8'); event.stopPropagation();">开始学习</button>
                </li>
            </div>
            <div class="chapter-list">
                <div class="chapter-title">高级前端技术</div>
                <div class="chapter-description">深入学习高级前端技术，包括框架的使用、性能优化和代码架构设计等。</div>
                <li class="chapter" onclick="openModal('video6.m3u8')">
                    <img src="/app/ai/avatar/video.png" alt="视频图标">
                    <span>视频6</span>
                    <button class="start-button" onclick="openModal('video6.m3u8'); event.stopPropagation();">开始学习</button>
                </li>
                <li class="chapter" onclick="openModal('video7.m3u8')">
                    <img src="/app/ai/avatar/video.png" alt="视频图标">
                    <span>视频7</span>
                    <button class="start-button" onclick="openModal('video7.m3u8'); event.stopPropagation();">开始学习</button>
                </li>
                <li class="chapter" onclick="openModal('video8.m3u8')">
                    <img src="/app/ai/avatar/video.png" alt="视频图标">
                    <span>视频8</span>
                    <button class="start-button" onclick="openModal('video8.m3u8'); event.stopPropagation();">开始学习</button>
                </li>
            </div> 
        </div>
        <div class="sidebar">
            <div class="sidebar-title">推荐课程</div>
            <div class="sidebar-item">
                <a href="https://example.com/course1" target="_blank">
                    <img src="/app/ai/avatar/test1.png" alt="课程1">
                    <div>
                        <div class="sidebar-item-title">课程1标题</div>
                        <div class="sidebar-item-price">价格：￥99</div>
                    </div>
                </a>
            </div>
            <div class="sidebar-item">
                <a href="https://example.com/course2" target="_blank">
                    <img src="/app/ai/avatar/test2.png" alt="课程2">
                    <div>
                        <div class="sidebar-item-title">课程2标题</div>
                        <div class="sidebar-item-price">价格：￥199</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</body>
</html>