<!doctype html>
<html lang="zh-cn" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="/app/ai/favicon.ico" />
    <link rel="stylesheet" href="/app/ai/css/bootstrap.min.css?v=5.3" rel="stylesheet">
    <link rel="stylesheet" href="/app/ai/css/app.css?v=<?=ai_css_version()?>">
    <link rel="stylesheet" href="/app/ai/painting/css/index.css?v=<?=ai_css_version()?>">
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>
    <title>webman AI作图-Midjourney 绘画</title>
</head>

<body data-bs-theme="light">

<div class="container-fluid h-100" id="app">
    <div class="h-100 d-flex position-relative" @dragover.prevent @drop="handleDrop" @paste="handlePaste" @dragenter="handleDragEnter" @dragleave="handleDragLeave">
        <div :class="{'shadow-selected':isDraggingOver, 'position-relative':!isMobile}"  class="user-select-none painting-left-box dark-bg border-end" v-show="leftBoxShow||!isMobile">
            <div class="header">Midjourney 绘画</div>
            <div class="px-3 pb-4" style="height:calc(100% - 50px);overflow-y:scroll;">
                <div class="mb-1 mt-3 f13">模型</div>
                <div class="d-flex justify-content-between f12 text-white">
                    <div :class="{selected:image.model==='midjourney'}" @click="image.model='midjourney'" class="model-midjourney cursor-pointer d-flex align-items-center justify-content-center rounded">Midjourney</div>
                    <div :class="{selected:image.model==='niji'}"  @click="image.model='niji'"class=" model-niji cursor-pointer d-flex align-items-center justify-content-center rounded">Niji动漫</div>
                </div>

                <div class="mb-1 mt-3 f13">版本</div>
                <select class="form-select form-select-sm" v-model="image.version">
                    <option v-for="(name, value) in defaultVersions" :value="value">{{name}}</option>
                </select>

                <div class="mb-1 mt-3 f13">尺寸</div>
                <div class="d-flex">
                    <template v-for="item in ['1:1','4:3','3:2', '16:9']">
                        <div :class="{selected:image.ar===item}" @click="image.ar=item" class="me-1 rounded py-2 f12 cursor-pointer ratio-item border form-control-bg">
                            <img :src="'/app/ai/painting/img/'+item.replace(':','x')+'.png'" width="30" height="30">
                            <div class="mt-1">{{item}}</div>
                        </div>
                    </template>
                </div>
                <div class="d-flex mt-1">
                    <template v-for="item in ['2:3', '9:16']">
                        <div :class="{selected:image.ar===item}" @click="image.ar=item" class="me-1 rounded py-2 f12 cursor-pointer ratio-item border form-control-bg">
                            <img :src="'/app/ai/painting/img/'+item.replace(':','x')+'.png'" width="30" height="30">
                            <div class="mt-1">{{item}}</div>
                        </div>
                    </template>

                    <div :class="{selected:image.ar==='customized'}" @click="image.ar='customized'" class="me-1 rounded py-2 f12 cursor-pointer ratio-customized dark-border form-control-bg">
                        <div>自定义</div>
                        <div class="d-flex justify-content-between mt-1">
                            <input v-model="image.widthRatio" class="ms-3 ratio-input border rounded" type="text" placeholder="宽">
                            <span>:</span>
                            <input v-model="image.heightRatio" class="me-3 ratio-input border rounded" type="text" placeholder="高">
                        </div>
                    </div>
                </div>

                <div class="mt-3 f13">参考图</div>
                <div class="d-flex flex-wrap justify-content-between">
                    <template v-for="(ref, index) in image.refs">
                        <div class="d-flex f12 rounded form-control-bg mt-2 ref-img-box dark-border">
                            <div class="d-flex align-items-center justify-content-center cursor-pointer overflow-hidden position-relative w-100 object-fit-contain">
                                <img :src="ref" class="img-fluid rounded" style="max-height:100%"/>
                                <span class="icon-btn position-absolute f13 cursor-pointer dark-bg rounded ref-img-del py-1 px-2" @click.stop="image.refs.splice(index, 1)">&#xe680;</span>
                            </div>
                        </div>
                    </template>
                    <div class="d-flex f12 rounded form-control-bg mt-2 ref-img-box dark-border" v-if="image.refs.length<6">
                        <div class="d-flex align-items-center justify-content-center cursor-pointer overflow-hidden position-relative h-100 w-100 rounded" @click="openUploadImage()">
                            <div v-if="!uploadPercent"><span class="iconfont f13">&#xea04;</span>上传参考图<div class="text-center">(支持拖拽粘贴)</div></div>
                            <div v-else class="progress w-75" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                <div class="progress-bar overflow-visible" :style="'width: '+uploadPercent+'%'">{{uploadPercent}}%</div>
                            </div>
                            <form ref="uploadForm"><input type="file" name="image.refs[0]" class="d-none" ref="uploadInput" @change="uploadImage"></form>
                        </div>
                    </div>
                </div>

                <div class="mb-1 mt-2 f13 d-flex justify-content-between">
                    <span>相似度 (iw)</span>
                    <span><input type="text" v-model="image.iw" class="ratio-input dark-border px-1 rounded f12"></span>
                </div>
                <div class="form-group align-items-center">
                    <input type="range" class="form-range" min="0" max="2" step="0.1" v-model="image.iw">
                </div>

                <div class="mb-1 mt-3 f13">风格</div>
                <template v-for="styles in defaultStyles">
                    <div class="d-flex mt-1">
                        <template v-for="(name, key) in styles">
                            <div :class="{selected:image.styles.includes(key)}" @click="toggleStyle(key)"
                                 class="me-1 rounded py-2 f12 cursor-pointer text-center dark-border form-control-bg" style="width:68px">
                                <div>{{name}}</div>
                            </div>
                        </template>
                    </div>
                </template>

                <div class="mb-1 mt-4 f13 d-flex justify-content-between">
                    <span>创造性 (chaos)</span>
                    <span><input type="text" v-model="image.chaos" class="ratio-input dark-border px-1 rounded f12"></span>
                </div>
                <div class="form-group align-items-center">
                    <input type="range" class="form-range" min="0" max="100"  v-model="image.chaos">
                </div>

                <div class="mb-1 mt-3 f13 d-flex justify-content-between">
                    <span>艺术性 (stylize)</span>
                    <span><input type="text" v-model="image.stylize" class="ratio-input dark-border px-1 rounded f12"></span>
                </div>
                <div class="form-group align-items-center">
                    <input type="range" class="form-range" min="0" max="1000" v-model="image.stylize">
                </div>

                <div class="mb-1 mt-3 f13">其它参数</div>
                <input type="text" class="form-control form-control-sm dark-border" v-model="image.others">

                <div class="mb-1 mt-3 f13" v-if="!settings.hideSpeedOptions">出图速度</div>
                <div class="row" v-if="!settings.hideSpeedOptions">
                    <div class="col-6">
                        <div class="alert f13 d-flex flex-column align-items-center cursor-pointer p-0 py-2 form-control-bg dark-border mb-1"
                             :class="{'selected':image.fast, 'cursor-not-allowed':!isVip()&&!settings.allowAllFast}"
                             @click="tryToUseFastModel()">
                            <div>{{isVip()||!settings.allowAllFast?'VIP':''}}快速出图</div> <div class="f12">约30秒</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="alert f13 d-flex flex-column align-items-center cursor-pointer p-0 py-2 form-control-bg dark-border mb-1" :class="{'selected':!image.fast}" @click="image.fast=false">
                            <div>普通出图</div> <div class="f12">约3分钟</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="painting-right-box position-relative gray-bg rounded-end">
            <div class="header bg-gray d-flex justify-content-between"><span>{{isMobile?'Midjourney 绘画':''}}</span> <b class="iconfont" @click.stop="showOptions=!showOptions">&#xe9f8;</b></div>
            <ul class="list-unstyled overflow-auto mb-0 message-list" ref="messageBox" :class="{empty:!images.length}" style="height:calc(100% - 80px)">
                <template v-for="image in images" :key="image.id">
                        <li class="py-2">
                            <div class="message-item position-relative d-flex">
                                <img v-show="!isMobile" class="avatar mt-1 flex-shrink-0 me-3" src="/app/ai/painting/img/mj-logo.png"/>
                                <div class="midjourney message-body w-100">
                                    <span class="d-flex align-items-center">
                                        <span>Midjourney Bot</span>  <span class="badge text-bg-primary icon ms-1">&#xe9da; 机器人</span> <span class="f13 text-muted ms-1">{{formatTime(image.time)}}</span>
                                    </span>
                                    <div>
                                        <span>{{image.text}}</span>
                                        <template v-for="(ref, index) in image.refs">
                                            <a class="ms-1" v-if="ref" @click="previewImage(ref)"> 参考图{{image.refs.length > 1 ? index+1 : ''}}</a>
                                        </template>
                                        <span v-if="getStyleNames(image).length"> - {{getStyleNames(image).join(" ")}}</span>
                                        <span class="ms-1" v-if="image.others">{{image.others}}</span>
                                        <span v-if="image.model==='niji'"> - Niji</span>
                                        <span v-if="image.action"> #{{image.action}}</span>
                                    </div>
                                    <div class="w-100" v-if="hasChinese(image.text)">
                                        <span class="text-muted">{{image.finalPrompt || image.prompt}}</span>
                                    </div>
                                    <div class="markdown-body mt-1" v-if="image.type==='text'" v-html="markdown('> ' + image.errorMessage)"></div>
                                    <div class="position-relative d-inline-block img-loading mt-1" v-if="image.type!=='text'" :style="{width:imageSize(image).width + 'px', height:imageSize(image).height + 'px'}">
                                        <img @drag="handleDrag" class="rounded" v-if="image.smallUrl||image.imageUrl" :src="image.smallUrl||image.imageUrl" @load="onload(image, $event)" @click="previewImage(image.imageUrl)" :style="{width:imageSize(image).width + 'px', height:imageSize(image).height + 'px'}"/>
                                        <div style="position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);opacity:.7" v-if="!image.completed&&image.type==='images'" :class="{'fade-out':image.loadedProgress==='100%'}">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 40 40">
                                                <circle cx="20" cy="20" r="10" stroke="#ebeef5" stroke-width="20" fill="#ebeef5"/>
                                                <circle :stroke-dasharray="2*3.14*8" :stroke-dashoffset="2*3.14*8*(1-parseInt(image.loadedProgress)/100)" cx="20" cy="20" r="8" stroke="#2f81f7" stroke-width="16" fill="none" stroke-linecap="butt" transform="rotate(-90, 20, 20)"/>
                                            </svg>
                                        </div>
                                    </div>

                                    <div v-for="(buttons, index) in image.buttons" class="image-tools">
                                        <div :key="index">
                                            <template v-for="(button, id) in buttons" >
                                                <button v-if="buttonInfo(button)" :title="buttonInfo(button).title" :class="{'bg-primary': button.clicked}" :key="id" class="btn btn-dark border-0 px-2 px-md-4 me-2 mt-2" @click="action(image, button)">{{buttonInfo(button).name}}</button>
                                            </template>
                                            <a v-if="index===image.buttons.length-1 && (image.downloadUrl||image.imageUrl)" :href="image.downloadUrl||image.imageUrl" class="btn btn-dark border-0 px-2 px-md-4 me-2 mt-2" title="下载" download>⤵️</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </template>
            </ul>

            <div class="p-2 position-absolute prompt-box" :class="{'px-3':!isMobile}" style="bottom:0;left:0;right:0;">
                <div class="position-relative d-flex">
                    <textarea placeholder="请输入图像内容" class="w-100 border-0 f1 mx-2 form-control form-control-shadow" style="padding-right: 3.4em;" ref="content" v-model="image.text" @input="adjustTextareaHeight"></textarea>
                    <button :disabled="!image.text&&image.refs.length<2&&image.styles.length<1" @click="draw(this.image)" class="btn btn-primary position-absolute iconfont rounded f18" style="right:.7em;bottom:.8em;padding-right:12px;padding-left:12px;">&#xea1d;</button>
                </div>
            </div>
        </div>
    </div>
    <div class="dark-bg" :class="{'box-mask':leftBoxShow, 'box-collapse':!leftBoxShow}" @click="leftBoxShow=!leftBoxShow" v-show="isMobile"><span>{{!leftBoxShow?"&#xe60b;":""}}</span></div>
    <div class="flex-column justify-content-center position-fixed" style="bottom:1.5rem;" :style="isMobile ? 'right:0.8%' : 'right:2.5%'" :class="{'d-flex':images.length>1, 'd-none':images.length<2}">
        <div class="iconfont mb-1 scroll-top" @click="scrollToTop(true)">&#xea35;</div>
        <div class="iconfont scroll-bottom" @click="scrollToBottom(true)">&#xe9e5;</div>
    </div>

    <div class="modal fade" id="zoomModal" tabindex="-1" role="dialog" aria-labelledby="zoomModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="zoomModalLabel">扩图</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <textarea class="w-100 f1 form-control light-bg" v-model="imageToEdit.text" rows="3"></textarea>
                    <div class="mb-1 mt-4 f13 d-flex justify-content-between">
                        <span>扩图(ZoomOut)</span>
                        <span><input type="text" v-model="imageToEdit.zoom" class="ratio-input border-0 light-bg rounded px-1"> 倍</span>
                    </div>
                    <div class="form-group align-items-center">
                        <input type="range" class="form-range" min="1" max="2" step="0.1"  v-model="imageToEdit.zoom">
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click="doZoom()">提交</button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="varyModal" tabindex="-1" role="dialog" aria-labelledby="varyModallLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="varyModallLabel">局部重绘</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-1 f13 d-flex justify-content-between">
                        <div class="d-flex justify-content-center flex-column align-items-center w-100">
                            <div class="main">
                                <div class="image" @contextmenu.prevent>
                                    <img :src="editor.src" @load="editorSetCanvas"/>
                                    <canvas
                                        ref="canvas"
                                        @mousedown="editorOnMouseDown"
                                        @touchstart="editorOnMouseDown"
                                        @mouseup="editorOnMouseUp"
                                        @touchend="editorOnMouseUp"
                                        @mousemove="editorOnMouseMove"
                                        @touchmove.prevent="editorOnMouseMove"
                                    ></canvas>
                                </div>
                            </div>
                            <div class="actions f26 user-select-none">
                                <span @click="editorSelectLassoTool" class="iconfont light-bg mx-1 p-2 rounded" :class="{'text-primary':editor.selectedTool==='lasso'}">&#xe978;</span>
                                <span @click="editorSelectRectangleTool" class="iconfont light-bg mx-1 p-2 rounded" :class="{'text-primary':editor.selectedTool==='rectangle'}">&#xe95b;</span>
                                <span @click="editorUndo" class="iconfont rounded light-bg mx-1 p-2 rounded">&#xe611;</span>
                            </div>
                        </div>
                    </div>
                    <textarea class="w-100 f1 form-control mt-3 light-bg" v-model="imageToEdit.text" rows="3"></textarea>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click="doVary()">提交</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 绘画顶部右侧菜单 -->
    <div class="top-right-options-box shadow-sm border-top rounded" style="z-index:10000" @click.stop v-show="showOptions" :class="{'slide-up':showOptions}">
        <a class="dropdown-item py-2 px-4 cursor-pointer" @click="clearImages()">清屏</a>
    </div>

</div>

<!-- 图片预览 -->
<div class="overlay img-preview" style="display:none;position:fixed">
    <span class="close">&times;</span>
    <img alt="展示图片">
</div>

<!-- vue -->
<script type="text/javascript" src="/app/ai/js/vue.global.js"></script>
<script type="text/javascript" src="/app/ai/js/markdown-it.min.js?v=<?=ai_css_version()?>"></script>

<script type="module" src="/app/ai/painting/js/index.js?v=<?=ai_js_version()?>"></script>

<script src="/app/user/js/webman.js"></script>

<script type="module">
    $(document).click(function () {
        try {window.parent.ai.hideAll();} catch (e) {}
        painting.showOptions = false;
    });
    try {
        $(document.body).attr("data-bs-theme", window.parent.ai.theme);
        parent.ai.showIframe();
        painting.scrollToBottom(true, false);
    } catch (e) {}
</script>

<!-- markdown css -->
<link rel="stylesheet" href="/app/ai/css/github-markdown.css?v=<?=ai_js_version()?>">

</body>
</html>
