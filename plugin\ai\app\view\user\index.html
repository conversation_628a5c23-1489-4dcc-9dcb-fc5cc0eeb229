<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/app/ai/css/bootstrap.min.css?v=5.3" rel="stylesheet">
    <link href="/app/ai/css/app.css?v=<?=ai_css_version()?>" rel="stylesheet">
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>
    <title>webman AI 我的详情</title>
</head>

<body data-bs-theme="light">
<div class="header">我的信息</div>
<div class="container overflow-scroll" style="height: calc(100% - 45px)">

    <div class="row">

        <div class="col-12 pt-4">

            <div class="mb-4 card border-0 shadow-sm" style="min-height:80vh">
                <div class="card-body pb-3">
                    <div class="d-flex justify-content-center">
                        <div class="rounded-pill position-relative avatar-box <?=$aiUser['vip']?'vip':''?>">
                            <a href="/app/user" target="_blank"><img src="<?=htmlspecialchars($user['avatar']??'')?>" height="96" width="96" class="rounded-pill"></a>
                            <?php if($aiUser['vip']){ ?>
                            <div class="bg-success text-white px-3 rounded vip-badge">VIP</div>
                            <?php }?>
                        </div>
                    </div>
                    <table class="table mt-4">
                        <tbody>
                        <tr>
                            <td style="width:170px" class="py-3">昵称</td>
                            <td class="text-secondary py-3"><?=htmlspecialchars($user['nickname']??'')?></td>
                        </tr>
                        <tr>
                            <td class="py-3">用户名</td>
                            <td class="text-secondary py-3"><?=htmlspecialchars($user['username']??'')?></td>
                        </tr>
                        <tr>
                            <td class="py-3">剩余积分</td>
                            <td class="text-secondary py-3"><?=$userinfo['score']??'0'?></td>
                        </tr>
                        <tr>
                            <td class="py-3">会员</td>
                            <td class="text-secondary py-3"><?=$aiUser['vipStr']?></td>
                        </tr>
                        <tr>
                            <td class="py-3">到期时间</td>
                            <td class="text-secondary py-3"><?=substr($aiUser['expired_at']?:'', 0, 10)?></td>
                        </tr>
                        <?php if($modelTypes['gpt3']??false){ ?>
                        <tr>
                            <td class="py-3">GPT3.5余量</td>
                            <td class="text-secondary py-3"><?=$balance['gpt3']??0?></td>
                        </tr>
                        <?php }?>
                        <?php if($modelTypes['gpt4']??false){ ?>
                        <tr>
                            <td class="py-3">GPT4余量</td>
                            <td class="text-secondary py-3"><?=$balance['gpt4']??0?></td>
                        </tr>
                        <?php }?>
                        <?php if($modelTypes['midjourney']??false){ ?>
                        <tr>
                            <td class="py-3">Midjourney余量</td>
                            <td class="text-secondary py-3"><?=$balance['midjourney']??0?></td>
                        </tr>
                        <?php }?>
                        <tr class="d-md-none">
                            <td>开启暗黑主题</td>
                            <td>
                                <div class="form-check form-switch">
                                    <input id="themeCheckbox" class="form-check-input" type="checkbox" role="switch" style="width:3em;height:1.5em;margin-top:.1em;">
                                    <label class="form-check-label" for="themeCheckbox"></label>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <div class="btn-list">
                        <div>
                        <?php if($vipEnabled){ ?><a class="btn btn-primary btn-block mt-3" onclick="switchVip()">充值AI会员</a><?php } ?>
                        <a class="btn btn-secondary btn-block mt-3" onclick="logout()">退出AI账户</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>

<script>
    function switchVip() {
        window.parent.ai.switchModule('vip')
    }
    function logout() {
        $.ajax({
            url: '/app/user/logout',
            success: () => {
                window.parent.ai.loadUserInfo();
                location.reload();
            }
        });
    }
    $('#themeCheckbox').change(function() {
        parent.ai.toggleTheme($(this).is(':checked') ? 'dark' : 'light');
    });
    $(document).click(function () {
        try {window.parent.ai.hideAll();} catch (e) {}
    });
    try {
        $(document.body).attr("data-bs-theme", window.parent.ai.theme);
        $('#themeCheckbox').prop('checked', window.parent.ai.theme === 'dark');
        parent.ai.showIframe();
    } catch (e) {}
</script>
<style>
    .avatar-box {
        border:10px solid var(--bs-card-bg);padding:10px;
    }
    .avatar-box.vip {
        border:10px solid rgb(var(--bs-success-rgb));
    }
    .vip-badge {
        border: 8px solid var(--bs-card-bg);position: absolute;bottom:-20px;left: 50%;transform: translateX(-50%);
    }
    .btn-list {
        display: flex;
        justify-content: center;
    }
    .btn-block {
        display: inline-block;
        width: initial !important;
        margin: 0 1rem;
    }
    .btn-block+.btn-block {
        margin-top: 0;
    }
    .text-secondary {
        color: var(--bs-body-color) !important;
    }
    .table {
        --bs-table-color: var(--bs-body-color);
    }
    @media (max-width: 768px) {
        .btn-list {
            display: block;
        }
        .btn-block {
            display: block;
            width: 100%;
        }
        .btn-block+.btn-block {
            margin-top: .5rem;
        }
    }
</style>

</body>
</html>
