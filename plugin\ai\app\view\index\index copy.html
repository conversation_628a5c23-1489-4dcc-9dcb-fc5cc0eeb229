<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>太空人星际漫游</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: #0a0a2a;
      font-family: Arial, sans-serif;
    }
    
    .container {
      position: relative;
      width: 100vw;
      height: 100vh;
    }
    
    .stars {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    
    .star {
      position: absolute;
      background-color: white;
      border-radius: 50%;
      animation: twinkle 3s infinite;
    }
    
    .astronaut {
      position: absolute;
      width: 100px;
      height: 100px;
      background-image: url('/api/placeholder/100/100');
      background-size: contain;
      background-repeat: no-repeat;
      animation: float 20s linear infinite;
      transform-origin: center center;
      filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
    }
    
    .controls {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 10px;
      z-index: 100;
    }
    
    button {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.5);
      padding: 8px 15px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;
    }
    
    button:hover {
      background-color: rgba(255, 255, 255, 0.4);
    }
    
    .planet {
      position: absolute;
      border-radius: 50%;
      filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.3));
    }
    
    @keyframes float {
      0% {
        transform: translate(-100px, 100px) rotate(0deg);
      }
      25% {
        transform: translate(calc(100vw - 100px), 200px) rotate(90deg);
      }
      50% {
        transform: translate(calc(100vw - 200px), calc(100vh - 200px)) rotate(180deg);
      }
      75% {
        transform: translate(100px, calc(100vh - 100px)) rotate(270deg);
      }
      100% {
        transform: translate(-100px, 100px) rotate(360deg);
      }
    }
    
    @keyframes twinkle {
      0% { opacity: 0.2; }
      50% { opacity: 1; }
      100% { opacity: 0.2; }
    }
    
    .trail {
      position: absolute;
      width: 5px;
      height: 5px;
      background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
      border-radius: 50%;
      pointer-events: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="stars" id="stars"></div>
    <div class="astronaut" id="astronaut"></div>
    <div class="controls">
      <button id="speedUp">加速</button>
      <button id="speedDown">减速</button>
      <button id="addPlanet">添加行星</button>
    </div>
  </div>

  <script>
    // 创建星星
    const starsContainer = document.getElementById('stars');
    const astronaut = document.getElementById('astronaut');
    const container = document.querySelector('.container');
    
    let animationSpeed = 20; // 初始速度
    let trailInterval;
    let trailElements = [];
    
    // 创建星星
    function createStars(count) {
      for (let i = 0; i < count; i++) {
        const star = document.createElement('div');
        star.className = 'star';
        
        // 随机大小
        const size = Math.random() * 3;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        
        // 随机位置
        star.style.left = `${Math.random() * 100}%`;
        star.style.top = `${Math.random() * 100}%`;
        
        // 随机闪烁延迟
        star.style.animationDelay = `${Math.random() * 3}s`;
        
        starsContainer.appendChild(star);
      }
    }
    
    // 创建行星
    function createPlanet() {
      const planet = document.createElement('div');
      planet.className = 'planet';
      
      // 随机大小
      const size = 30 + Math.random() * 70;
      planet.style.width = `${size}px`;
      planet.style.height = `${size}px`;
      
      // 随机位置 (保持在可视区域内)
      const maxX = window.innerWidth - size;
      const maxY = window.innerHeight - size;
      planet.style.left = `${Math.random() * maxX}px`;
      planet.style.top = `${Math.random() * maxY}px`;
      
      // 随机颜色
      const colors = ['#ff9e7d', '#7dceff', '#a4ff7d', '#d67dff', '#ffdd7d'];
      const randomColor = colors[Math.floor(Math.random() * colors.length)];
      planet.style.backgroundColor = randomColor;
      
      container.appendChild(planet);
    }
    
    // 更新动画速度
    function updateSpeed() {
      astronaut.style.animationDuration = `${animationSpeed}s`;
    }
    
    // 创建太空人轨迹效果
    function createTrail() {
      clearInterval(trailInterval);
      
      trailInterval = setInterval(() => {
        const trail = document.createElement('div');
        trail.className = 'trail';
        
        // 获取太空人当前位置
        const astronautRect = astronaut.getBoundingClientRect();
        const centerX = astronautRect.left + astronautRect.width / 2;
        const centerY = astronautRect.top + astronautRect.height / 2;
        
        trail.style.left = `${centerX}px`;
        trail.style.top = `${centerY}px`;
        
        document.body.appendChild(trail);
        trailElements.push(trail);
        
        // 轨迹淡出效果
        setTimeout(() => {
          if (trail.parentNode) {
            trail.parentNode.removeChild(trail);
            trailElements = trailElements.filter(el => el !== trail);
          }
        }, 1000);
      }, 50);
    }
    
    // 初始化
    function init() {
      createStars(200);
      createPlanet();
      createPlanet();
      updateSpeed();
      createTrail();
      
      // 事件监听
      document.getElementById('speedUp').addEventListener('click', () => {
        animationSpeed = Math.max(5, animationSpeed - 5);
        updateSpeed();
      });
      
      document.getElementById('speedDown').addEventListener('click', () => {
        animationSpeed += 5;
        updateSpeed();
      });
      
      document.getElementById('addPlanet').addEventListener('click', () => {
        createPlanet();
      });
    }
    
    // 窗口大小改变时重新调整
    window.addEventListener('resize', () => {
      // 清理轨迹元素
      trailElements.forEach(trail => {
        if (trail.parentNode) {
          trail.parentNode.removeChild(trail);
        }
      });
      trailElements = [];
    });
    
    // 启动
    init();
  </script>
</body>
</html>