
[data-bs-theme=dark] {
    --ai-mj-message-bg: #3f3d39;
    --ai-mj-message-border: 2px solid #edaf40;
    --ai-mj-button-bg: #4e5058;
}

[data-bs-theme=light] {
    --ai-mj-message-bg: #fff;
    --ai-mj-message-border: none;
    --ai-mj-button-bg: #6f7d8a;
}

.selected {
    box-shadow: inset 0 0 0 1px var(--bs-primary) !important;
}
.shadow-selected::after {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: inset 0 0 5px 1px var(--bs-primary);
    z-index: 10000;
    pointer-events: none;
}
.message-item {
    padding: 8px 16px;
    border-left: var(--ai-mj-message-border);
    background: var(--ai-mj-message-bg);
}
body {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
}
.model-midjourney {
    height:40px;width:130px;background: url('/app/ai/painting/img/midjourney-model.png'); background-size: cover;background-repeat: no-repeat;text-shadow: rgb(0, 0, 0) 1px 1px 5px;font-weight: 700;
}
.model-niji {
    height:40px;width:130px;background: url('/app/ai/painting/img/niji.png'); background-size: cover;background-repeat: no-repeat;text-shadow: rgb(0, 0, 0) 1px 1px 5px;font-weight: 700;
}
.ratio-item {
    text-align: center;width:65px;height:66px;
}
.ratio-customized {
    text-align: center;width:135px;height:66px;
}
.ratio-input {
    width:40px;text-align:center;
}
.ref-img-box {
    height:128px;width:128px;
}
.ref-img-del {
    bottom:12px;right:12px;
    background: rgba(var(--bs-body-bg-rgb), .7);
}
.create-img-btn-box {
    bottom:0;left:0;width:300px;height:60px;z-index:101;
}
ul.message-list li:last-child {
    margin-bottom: 40px;
}
ul.message-list li {
    margin-left: 8%;
    margin-right: 8%;
}
.painting-left-box {
    width:300px;
}
.painting-right-box {
    width: calc(100% - 300px);
    background: var(--ai-message-list-bg);
}
.box-mask {
    display: none;
}
.message-list li.d-flex:first-child {
    margin-top: 1rem !important;
}
.message-body img {
    max-width: 360px;
    height: auto;
    max-height: 360px;
}
.scroll-top, .scroll-bottom {
    background:#000;
    opacity: .2;
    color:#fff;
    padding-bottom: .12rem!important;
    padding-top: .12rem!important;
}
.scroll-top:hover, .scroll-bottom:hover {
    opacity: .5;
}
.scroll-top {
    border-bottom-left-radius: .25rem;
    border-bottom-right-radius: .25rem;
}
.scroll-bottom {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
}
.image-tools .btn {
    min-width: 65px;
    background-color: var(--ai-mj-button-bg);
}
.image-tools .btn.focus, .image-tools .btn:focus, .image-tools .btn:hover, .image-tools .btn.selected {
    background-color: #007bff;
}

.markdown-body blockquote {
    border-left: .25em solid #dc3545 !important;
    padding-top: .5rem !important;
    padding-bottom: .5rem !important;
}

.img-loading {
    display: flex;
    justify-content: center;
    align-items: center;
}

.img-loading {
    max-width: 360px;
    max-height: 360px;
    background: linear-gradient(to right, #985051, #5d8180);
    border-radius: .5rem;
    aspect-ratio: 1/1;
}

.main {
    width: 100%;
    height: 300px;
}
.actions {
    margin-top: 12px;
}
.image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-color: rgba(0, 0, 0, 0.015);
    border-radius: 8px;
}
.image img {
    max-width: 100%;
    max-height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0 auto;
    object-fit: contain;
    border-radius: 8px;
    user-select: none;
}
.image canvas {
    position: absolute;
    top: 0;
}
.prompt-box {
    margin-left: 8%;
    margin-right: 8%;
}

@media (max-width: 768px) {
    ul.message-list li {
        margin-left: 0;
        margin-right: 0;
    }
    .painting-left-box {
        overflow-y: scroll;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 100;
    }
    .painting-right-box {
        width: 100%;
    }
    .message-list {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    .box-mask {
        display: block;
        position: absolute;
        z-index: 99;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: #000;
        opacity: .2;
    }
    .message-body img {
        max-width: 100% !important;
    }
    .box-collapse {
        position: fixed;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        font-family: iconfont;
        cursor: pointer;
        height: 3.6rem;
        display: flex;
        align-items: center;
        border-bottom-right-radius: .25rem;
        border-top-right-radius: .25rem;
        justify-content: center;
        width: 1.2rem;
        font-size: 1.2rem;
    }
    .message-item {
        border-left: none;
    }
    .image-tools .btn {
        min-width: 50px;
    }
    .markdown-body {
        font-size: 1rem !important;
    }
    .img-loading {
        width: 100%;
        max-width: 100%;
    }
    html, body {
        height: 100%;
        font-size: 17px;
    }
    body {
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
    }
}