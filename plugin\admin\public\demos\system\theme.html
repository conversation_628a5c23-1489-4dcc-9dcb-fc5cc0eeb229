<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>主题预览</title>
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
	</head>
	<body class="pear-container">
		<div class="layui-card">
			<div class="layui-card-header">按钮</div>
			<div class="layui-card-body">
				<button class="pear-btn pear-btn-primary">Button</button>
				<button class="pear-btn pear-btn-primary">Button</button>
			</div>
		</div>
		<div class="layui-card">
			<div class="layui-card-header">输入框</div>
			<div class="layui-card-body">
				<div class="layui-row layui-col-space10">
					<div class="layui-col-md12">
						<input type="text" name="title" placeholder="请输入标题" autocomplete="off" class="layui-input">
					</div>
				</div>
			</div>
		</div>
		<div class="layui-card layui-form" lay-filter="component-form-element">
			<div class="layui-card-header">复选框</div>
			<div class="layui-card-body layui-row layui-col-space10">
				<div class="layui-col-md12">
					<input type="checkbox" name="" title="写作" checked>
					<input type="checkbox" name="" title="发呆">
					<input type="checkbox" name="" title="禁用" disabled>
					<input type="checkbox" name="" title="写作" lay-skin="primary" checked>
					<input type="checkbox" name="" title="发呆" lay-skin="primary">
					<input type="checkbox" name="" title="禁用" lay-skin="primary" disabled>
				</div>
			</div>
		</div>
		<div class="layui-card layui-form" lay-filter="component-form-element">
			<div class="layui-card-header">开关</div>
			<div class="layui-card-body layui-row layui-col-space10">
				<div class="layui-col-md12">
					<input type="checkbox" name="xxx" lay-skin="switch">&nbsp;&nbsp;
					<input type="checkbox" name="yyy" lay-skin="switch" lay-text="ON|OFF" checked>&nbsp;&nbsp;
					<input type="checkbox" name="zzz" lay-skin="switch" lay-text="开启|关闭">&nbsp;&nbsp;
					<input type="checkbox" name="aaa" lay-skin="switch" disabled>&nbsp;&nbsp;
				</div>
			</div>
		</div>
		<div class="layui-card layui-form" lay-filter="component-form-element">
			<div class="layui-card-header">单选框</div>
			<div class="layui-card-body layui-row layui-col-space10">
				<div class="layui-col-md12">
					<input type="radio" name="sex" value="nan" title="男">
					<input type="radio" name="sex" value="nv" title="女" checked>
					<input type="radio" name="sex" value="" title="中性" disabled>
				</div>
			</div>
		</div>
		<div class="layui-card layui-form">
			<div class="layui-card-header">下拉</div>
			<div class="layui-card-body">
				<select name="city" lay-verify="">
					<option value="">请选择一个城市</option>
					<option value="010">北京</option>
					<option value="021">上海</option>
					<option value="0571">杭州</option>
				</select>
			</div>
		</div>
		<div class="layui-card">
			<div class="layui-card-body">
				<table id="user-table" lay-filter="user-table"></table>
			</div>
		</div>

		<script type="text/html" id="user-toolbar">
			<button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
		        <i class="layui-icon layui-icon-add-1"></i>
		        新增
		    </button>
		    <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove">
		        <i class="layui-icon layui-icon-delete"></i>
		        删除
		    </button>
		</script>

		<script type="text/html" id="user-bar">
			<button class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i></button>
		    <button class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i></button>
		</script>

		<script type="text/html" id="user-enable">
			<input type="checkbox" name="enable" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="user-enable" checked = "{{ d.enable == 0 ? 'true' : 'false' }}">
		</script>

		<script type="text/html" id="user-sex">
			{{#if (d.sex == 1) { }}
		    <span>男</span>
		    {{# }else if(d.sex == 2){ }}
		    <span>女</span>
		    {{# } }}
		</script>

		<script type="text/html" id="user-login">
			{{#if (d.login == 0) { }}
		    <span>在线</span>
		    {{# }else if(d.sex == 1){ }}
		    <span>离线</span>
		    {{# } }}
		</script>

		<script type="text/html" id="user-createTime">
			{{layui.util.toDateString(d.createTime, 'yyyy-MM-dd')}}
		</script>

		<div class="layui-card">
			<div class="layui-card-header">选项卡</div>
			<div class="layui-card-body">
				<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
					<ul class="layui-tab-title">
						<li class="layui-this">网站设置</li>
						<li>用户管理</li>
						<li>权限分配</li>
						<li>商品管理</li>
						<li>订单管理</li>
					</ul>
					<div class="layui-tab-content" style="height: 30px;">
						<div class="layui-tab-item layui-show"></div>
						<div class="layui-tab-item"></div>
						<div class="layui-tab-item"></div>
						<div class="layui-tab-item"></div>
						<div class="layui-tab-item"></div>
					</div>
				</div>
			</div>
		</div>
		<div class="layui-card">
			<div class="layui-card-header">进度条</div>
			<div class="layui-card-body">
				<div class="layui-progress">
					<div class="layui-progress-bar" lay-percent="30%"></div>
				</div>
				<br />
				<div class="layui-progress">
					<div class="layui-progress-bar" lay-percent="20%"></div>
				</div>
			</div>
		</div>
		<div class="layui-card">
			<div class="layui-card-header">分页</div>
			<div class="layui-card-body">
				<div id="test1"></div>
			</div>
		</div>
		<div class="layui-card">
			<div class="layui-card-header">辅助元素</div>
			<div class="layui-card-body">
				<blockquote class="layui-elem-quote">快乐的时候不敢尽兴，频繁警戒自己保持清醒.</blockquote>
				<blockquote class="layui-elem-quote">路上没有灯火的时候，就点亮自己的头颅.</blockquote>
			</div>
		</div>
		<div class="layui-card">
			<div class="layui-card-header"></div>
			<div class="layui-card-body">
				<ul class="layui-timeline">
					<li class="layui-timeline-item">
						<i class="layui-icon layui-timeline-axis">&#xe63f;</i>
						<div class="layui-timeline-content layui-text">
							<h3 class="layui-timeline-title">8月18日</h3>
							<p>
								layui 2.0 的一切准备工作似乎都已到位。发布之弦，一触即发。
							</p>
						</div>
					</li>
					<li class="layui-timeline-item">
						<i class="layui-icon layui-timeline-axis">&#xe63f;</i>
						<div class="layui-timeline-content layui-text">
							<h3 class="layui-timeline-title">8月16日</h3>
							<p>杜甫的思想核心是儒家的仁政思想，他有“<em>致君尧舜上，再使风俗淳</em>”的宏伟抱负。个人最爱的名篇有：</p>
						</div>
					</li>
					<li class="layui-timeline-item">
						<i class="layui-icon layui-timeline-axis">&#xe63f;</i>
						<div class="layui-timeline-content layui-text">
							<h3 class="layui-timeline-title">8月15日</h3>
							<p>
								中国人民抗日战争胜利72周年
							</p>
						</div>
					</li>
				</ul>
			</div>
		</div>
		<div class="layui-card">
			<div class="layui-card-header">日期选择</div>
			<div class="layui-card-body">
				<input type="text" class="layui-input" id="test2">
			</div>
		</div>
	</body>
	<script src="../../component/layui/layui.js"></script>
	<script src="../../component/pear/pear.js"></script>
	<script>
		layui.use(['table', 'form', 'jquery', 'drawer'], function() {
			let table = layui.table;
			let form = layui.form;
			let $ = layui.jquery;
			let drawer = layui.drawer;

			let MODULE_PATH = "/system/user/";

			let cols = [
				[{
						type: 'checkbox'
					},
					{
						title: '姓名',
						field: 'realName',
						align: 'center'
					},
					{
						title: '性别',
						field: 'sex',
						align: 'center',
						width: 80,
						templet: '#user-sex'
					},
					{
						title: '启用',
						field: 'enable',
						align: 'center',
						templet: '#user-enable'
					},
					{
						title: '登录',
						field: 'login',
						align: 'center',
						templet: '#user-login'
					},
					{
						title: '注册',
						field: 'createTime',
						align: 'center',
						templet: '#user-createTime'
					},
					{
						title: '操作',
						toolbar: '#user-bar',
						align: 'center',
						width: 130
					}
				]
			]

			table.render({
				elem: '#user-table',
				url: '../../demos/data/table.json',
				page: true,
				cols: cols,
				skin: 'line',
				toolbar: '#user-toolbar',
				defaultToolbar: [{
					layEvent: 'refresh',
					icon: 'layui-icon-refresh',
				}, 'filter', 'print', 'exports']
			});

			form.on('switch(user-enable)', function(obj) {
				layer.tips(this.value + ' ' + this.name + '：' + obj.elem.checked, obj.othis);
			});

		})
	</script>
	<script>
		layui.use('form', function() {

		})
	</script>
	<script>
		layui.use('laypage', function() {
			var laypage = layui.laypage;

			laypage.render({
				elem: 'test1' //注意，这里的 test1 是 ID，不用加 # 号
					,
				count: 50 //数据总数，从服务端得到
			});
		});
	</script>
	<script>
		layui.use('laydate', function() {
			var laydate = layui.laydate;

			laydate.render({
				elem: '#test2' //指定元素
			});
		});
	</script>
</html>
