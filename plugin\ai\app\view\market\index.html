<!DOCTYPE html>
<html lang="zh-cn">

         <head>
    <style>
        body {
            transition: opacity ease-in 0.2s;
        }

        body[unresolved] {
            opacity: 0;
            display: block;
            overflow: hidden;
            position: relative;
        }

        .avatare {
            width: 3.5rem;
            height: 3.5rem;
            background-color: #ddd;
        }

        .shadow-sme {
            box-shadow: rgb(235, 238, 253) 0px 3px 10px;
        }

        .header-2 {
            font-size: 24px;
            font-family: Arial, sans-serif;
            display: inline-block;
            overflow: hidden;
            /* 隐藏溢出的内容 */
            white-space: nowrap;
            /* 防止文本换行 */
        }

        .card-container {
            transition: transform 0.3s ease;
        }

        .card-container:hover {
            transform: scale(1.1);
        }

        .cursor {
            border-right: 0.1em solid black;
            /* 光标 */
            animation: blink 0.5s step-end infinite;
        }

        @keyframes blink {
            50% {
                border-color: transparent;
            }
        }

        .monica-reading-highlight {
            animation: fadeInOut 1.5s ease-in-out;
        }

        @keyframes fadeInOut {

            0%,
            100% {
                background-color: transparent;
            }

            30%,
            70% {
                background-color: rgba(2, 118, 255, 0.2);
            }
        }

        input.form-control::placeholder {
            height: 40px;
            font-size: 14px;
            color: rgb(168, 171, 178);
            opacity: 1;
        }
    </style>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/app/ai/css/bootstrap.min.css?v=5.3" rel="stylesheet" />
    <link href="/app/ai/css/app.css?v=<?=ai_css_version()?>" rel="stylesheet" />
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>
    <!-- navigation.f9cec2d9.css-->
    <link rel="stylesheet" href="/app/ai/static/index.css" />
    <link rel="stylesheet" href="/app/ai/static/index_1.css" />
    <link rel="stylesheet" href="/app/ai/static/index_2.css" />
    <link rel="stylesheet" href="/app/ai/static/navigation.css" />
    <link rel="stylesheet" href="/app/ai/static/swiper.css" />
    <link rel="stylesheet" href="/app/ai/static/el-scrollbar.css" />
    <link rel="stylesheet" href="/app/ai/static/index1.css" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <script src="/app/ai/js/jquery.min.js"></script>
    <script src="/app/ai/js/bootstrap.bundle.min.js?v=5.3"></script>
    <style type="text/css">
        :root body {
            --color-primary: #43cea2;
            --color-minor: #185a9d;
            --color-btn-text: white;
            --el-color-primary: #43cea2;
            --el-color-primary-dark-2: rgb(54, 165, 130);
            --el-color-primary-light-3: rgb(123, 221, 190);
            --el-color-primary-light-5: rgb(161, 231, 209);
            --el-color-primary-light-7: rgb(199, 240, 227);
            --el-color-primary-light-8: rgb(217, 245, 236);
            --el-color-primary-light-9: rgb(236, 250, 246);
        }
    </style>
    <title>webman AI - 市场</title>
</head>

<body data-bs-theme="light" style="background: white">
    <div class="header">应用市场</div>
    <div index_1="" class="cunstom-header" style="padding-top: 30px">
        <div index_1="" class="header-1">亚马逊AI助手 Plus</div>
        <div index_1="" class="header-2">
            <span class="header-2_text" index_1=""></span>
            <span class="cursor" index_1=""></span>
        </div>
    </div>
    <!-- <div index_1="" class="mt-[16px] ml-[16px] search">
        <div index_1="" class="el-input el-input--large el-input--suffix"
            style="width: 99%; height: 40px; border-radius: 15px">
            <div class="el-input__wrapper">
                <input class="el-input__inner" type="text" autocomplete="off" tabindex="0" placeholder="请输入关键词搜索"
                    id="el-id-100-4" /><span class="el-input__suffix"><span class="el-input__suffix-inner"><i index_1=""
                            class="el-icon el-input__icon"><svg index_1="" xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 1024 1024">
                                <path fill="currentColor"
                                    d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704z">
                                </path>
                            </svg></i>
                    </span>
                </span>
            </div>
        </div>
    </div> -->
    <!-- <div class="container-fluid p-4 overflow-scroll light-bg" style="height: calc(100% - 45px)"> -->
    <div index_1="" class="flex-1 min-h-0">
        <div class="row">
            <div class="col-12 pt-2" id="app">
                <div v-cloak style="text-align: center">
                    <!-- <div class="d-inline-block me-3">
                        <div class="d-flex align-items-center flex-wrap">
                            <button class="btn btn-sm btn-outline-secondary me-3 mb-3 white-bg border"
                                @click="category=''" :class="{selected:category===''}">
                                全部
                            </button>
                            <button v-for="item in categories" @click="category=item"
                                class="btn btn-sm btn-outline-secondary me-3 mb-3 white-bg border"
                                :class="{selected:category===item}">
                                {{item}}
                            </button>
                        </div>
                    </div> -->

                    <div class="d-inline-block mb-3">
                        <div class="d-flex align-items-center">
                            <input class="form-control me-3 d-inline-block" v-model="keyword" type="text"
                                placeholder="请输入关键字搜索" style="
                    width: 105em;
                    background: rgb(248, 248, 251);
                    border-radius: 0.8rem;
                    border: none;
                    outline: none;
                  " />
                            <!-- <div class="dropdown d-inline-block">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle border-0 bg-transparent"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{sort==="hot"?"热门":"最新"}}
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" @click="sort='hot'">热门</a>
                                    <a class="dropdown-item" @click="sort='new'">最新</a>
                                </div>
                            </div> -->
                            <!-- <div class="dropdown d-inline-block ms-2">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle border-0 bg-transparent"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{{all:"全部",installed:"已添加",notInstalled:"未添加"}[display]}}
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" @click="display='all'">全部</a>
                                    <a class="dropdown-item" @click="display='notInstalled'">未添加</a>
                                    <a class="dropdown-item" @click="display='installed'">已添加</a>
                                </div>
                            </div> -->
                        </div>
                    </div>
                </div>

                <div style="padding: 0 20px" class="row" v-cloak>
                    <div v-for="role in filteredRoles" class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-2">
                        <div class="white-bg shadow-sme mt-3 rounded p-3 card-container" @mouseover="hover=role.roleId"
                            @mouseout="hover=0">
                            <div class="d-flex align-items-center justify-content-between role position-relative">
                                <div class="d-flex align-items-center">
                                    <img :src="role.avatar" class="avatare me-2"
                                        @click="installAndSwitch(role.roleId)" />
                                    <div>
                                        <div style="
                          font-weight: 800;
                          letter-spacing: 0.025em;
                          font-size: 17px;
                        ">
                                            {{ role.name }}
                                        </div>
                                    </div>
                                </div>
                                <div v-if="installed(role.roleId)" class="installed" title="点击卸载"
                                    @click="uninstall(role.roleId)">
                                    &#xe9dd;
                                </div>
                                <div v-else class="install" title="点击安装" v-show="isMobile || hover == role.roleId"
                                    @click="install(role.roleId)">
                                    &#xe9dc;
                                </div>
                            </div>
                            <div class="mt-3 d-flex justify-content-between align-items-center">
                                <div class="text-secondary-sm">{{ role.desc }}</div>
                            </div>
                            <span class="text-secondary-sm">
                                <div style="padding: 10px 0" class="text-secondary-sm icon"
                                    v-if="role.installed === null">
                                    &#xe9e3; {{ role.installed }}
                                </div>
                                <div style="padding: 10px 0" class="text-secondary-sm" v-else>
                                    🔥{{ role.installed }}人使用过
                                </div>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- vue -->
    <script type="text/javascript" src="/app/ai/js/vue.global.js"></script>

    <script>
        const App = {
            data() {
                return {
                    roles: [],
                    installedRoles: [],
                    categories: [],
                    hover: 0,
                    category: "",
                    keyword: "",
                    display: "all", // all installed notInstalled
                    sort: "hot", // hot new
                    isMobile: false,
                };
            },
            computed: {
                filteredRoles() {
                    return this.roles
                        .filter((item) => {
                            return (
                                item.status === 0 &&
                                (this.display === "all" ||
                                    (this.display === "installed" &&
                                        this.installed(item.roleId)) ||
                                    (this.display === "notInstalled" &&
                                        !this.installed(item.roleId))) &&
                                (!this.category ||
                                    (item.category && item.category.includes(this.category))) &&
                                (!this.keyword ||
                                    item.name.includes(this.keyword) ||
                                    (item.desc && item.desc.includes(this.keyword)))
                            );
                        })
                        .sort((a, b) => {
                            return this.sort === "hot"
                                ? (b.installed || 0) - (a.installed || 0)
                                : b.roleId - a.roleId;
                        });
                },
            },
            mounted() {
                this.loadRoles();
                this.loadInstalledRoles();
                this.loadCategories();
                this.checkMobile();
            },
            methods: {
                loadRoles() {
                    $.ajax({
                        url: "/app/ai/roles?type=all",
                        success: (res) => {
                            if (res.code) {
                                return alert(res.msg);
                            }
                            this.roles = res.data;
                        },
                    });
                },
                loadCategories() {
                    $.ajax({
                        url: "/app/ai/setting/categories",
                        success: (res) => {
                            if (res.code) {
                                return alert(res.msg);
                            }
                            for (let category of res.data) {
                                this.categories.push(category.value);
                            }
                        },
                    });
                },
                loadInstalledRoles() {
                    const data = JSON.parse(localStorage.getItem("ai.data") || "{}");
                    this.installedRoles = data.roleList || [];
                },
                install(roleId) {
                    const role = this.roles.find((role) => role.roleId === roleId);
                    if (role) {
                        window.parent.ai.saveRole(role);
                        this.loadInstalledRoles();
                        $.ajax({
                            url: "/app/ai/role/installed",
                            data: { roleId },
                            type: "post",
                        });
                    }
                },
                installAndSwitch(roleId) {
                    this.install(roleId);
                    window.parent.ai.switchRoleId(roleId);
                    window.parent.ai.switchModule("chat");
                },
                uninstall(roleId) {
                    window.parent.ai.deleteRole(roleId);
                    this.loadInstalledRoles();
                },
                installed(roleId) {
                    return this.installedRoles.find(
                        (role) => !role.deleted && role.roleId === roleId
                    );
                },
                checkMobile() {
                    this.isMobile = window.innerWidth <= 768; // 假设小于768px的宽度为移动端
                },
            },
        };
        Vue.createApp(App).mount("#app");

        $(document).click(function () {
            try {
                window.parent.ai.hideAll();
            } catch (e) { }
        });

        try {
            $(document.body).attr("data-bs-theme", window.parent.ai.theme);
            parent.ai.showIframe();
        } catch (e) { }
    </script>

    <style>
        [data-bs-theme="light"] .btn {
            --bs-btn-hover-bg: var(--ai-white-bg);
            --bs-btn-active-bg: var(--bs-btn-hover-bg);
            --bs-btn-active-color: var(--bs-primary);
        }

        [data-bs-theme="dark"] .btn {
            --bs-btn-hover-bg: var(--ai-white-bg);
            --bs-btn-active-bg: var(--ai-white-bg);
            --bs-btn-active-color: var(--bs-body-color);
        }

        .container-fluid {
            max-width: 1660px;
        }

        .install,
        .installed {
            color: rgb(var(--bs-secondary-rgb));
            font-size: 1.6rem;
            border-radius: 0.5rem;
            width: 2rem;
            height: 2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: iconfont;
        }

        .install:hover {
            color: var(--bs-primary);
        }

        .installed {
            color: var(--bs-primary) !important;
        }

        .btn-sm {
            font-size: 1rem !important;
        }

        .selected {
            color: var(--bs-primary);
            border-color: var(--bs-primary) !important;
        }

        [data-bs-theme="dark"] .selected {
            color: var(--bs-body-color);
            border-color: var(--bs-primary) !important;
        }

        a {
            cursor: pointer;
        }

        .btn-outline-secondary {
            --bs-btn-color: var(--bs-btn-color);
            --bs-btn-hover-bg: var(--bs-btn-hover-bg);
            --bs-btn-hover-color: var(--bs-btn-hover-color);
        }

        body {
            padding-left: env(safe-area-inset-left);
            padding-right: env(safe-area-inset-right);
        }
    </style>
    <script>
        const textContainer = document.querySelector(".header-2_text");
        const texts = [
            "我们正处于AI 的iPhone时刻！",
            "人工智能不是未来，而是现在。",
            "你知道嘛，如果文生图没有出图，可能是你文字中没有“图片”等关键字。",
        ];
        let currentIndex = 0;

        function playAnimation() {
            typeText(texts[currentIndex], () => {
                setTimeout(() => {
                    deleteText(() => {
                        currentIndex = (currentIndex + 1) % texts.length;
                        setTimeout(playAnimation, 1000);
                    });
                }, 2000);
            });
        }

        function typeText(text, callback) {
            let currentText = "";
            text.split("").forEach((char, i) => {
                setTimeout(() => {
                    currentText += char;
                    textContainer.textContent = currentText;
                    if (i === text.length - 1) callback();
                }, i * 100);
            });
        }

        function deleteText(callback) {
            const text = textContainer.textContent;
            text.split("").forEach((_, i) => {
                setTimeout(() => {
                    textContainer.textContent = text.slice(0, text.length - i - 1);
                    if (i === text.length - 1) callback();
                }, i * 50); // 让删除速度快一点
            });
        }

        playAnimation();
    </script>
</body>

</html>