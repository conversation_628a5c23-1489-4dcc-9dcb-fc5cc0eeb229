<?=plugin\user\api\Template::header('用户登录')?>

<?=plugin\user\api\Template::nav()?>

<div class="container">
    <div class="row d-flex align-items-center justify-content-center">

        <div style="width:360px;" class="my-4">

            <form method="post">
                <h3 class="mb-3">登录</h3>
                <div class="form-group">
                    <input type="text" name="username" class="form-control" placeholder="用户名或Email" required>
                </div>
                <div class="form-group">
                    <input type="password" name="password" class="form-control" placeholder="密码" required>
                </div>
                <div class="form-group d-flex justify-content-between">
                    <input type="text" name="image_code" class="form-control w-50" autocomplete="off" placeholder="验证码" required>
                    <img class="rounded" src="/app/user/captcha/image/login"/>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block w-100">登录</button>
                </div>
                <div class="form-group d-flex justify-content-between">
                    <?php if($setting['register_enable']??true){ ?>
                    <!-- <a class="text-decoration-none" id="registerLink" href="/app/user/register">没有账号？点这里注册</a> -->
                    <?php } ?>
                    <a class="text-decoration-none" href="/app/user/password/reset">找回密码</a>
                </div>

            </form>
            <!-- <div>
                <div class="form-group d-flex">
                    <div class="text-decoration-none" style="text-align:center;width: 100%;color: #c5c5c5">-------其他方式登录------</div>
                </div>
                <div class="form-group d-flex">
                    <a style="width: 100%;text-align: center" href="/app/user/scan">
                        <img width="40px" src="data:image/svg+xml;charset=utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='24' height='24' viewBox='0 0 24 24'>  <defs>    <path id='0f20791c-6774-4e52-920f-6b6d8404b4dc-a' d='M6.724 0h10.552c2.338 0 3.186.243 4.04.7A4.766 4.766 0 0 1 23.3 2.684c.458.855.701 1.703.701 4.04v10.553c0 2.338-.243 3.186-.7 4.04a4.766 4.766 0 0 1-1.983 1.983c-.855.458-1.703.701-4.04.701H6.723c-2.338 0-3.186-.243-4.04-.7A4.766 4.766 0 0 1 .7 21.316c-.457-.854-.7-1.702-.7-4.039V6.723c0-2.338.243-3.186.7-4.04A4.766 4.766 0 0 1 2.684.7C3.538.243 4.386 0 6.723 0z'/>    <linearGradient id='0f20791c-6774-4e52-920f-6b6d8404b4dc-b' x1='50%25' x2='50%25' y1='0%25' y2='100%25'>      <stop offset='0%25' stop-color='%2302E36F'/>      <stop offset='100%25' stop-color='%2305CD65'/>      <stop offset='100%25' stop-color='%2307C160'/>    </linearGradient>  </defs>  <g fill='none' fill-rule='evenodd'>    <mask id='0f20791c-6774-4e52-920f-6b6d8404b4dc-c' fill='%23fff'>      <use xlink:href='%230f20791c-6774-4e52-920f-6b6d8404b4dc-a'/>    </mask>    <path fill='url(%230f20791c-6774-4e52-920f-6b6d8404b4dc-b)' d='M0 0h24v24H0z' mask='url(%230f20791c-6774-4e52-920f-6b6d8404b4dc-c)'/>    <path fill='%23FFF' d='M19.095 17.63c1.141-.826 1.87-2.05 1.87-3.408 0-2.49-2.423-4.51-5.411-4.51-2.989 0-5.411 2.02-5.411 4.51 0 2.49 2.422 4.51 5.41 4.51.618 0 1.214-.089 1.767-.248a.543.543 0 0 1 .447.06l1.184.683c.033.02.065.034.104.034.1 0 .18-.08.18-.18 0-.045-.017-.09-.028-.132l-.244-.91a.36.36 0 0 1 .132-.409M13.75 13.5a.721.721 0 1 1 0-1.442.721.721 0 0 1 0 1.443M9.493 4.734c3.24 0 5.925 1.977 6.414 4.562a7.206 7.206 0 0 0-.353-.01c-3.27 0-5.922 2.21-5.922 4.936 0 .46.077.904.218 1.326a7.687 7.687 0 0 1-2.476-.288.651.651 0 0 0-.536.071l-1.421.82a.245.245 0 0 1-.125.041.216.216 0 0 1-.217-.216c0-.054.021-.107.035-.158l.292-1.092a.433.433 0 0 0-.159-.49C3.876 13.243 3 11.775 3 10.145c0-2.989 2.907-5.412 6.493-5.412zm7.865 7.323a.721.721 0 1 1 0 1.443.721.721 0 0 1 0-1.443zM7.328 7.548a.866.866 0 1 0 0 1.732.866.866 0 0 0 0-1.732zm4.33 0a.866.866 0 1 0 0 1.731.866.866 0 0 0 0-1.73z' mask='url(%230f20791c-6774-4e52-920f-6b6d8404b4dc-c)'/>  </g></svg>">
                    </a>
                </div>
            </div> -->

        </div>

    </div>

</div>
<style>
    html, body {
        height: 90%;
    }
    .container, .row {
        height: 100%;
    }
</style>

<script>
    $('form img').on('click', function (e) {
        e.stopPropagation();
        e.preventDefault();
        $(this).attr('src', '/app/user/captcha/image/login?r='+ Math.random());
        $('input[name="image_code"]').val('');
    });

    $('input').keyup(function () {
        $(this).removeClass('is-invalid');
    });


    $('form').submit(function(event) {
        // alert(111)
        // return false;
        event.preventDefault();
        $.ajax({
            url: "/app/user/login",
            type: "POST",
            dataType: 'json',
            data: $(this).serialize(),
            success: function (e) {
                if (e.code !== 0) {
                    let field = e.data ? e.data.field : false;
                    field !== 'image_code' && $('form img').trigger('click');
                    field && $('input[name="'+field+'"]').addClass('is-invalid').focus();
                    return webman.error(e.msg);
                }
                webman.success('登录成功', function () {
                    let url = new URL(window.location.href);
                    let redirect = url.searchParams.get('redirect');
                    console.log("url",url)
                    console.log("redirect",redirect)
                    location.href = redirect && redirect.startsWith('/') && !redirect.startsWith('//') ? redirect : '/app/user';
                });
            }
        });
    });

    $("#registerLink").attr('href', '/app/user/register' + location.search);

</script>

<?=plugin\user\api\Template::footer()?>
