<?php

use plugin\ai\app\event\listener\ModelRequest;

return [
    // 当渲染用户端导航菜单时
    'user.nav.render' => [
        function(stdClass $object) {
            $object->navs[] = [
                'name' => 'AI',
                'items' => [
                    ['name' => 'AI', 'url' => '/app/ai'],
                ]
            ];
        }
    ],
    // 当发起模型请求前
    'ai.chat.completions.request' => [
        100 => [ModelRequest::class, 'gptMask'],
        101 => [ModelRequest::class, 'network'],
    ],
    'ai.image.generations.response' => [
        100 => [ModelRequest::class, 'dalleCdnReplace']
    ],
    'ai.task.notify' => [
        100 => [ModelRequest::class, 'midjourneyNotifyCdnReplace']
    ],
    'ai.task.status.response' => [
        100 => [ModelRequest::class, 'midjourneyStatusCdnReplace']
    ],
];
