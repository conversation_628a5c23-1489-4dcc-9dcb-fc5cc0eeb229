<?php

namespace plugin\ai\app\admin\controller;

use plugin\ai\app\model\AiApikey;
use support\Request;
use support\Response;
use plugin\ai\app\model\AiApikey as AiApikeyModel;
use plugin\admin\app\controller\Crud;
use support\exception\BusinessException;
use Throwable;

/**
 * APIKEY设置 
 */
class AiApikeyController extends Crud
{
    
    /**
     * @var AiApikey
     */
    protected $model = null;

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->model = new AiApikey;
    }
    
    /**
     * 浏览
     * @return Response
     */
    public function index(): Response
    {
        return view('ai-apikey/index');
    }

    /**
     * 插入
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function insert(Request $request): Response
    {
        if ($request->method() === 'POST') {
            if ($request->method() === 'POST') {
                if (AiApikeyModel::where('apikey', $request->post('apikey'))->first()) {
                    return $this->json(1, 'apikey已经存在，不能重复添加');
                }
                return parent::insert($request);
            }
            return parent::insert($request);
        }
        return view('ai-apikey/insert');
    }

    /**
     * 更新
     * @param Request $request
     * @return Response
     * @throws BusinessException
    */
    public function update(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return parent::update($request);
        }
        return view('ai-apikey/update');
    }

    /**
     * 批量插入
     * @param Request $request
     * @return Response
     * @throws Throwable
     */
    public function batchInsert(Request $request): Response
    {
        if ($request->method() !== 'POST') {
            return raw_view('ai-apikey/batch-insert');
        }
        $apiKeys = $request->post('apikeys', []);
        foreach ($apiKeys as $key) {
            if (!AiApikeyModel::where('apikey', $key)->first()) {
                $apikey = new AiApikey();
                $apikey->apikey = $key;
                $apikey->api = $request->post('api', '');
                $apikey->gpt4 = $request->post('gpt4') ? 1 : 0;
                $apikey->gpt3 = $request->post('gpt3') ? 1 : 0;
                $apikey->dalle = $request->post('dalle') ? 1 : 0;
                $apikey->tts = $request->post('tts') ? 1 : 0;
                $apikey->embedding = $request->post('embedding') ? 1 : 0;
                $apikey->save();
            }
        }
        return $this->json(0);
    }

}
