<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录设置</title>
    <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
    <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
</head>
<body class="pear-container">


<div class="layui-card">
    <div class="layui-card-body">

        <form class="layui-form" action="" lay-filter="setting-form">

            <table class="layui-table" lay-size="lg" lay-skin="nob" style="max-width:450px;">
                <thead>
                <tr>
                    <th>字段</th>
                    <th>是否显示</th>
                    <th>开启验证</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>昵称</td>
                    <td>
                        <input type="checkbox" name="nickname_enable" lay-skin="switch" >
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>邮箱</td>
                    <td>
                        <input type="checkbox" name="email_enable" lay-skin="switch">
                    </td>
                    <td>
                        <?php if($support_email){ ?>
                        <input type="checkbox" name="email_verify" lay-skin="switch">
                        <?php }else{ ?>
                        <a href="https://www.workerman.net/app/view/email" target="_blank">需安装云邮件</a>
                        <?php } ?>
                    </td>
                </tr>
                <tr>
                    <td>手机</td>
                    <td>
                        <input type="checkbox" name="mobile_enable" lay-skin="switch">
                    </td>
                    <td>
                        <?php if($support_sms){ ?>
                        <input type="checkbox" name="mobile_verify" lay-skin="switch">
                        <?php }else{ ?>
                        <a href="https://www.workerman.net/app/view/sms" target="_blank">需安装云短信</a>
                        <?php } ?>
                    </td>
                </tr>
                <tr>
                    <td>图形验证码</td>
                    <td>
                        <input type="checkbox" name="captcha_enable" lay-skin="switch">
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>开启注册</td>
                    <td>
                        <input type="checkbox" name="register_enable" lay-skin="switch" >
                    </td>
                    <td></td>
                </tr>
                </tbody>
            </table>
            <div class="layui-form-item" style="margin-top: 32px;">
                <div class="layui-input-block">
                    <button type="reset" class="pear-btn">重置</button>
                    <button class="pear-btn pear-btn-primary" lay-submit="" lay-filter="saveSetting">保存</button>
                </div>
            </div>
        </form>

    </div>
</div>


<script src="/app/admin/component/layui/layui.js"></script>
<script src="/app/admin/component/pear/pear.js"></script>
<script>
    layui.use(["form", "popup"], function(){
        let form = layui.form;
        let $ = layui.$;
        let popup = layui.popup;

        $.ajax({
            url: '/app/user/admin/register/getSetting',
            success: function (res) {
                console.log(res.data);
                form.val("setting-form", res.data);
            }
        });
        // 提交表单
        form.on('submit(saveSetting)', function(data){
            console.log(data.field);
            $.ajax({
                url: '/app/user/admin/register/saveSetting',
                data: data.field,
                type: 'POST',
                success: function (res) {
                    if (res.code) {
                        return popup.failure(res.msg);
                    }
                    return popup.success("操作成功");
                }
            });
            return false;
        });
    });
</script>

</body>
</html>

