<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>权限管理</title>
    <link rel="stylesheet" href="../../component/pear/css/pear.css" />
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="">
        	<div class="layui-form-item">
        		<div class="layui-form-item layui-inline">
        			<label class="layui-form-label">用户名</label>
        			<div class="layui-input-inline">
        				<input type="text" name="realName" placeholder="" class="layui-input">
        			</div>
        		</div>
        		<div class="layui-form-item layui-inline">
        			<label class="layui-form-label">性别</label>
        			<div class="layui-input-inline">
        				<input type="text" name="realName" placeholder="" class="layui-input">
        			</div>
        		</div>
        		<div class="layui-form-item layui-inline">
        			<label class="layui-form-label">邮箱</label>
        			<div class="layui-input-inline">
        				<input type="text" name="realName" placeholder="" class="layui-input">
        			</div>
        		</div>
        		<div class="layui-form-item layui-inline">
        			<button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="user-query">
        				<i class="layui-icon layui-icon-search"></i>
        				查询
        			</button>
        			<button type="reset" class="pear-btn pear-btn-md">
        				<i class="layui-icon layui-icon-refresh"></i>
        				重置
        			</button>
        		</div>
        	</div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="power-table" lay-filter="power-table"></table>
    </div>
</div>

<script type="text/html" id="power-toolbar">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        新增
    </button>
    <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove">
        <i class="layui-icon layui-icon-delete"></i>
        删除
    </button>
	<button class="pear-btn pear-btn-success pear-btn-md" lay-event="expandAll">
	    <i class="layui-icon layui-icon-spread-left"></i>
	    展开
	</button>
	<button class="pear-btn pear-btn-success pear-btn-md" lay-event="foldAll">
	    <i class="layui-icon layui-icon-shrink-right"></i>
	    折叠
	</button>
</script>

<script type="text/html" id="power-bar">
    <button class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i></button>
    <button class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i></button>
</script>

<script type="text/html" id="power-type">
    {{#if (d.powerType == '0') { }}
    <span>目录</span>
    {{# }else if(d.powerType == '1'){ }}
    <span>菜单</span>
    {{# }else if(d.powerType == '2'){ }}
    <span>按钮</span>
    {{# } }}
</script>

<script type="text/html" id="power-enable">
    <input type="checkbox" name="enable" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="user-enable"  {{ d.enable== true ? 'checked' : '' }} />
</script>

<script type="text/html" id="icon">
    <i class="layui-icon {{d.icon}}"></i>
</script>

<script src="../../component/layui/layui.js"></script>
<script src="../../component/pear/pear.js"></script>
<script>
    layui.use(['table','form','jquery','treetable'],function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let treetable = layui.treetable;

        let MODULE_PATH = "operate/";

        window.render = function(){
            treetable.render({
                treeColIndex: 1,
                treeIdName: 'powerId',
                treePidName: 'parentId',
                skin:'line',
                treeDefaultClose: true,
                toolbar:'#power-toolbar',
                elem: '#power-table',
                url: '../../demos/data/power.json',
                page: false,
                cols: [
                    [
                    {type: 'checkbox'},
                    {field: 'powerName', minWidth: 200, title: '权限名称'},
                    {field: 'icon', title: '图标',templet:'#icon'},
                    {field: 'powerType', title: '权限类型',templet:'#power-type'},
                    {field: 'enable', title: '是否可用',templet:'#power-enable'},
                    {field: 'sort', title: '排序'},
                    {title: '操作',templet: '#power-bar', width: 150, align: 'center'}
                    ]
                ]
            });
        }

        render();

        table.on('tool(power-table)',function(obj){
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'edit') {
                window.edit(obj);
            }
        })


        table.on('toolbar(power-table)', function(obj){
            if(obj.event === 'add'){
                window.add();
            } else if(obj.event === 'refresh'){
                window.refresh();
            } else if(obj.event === 'batchRemove'){
                window.batchRemove(obj);
            }  else if(obj.event === 'expandAll'){
				 treetable.expandAll("#power-table");
			} else if(obj.event === 'foldAll'){
				 treetable.foldAll("#power-table");
			}
        });

        window.add = function(){
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['450px', '500px'],
                content: MODULE_PATH + 'add.html'
            });
        }

        window.edit = function(obj){
            layer.open({
                type: 2,
                title: '修改',
                shade: 0.1,
                area: ['450px', '500px'],
                content: MODULE_PATH + 'edit.html'
            });
        }
        window.remove = function(obj){
            layer.confirm('确定要删除该权限', {icon: 3, title:'提示'}, function(index){
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH+"remove/"+obj.data['powerId'],
                    dataType:'json',
                    type:'delete',
                    success:function(result){
                        layer.close(loading);
                        if(result.success){
                            layer.msg(result.msg,{icon:1,time:1000},function(){
                                obj.del();
                            });
                        }else{
                            layer.msg(result.msg,{icon:2,time:1000});
                        }
                    }
                })
            });
        }
		
		window.batchRemove = function(obj) {
			let data = table.checkStatus(obj.config.id).data;
			if (data.length === 0) {
				layer.msg("未选中数据", {
					icon: 3,
					time: 1000
				});
				return false;
			}
			let ids = "";
			for (let i = 0; i < data.length; i++) {
				ids += data[i].powerId + ",";
			}
			ids = ids.substr(0, ids.length - 1);
			layer.confirm('确定要删除这些权限', {
				icon: 3,
				title: '提示'
			}, function(index) {
				layer.close(index);
				let loading = layer.load();
				$.ajax({
					url: MODULE_PATH + "batchRemove/" + ids,
					dataType: 'json',
					type: 'delete',
					success: function(result) {
						layer.close(loading);
						if (result.success) {
							layer.msg(result.msg, {
								icon: 1,
								time: 1000
							}, function() {
								table.reload('user-table');
							});
						} else {
							layer.msg(result.msg, {
								icon: 2,
								time: 1000
							});
						}
					}
				})
			});
		}
    })
</script>
</body>
</html>