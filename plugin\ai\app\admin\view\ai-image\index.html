
<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="utf-8">
        <title>浏览页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body class="pear-container">
    
        <!-- 顶部查询表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form top-search-from">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">主键</label>
                        <div class="layui-input-block">
                            <input type="number" name="id" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">任务id</label>
                        <div class="layui-input-block">
                            <input type="number" name="task_id" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">创建时间</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block" id="created_at">
                                <input type="text" autocomplete="off" name="created_at[]" id="created_at-date-start" class="layui-input inline-block" placeholder="开始时间">
                                -
                                <input type="text" autocomplete="off" name="created_at[]" id="created_at-date-end" class="layui-input inline-block" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">模型</label>
                        <div class="layui-input-block">
                            <input type="text" name="model" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">类型</label>
                        <div class="layui-input-block">
                            <input type="text" name="type" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户id</label>
                        <div class="layui-input-block">
                            <input type="number" name="user_id" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">session_id</label>
                        <div class="layui-input-block">
                            <input type="text" name="session_id" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">ip</label>
                        <div class="layui-input-block">
                            <input type="text" name="ip" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-inline">
                        <label class="layui-form-label"></label>
                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="table-query">
                            <i class="layui-icon layui-icon-search"></i>查询
                        </button>
                        <button type="reset" class="pear-btn pear-btn-md" lay-submit lay-filter="table-reset">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                    <div class="toggle-btn">
                        <a class="layui-hide">展开<i class="layui-icon layui-icon-down"></i></a>
                        <a class="layui-hide">收起<i class="layui-icon layui-icon-up"></i></a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="data-table" lay-filter="data-table"></table>
            </div>
        </div>

        <!-- 表格顶部工具栏 -->
        <script type="text/html" id="table-toolbar">
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add" permission="app.ai.admin.aiimage.insert">
                <i class="layui-icon layui-icon-add-1"></i>新增
            </button>
            <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove" permission="app.ai.admin.aiimage.delete">
                <i class="layui-icon layui-icon-delete"></i>删除
            </button>
        </script>

        <!-- 表格行工具栏 -->
        <script type="text/html" id="table-bar">
            <button class="pear-btn pear-btn-xs tool-btn" lay-event="edit" permission="app.ai.admin.aiimage.update">编辑</button>
            <button class="pear-btn pear-btn-xs tool-btn" lay-event="remove" permission="app.ai.admin.aiimage.delete">删除</button>
        </script>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        <script src="/app/admin/admin/js/common.js"></script>
        
        <script>

            // 相关常量
            const PRIMARY_KEY = "id";
            const SELECT_API = "/app/ai/admin/ai-image/select";
            const UPDATE_API = "/app/ai/admin/ai-image/update";
            const DELETE_API = "/app/ai/admin/ai-image/delete";
            const INSERT_URL = "/app/ai/admin/ai-image/insert";
            const UPDATE_URL = "/app/ai/admin/ai-image/update";
            
            // 字段 创建时间 created_at
            layui.use(["laydate"], function() {
                layui.laydate.render({
                    elem: "#created_at",
                    range: ["#created_at-date-start", "#created_at-date-end"],
                    type: "datetime",
                });
            })
            
            // 表格渲染
            layui.use(["table", "form", "common", "popup", "util"], function() {
                let table = layui.table;
                let form = layui.form;
                let $ = layui.$;
                let common = layui.common;
                let util = layui.util;
                
				// 表头参数
				let cols = [
					{
						type: "checkbox",
						align: "center"
					},{
						title: "主键",align: "center",
						field: "id",
						sort: true,
                        width: 90,
					},{
						title: "任务id",align: "center",
						field: "task_id",
                        hide: true,
					},{
						title: "创建时间",align: "center",
						field: "created_at",
						sort: true,
					},{
						title: "更新时间",align: "center",
						field: "updated_at",
						hide: true,
					},{
						title: "模型",align: "center",
						field: "model",
                        width: 120,
					},{
						title: "类型",align: "center",
						field: "type",
                        width: 150,
					},{
						title: "提示词",
						field: "prompt",
					},{
						title: "缩略图",
						field: "small_url",
                        width: 150,
						templet: function (d) {
                            if (!d['small_url'] && d.data && d.data.failReason) {
                                return '<span style="color:red;">'+d.data.failReason+'</span>';
                            }
							return '<img src="'+d['small_url']+'" style="width:128px;" alt="" />'
						}
					},{
                        title: "广场显示",align: "center",
                        field: "gallery",
                        width: 100,
                        templet: function (d) {
                            let field = "gallery";
                            form.on("switch("+field+")", function (data) {
                                let load = layer.load();
                                let postData = {};
                                postData[field] = data.elem.checked ? 1 : 0;
                                postData[PRIMARY_KEY] = this.value;
                                $.post(UPDATE_API, postData, function (res) {
                                    layer.close(load);
                                    if (res.code) {
                                        return layui.popup.failure(res.msg, function () {
                                            data.elem.checked = !data.elem.checked;
                                            form.render();
                                        });
                                    }
                                    return layui.popup.success("操作成功");
                                })
                            });
                            let checked = d[field] === 1 ? "checked" : "";
                            return '<input type="checkbox" value="'+util.escape(d[PRIMARY_KEY])+'" lay-filter="'+util.escape(field)+'" lay-skin="switch" lay-text="'+util.escape('')+'" '+checked+'/>';
                        }
                    },{
						title: "图像数据",
						field: "image",
						hide: true,
                        align: 'left',
					},{
						title: "结果数据",
						field: "data",
						hide: true,
                        templet: function (d) {
                            return d.data ? JSON.stringify(d.data, null, 4) : '';
                        }
					},{
						title: "用户",align: "center",
						field: "user_id",
                        templet: function (d) {
                            return '<span>' + (d.user ? (util.escape(d.user.nickname) + '(' + d.user.id + ')') : '匿名' + (d.user_id? '(' + d.user_id + ')' : '')) + '</span>'
                        }
					},{
						title: "session_id",align: "center",
						field: "session_id",
                        hide: true,
					},{
						title: "ip",align: "center",
						field: "ip",
					},{
						title: "操作",
						toolbar: "#table-bar",
						align: "center",
						fixed: "right",
						width: 120,
					}
				];
				
				// 渲染表格
				table.render({
				    elem: "#data-table",
				    url: SELECT_API,
				    page: true,
				    cols: [cols],
				    skin: "line",
				    size: "lg",
				    toolbar: "#table-toolbar",
				    autoSort: false,
                    lineStyle: 'height: 200px;',
				    defaultToolbar: [{
				        title: "刷新",
				        layEvent: "refresh",
				        icon: "layui-icon-refresh",
				    }, "filter", "print", "exports"],
				    done: function () {
				        layer.photos({photos: 'div[lay-id="data-table"]', anim: 5});
				    }
				});
				
				
                // 编辑或删除行事件
                table.on("tool(data-table)", function(obj) {
                    if (obj.event === "remove") {
                        remove(obj);
                    } else if (obj.event === "edit") {
                        edit(obj);
                    }
                });

                // 表格顶部工具栏事件
                table.on("toolbar(data-table)", function(obj) {
                    if (obj.event === "add") {
                        add();
                    } else if (obj.event === "refresh") {
                        refreshTable();
                    } else if (obj.event === "batchRemove") {
                        batchRemove(obj);
                    }
                });

                // 表格顶部搜索事件
                form.on("submit(table-query)", function(data) {
                    table.reload("data-table", {
                        page: {
                            curr: 1
                        },
                        where: data.field
                    })
                    return false;
                });
                
                // 表格顶部搜索重置事件
                form.on("submit(table-reset)", function(data) {
                    table.reload("data-table", {
                        where: []
                    })
                });
                
                // 字段允许为空
                form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });

                // 表格排序事件
                table.on("sort(data-table)", function(obj){
                    table.reload("data-table", {
                        initSort: obj,
                        scrollPos: "fixed",
                        where: {
                            field: obj.field,
                            order: obj.type
                        }
                    });
                });

                // 表格新增数据
                let add = function() {
                    layer.open({
                        type: 2,
                        title: "新增",
                        shade: 0.1,
                        maxmin: true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: INSERT_URL
                    });
                }

                // 表格编辑数据
                let edit = function(obj) {
                    let value = obj.data[PRIMARY_KEY];
                    layer.open({
                        type: 2,
                        title: "修改",
                        shade: 0.1,
                        maxmin: true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: UPDATE_URL + "?" + PRIMARY_KEY + "=" + value
                    });
                }

                // 删除一行
                let remove = function(obj) {
                    return doRemove(obj.data[PRIMARY_KEY]);
                }

                // 删除多行
                let batchRemove = function(obj) {
                    let checkIds = common.checkField(obj, PRIMARY_KEY);
                    if (checkIds === "") {
                        layui.popup.warning("未选中数据");
                        return false;
                    }
                    doRemove(checkIds.split(","));
                }

                // 执行删除
                let doRemove = function (ids) {
                    let data = {};
                    data[PRIMARY_KEY] = ids;
                    layer.confirm("确定删除?", {
                        icon: 3,
                        title: "提示"
                    }, function(index) {
                        layer.close(index);
                        let loading = layer.load();
                        $.ajax({
                            url: DELETE_API,
                            data: data,
                            dataType: "json",
                            type: "post",
                            success: function(res) {
                                layer.close(loading);
                                if (res.code) {
                                    return layui.popup.failure(res.msg);
                                }
                                return layui.popup.success("操作成功", refreshTable);
                            }
                        })
                    });
                }

                // 刷新表格数据
                window.refreshTable = function() {
                    table.reloadData("data-table", {
                        scrollPos: "fixed",
                        done: function (res, curr) {
                            if (curr > 1 && res.data && !res.data.length) {
                                curr = curr - 1;
                                table.reloadData("data-table", {
                                    page: {
                                        curr: curr
                                    },
                                })
                            }
                        }
                    });
                }
            })

        </script>

        <style>
            .layui-table-view .layui-table[lay-size=lg] .layui-table-cell {
                line-height: 20px;
            }
            .layui-table-view .layui-table td {
                padding-top: 8px;
                padding-bottom: 8px;
            }
        </style>
    </body>
</html>
