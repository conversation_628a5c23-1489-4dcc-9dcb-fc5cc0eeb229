<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>导入</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body>
    <style>
        .layui-input-block {
            margin-left: 60px;
        }
    </style>

        <form class="layui-form" action="">

            <div class="mainBox">
                <div class="main-container mr-5" style="padding-top:30px;">

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <input type="file" multiple name="file" accept=".md,.txt,.zip"/>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block layui-font-orange">
                            只支持txt、md为后缀的文件，每个文本文件不大于9k，<br>文件编码utf-8。可以上传zip压缩包。
                        </div>
                    </div>

                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit="" lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
        </form>

        <script src="/app/admin/component/layui/layui.js"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        
        <script>

            // 相关接口
            const UPLOAD_API = "/app/ai/admin/ai-dataset/import";
            // 获取query中的dataset
            const datasetID = new URL(window.location.href).searchParams.get("dataset_id");

            //提交事件
            layui.use(["form", "popup"], function () {
                layui.form.on("submit(save)", function (data) {
                    const formData = new FormData();
                    const files = layui.$("input[name='file']")[0].files;
                    for(let i = 0; i < files.length; i++) {
                        formData.append('file[]', files[i]);
                    }
                    formData.append('dataset_id', datasetID);
                    layui.$.ajax({
                        url: UPLOAD_API,
                        type: "POST",
                        dateType: "json",
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>
</html>
