<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>封装弹窗</title>
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
	</head>
	<body class="pear-container">
		<div class="layui-row layui-col-space10">
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">开发环境</div>
					<div class="layui-card-body">
						Popup 基于 Layer 的 二次封装, 提供简洁高效的 API 调用
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								&lt;link rel="stylesheet" href="component/pear/css/pear.css" /&gt;
								 并
								&lt;script src="component/layui/layui.js"&gt;&lt;/script&gt;
								 并
								&lt;script src="component/pear/pear.js"&gt;&lt;/script&gt;
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">提示消息</div>
					<div class="layui-card-body">
						<button class="success pear-btn pear-btn-primary">成功消息</button>
						<button class="failure pear-btn pear-btn-danger">失败消息</button>
						<button class="warming pear-btn pear-btn-warming">警告消息</button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(['popup'], function() {
								    var popup = layui.popup;
								  
								    popup.success("成功消息")
								    popup.failure("失败消息")
								    popup.warning("警告消息")
								})
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">回调函数</div>
					<div class="layui-card-body">
						<button class="success-callback pear-btn pear-btn-primary">成功消息</button>
						<button class="failure-callback pear-btn pear-btn-danger">失败消息</button>
						<button class="warming-callback pear-btn pear-btn-warming">警告消息</button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(['popup'], function() {
								    var popup = layui.popup;
								  
								    popup.success("成功消息",callback)
								    popup.failure("失败消息",callback)
								    popup.warning("警告消息",callback)
								})
							</pre>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
	<script src="../../component/layui/layui.js"></script>
	<script src="../../component/pear/pear.js"></script>
	<script>
		layui.use(['popup', 'jquery', 'code'], function() {
			var popup = layui.popup;
			var $ = layui.jquery;

			layui.code();

			$(".success").click(function() {
				popup.success("成功消息")
			})
			$(".failure").click(function() {
				popup.failure("失败消息")
			})
			$(".warming").click(function() {
				popup.warning("警告消息")
			})
			$(".success-callback").click(function() {
				popup.success("成功消息", function() {
					layer.msg("回调函数")
				})
			})
			$(".failure-callback").click(function() {
				popup.failure("失败消息", function() {
					layer.msg("回调函数")
				})
			})
			$(".warming-callback").click(function() {
				popup.warning("警告消息", function() {
					layer.msg("回调函数")
				})
			})
		})
	</script>
</html>
