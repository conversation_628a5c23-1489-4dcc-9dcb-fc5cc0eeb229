<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\handler;

use Webman\Openai\Image;

class Dalle extends Gpt3
{
    /**
     * @var string 模型处理器名称
     */
    protected static $name = 'Dall.E';

    /**
     * @var string 模型类型
     */
    protected static $type = 'dalle';

    /**
     * @var string[] 支持的模型
     */
    public static $models = [
        'dall.e',
        'dall-e-3',
        'dall-e-2',
    ];

    /**
     * @var string[] 自定义配置
     */
    public static $defaultSettings = [
        'api' => [
            'name' => 'API',
            'type' => 'text',
            'value' => 'https://api.ailard.com'
        ],
        'regFreeCount' => [
            'name' => '注册赠送',
            'type' => 'number',
            'value' => 0,
        ],
        'dayFreeCount' => [
            'name' => '每日赠送',
            'type' => 'number',
            'value' => 0,
        ],
    ];

    /**
     * @var string 处理器
     */
    protected $driverClass = driver\Dalle::class;

    /**
     * Image
     * @param array $data
     * @param array $options
     * @return void
     */
    public function generations(array $data, array $options)
    {
        if ($data['model'] === 'dall.e') {
            $data['model'] = 'dall-e-3';
        }
        $settings = $this->getRealSettings($data['model']);
        $this->driver = new $this->driverClass($settings);
        $this->driver->generations($data, $options);
    }

    /**
     * 对话
     * @param $data
     * @param $options
     * @return void
     */
    public function completions($data, $options)
    {
        $this->driver = new $this->driverClass($this->getSettings());
        $this->driver->completions($data, $options);
    }

}