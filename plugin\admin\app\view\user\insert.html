<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="UTF-8">
        <title>新增页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body>

        <form class="layui-form" action="">

            <div class="mainBox">
                <div class="main-container mr-5">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">用户名</label>
                        <div class="layui-input-block">
                            <input type="text" name="username" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">昵称</label>
                        <div class="layui-input-block">
                            <input type="text" name="nickname" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">密码</label>
                        <div class="layui-input-block">
                            <input type="text" name="password" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">性别</label>
                        <div class="layui-input-block">
                            <div name="sex" id="sex" value="1" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">头像</label>
                        <div class="layui-input-block">
                            <img class="img-3" src=""/>
                            <input type="text" style="display:none" name="avatar" value="" />
                            <button type="button" class="pear-btn pear-btn-primary pear-btn-sm" id="avatar" permission="app.admin.upload.avatar">
                                <i class="layui-icon layui-icon-upload"></i>上传图片
                            </button>
                            <button type="button" class="pear-btn pear-btn-primary pear-btn-sm" id="attachment-choose-avatar" permission="app.admin.upload.attachment">
                                <i class="layui-icon layui-icon-align-left"></i>选择图片
                            </button>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">邮箱</label>
                        <div class="layui-input-block">
                            <input type="text" name="email" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">手机</label>
                        <div class="layui-input-block">
                            <input type="text" name="mobile" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">等级</label>
                        <div class="layui-input-block">
                            <input type="number" name="level" value="0" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">生日</label>
                        <div class="layui-input-block">
                            <input type="text" name="birthday" id="birthday" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">余额(元)</label>
                        <div class="layui-input-block">
                            <input type="number" name="money" value="0" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">积分</label>
                        <div class="layui-input-block">
                            <input type="number" name="score" value="0" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">登录时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="last_time" id="last_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">登录ip</label>
                        <div class="layui-input-block">
                            <input type="text" name="last_ip" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">注册时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="join_time" id="join_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">注册ip</label>
                        <div class="layui-input-block">
                            <input type="text" name="join_ip" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">禁用</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="status" lay-filter="status" lay-skin="switch" />
                            <input type="text" style="display:none" name="status" value="0" />
                        </div>
                    </div>
                    
                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                        lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
        </form>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        <script>

            // 相关接口
            const INSERT_API = "/app/admin/user/insert";
            
            // 字段 性别 sex
            layui.use(["jquery", "xmSelect"], function() {
                layui.$.ajax({
                    url: "/app/admin/dict/get/sex",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#sex").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#sex",
                            name: "sex",
                            initValue: initValue,
                            data: res.data,
                            value: "1",
                            model: {"icon":"hidden","label":{"type":"text"}},
                            clickClose: true,
                            radio: true,
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            // 字段 头像 avatar
            layui.use(["upload", "layer"], function() {
                let input = layui.$("#avatar").prev();
                input.prev().attr("src", input.val());
                layui.$("#attachment-choose-avatar").on("click", function() {
                    parent.layer.open({
                        type: 2,
                        title: "选择附件",
                        content: "/app/admin/upload/attachment?ext=jpg,jpeg,png,gif,bmp",
                        area: ["95%", "90%"],
                        success: function (layero, index) {
                            parent.layui.$("#layui-layer" + index).data("callback", function (data) {
                                input.val(data.url).prev().attr("src", data.url);
                            });
                        }
                    });
                });
                layui.upload.render({
                    elem: "#avatar",
                    url: "/app/admin/upload/avatar",
                    acceptMime: "image/gif,image/jpeg,image/jpg,image/png",
                    field: "__file__",
                    done: function (res) {
                        if (res.code > 0) return layui.layer.msg(res.msg);
                        this.item.prev().val(res.data.url).prev().attr("src", res.data.url);
                    }
                });
            });
            
            // 字段 生日 birthday
            layui.use(["laydate"], function() {
                layui.laydate.render({
                    elem: "#birthday",
                });
            })
            
            // 字段 登录时间 last_time
            layui.use(["laydate"], function() {
                layui.laydate.render({
                    elem: "#last_time",
                    type: "datetime",
                });
            })
            
            // 字段 注册时间 join_time
            layui.use(["laydate"], function() {
                layui.laydate.render({
                    elem: "#join_time",
                    type: "datetime",
                });
            })
            
            // 字段 禁用 status
            layui.use(["form"], function() {
                layui.$("#status").attr("checked", layui.$('input[name="status"]').val() != 0);
                layui.form.render();
                layui.form.on("switch(status)", function(data) {
                    layui.$('input[name="status"]').val(this.checked ? 1 : 0);
                });
            })
            
            //提交事件
            layui.use(["form", "popup"], function () {
                layui.form.on("submit(save)", function (data) {
                    layui.$.ajax({
                        url: INSERT_API,
                        type: "POST",
                        dateType: "json",
                        data: data.field,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>
</html>
