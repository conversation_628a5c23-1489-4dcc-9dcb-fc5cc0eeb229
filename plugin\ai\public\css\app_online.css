/* ＷＥＢＭＡＮ ＡＩ 主样式 */
html, body {
    font-size: 15px;
    background-color: var(--ai-light-bg);
    height: 100%;
}

*::-webkit-scrollbar {
    width: 0;
}

[data-bs-theme=dark] {
    --bs-form-control-bg: #343a40;
    --bs-body-color: #f3f4f5;
    --ai-dark-bg: #2b2d31;
    --ai-gray-bg: #313338;
    --ai-light-bg: #313338;
    --ai-message-bg: #3a3c41;
    --ai-body-bg: #495057;
    --bs-primary: #2f81f7;
    --bs-range-thunmb-bg: #f3f4f5;
    --ai-message-list-bg: var(--ai-gray-bg);
    --ai-hover-color: #bbb;
    --ai-selected-bg: #3a3c41;
    --ai-dark-border: 0;
    --bs-btn-color: #aab6c2;
    --ai-white-bg: #373f47;
    --ai-slider-runnable-track: var(--bs-form-control-bg);
    --bs-heading-color: var(--bs-body-color);
    --bs-card-title-color: var(--bs-body-color) !important;
    --bs-danger-rgb: 255, 90, 106;
    --bs-danger: #ff5a6a;
    --bs-secondary-rgb: 160, 167, 173;
    --bs-border-color: rgb(83, 86, 88, 0.75);
}

[data-bs-theme=light] {
    --ai-dark-bg: #fff;
    --ai-gray-bg: #fff;
    --ai-light-bg: #f2f3f5;
    --ai-message-list-bg: #f2f3f5;
    --ai-message-bg: #fff;
    --ai-body-bg: var(--bs-border-color);
    --bs-form-control-bg: #fff;
    --bs-range-thunmb-bg: var(--bs-primary);
    --ai-hover-color: #666;
    --ai-selected-bg: #f2f3f5;
    --ai-dark-border: 1px solid var(--bs-border-color);
    --ai-white-bg: #fff;
    --ai-slider-runnable-track: #dee2e6;
    --bs-heading-color: var(--bs-body-color);
}

.white-bg {
    background: var(--ai-white-bg);
}

.black-bg {
    background: var(--bs-body-bg);
}

.dark-bg {
    background: var(--ai-dark-bg);
}

.gray-bg {
    background: var(--ai-gray-bg);
}

.light-bg {
    background: var(--ai-light-bg);
}

.selected-bg {
    background: var(--ai-selected-bg);
}

.form-control-bg {
    background: var(--bs-form-control-bg);
}

[data-bs-theme=light] .form-control-shadow {
    box-shadow: 0 0 0 0.25rem #d4e1f6;
    transition: none;
}

[data-bs-theme=light] .form-control-shadow:focus {
    box-shadow: 0 0 0 0.25rem #a3c4f7;
    transition: none;
}

[data-bs-theme=dark] .form-control-shadow {
    box-shadow: 0 0 0 0.25rem #2c3b56;
    transition: none;
}

[data-bs-theme=dark] .form-control-shadow:focus {
    box-shadow: 0 0 0 0.25rem #25477c;
    transition: none;
}

.form-group {
    margin-bottom: .7rem;
}

.ai-container {
    background: var(--bs-body-bg);
}

.dark-border {
    border: var(--ai-dark-border);
}

.text-secondary-sm {
    color: var(--bs-secondary-color);
    font-size: 0.86rem;
}

[data-bs-theme=dark] .shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgb(0 0 0 / 8%) !important;
}

.message-bg {
    background: var(--ai-message-bg);
}

input[type=range]::-webkit-slider-runnable-track {
    background: var(--ai-slider-runnable-track);
}
input[type=range]::-webkit-slider-thumb {
    background: var(--bs-range-thunmb-bg);
}
input[type=range]::-moz-range-thumb {
    background: var(--bs-range-thunmb-bg);
}

input, textarea {
    outline: none;
    resize: none;
}

input, textarea, select {
    background: var(--bs-form-control-bg);
}

input[type=number] {
    -moz-appearance:textfield;
}

.container-fluid {
    padding: 0;
    font-size: 1rem;
}

a {
    text-decoration: none;
    cursor: pointer;
}

.modal-header {
    border-bottom: none;
}
.modal-footer {
    border-top: none;
    
}

/** 兼容bootstrap4 webman提示 **/
.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: var(--bs-body-color);
    text-shadow: 0 1px 0 var(--bs-body-bg);
    opacity: .5;
}

.modal-header .close {
    padding: 1rem 1rem;
    margin: -1rem -1rem -1rem auto;
}

button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

[data-bs-theme=dark] button.close {
    filter: var(--bs-btn-close-white-filter);
}

.cursor-not-allowed {
    cursor: not-allowed !important;
}
.mr-2 {
    margin-right: .5rem !important;
}
.mr-auto, .mx-auto {
    margin-right: auto !important;
}
/** end **/

.ai-menu-icon::before {
    font-family: iconfont;
    align-items: center;
    justify-content: center;
    font-size: 25px;
    cursor: pointer;
}

.icon-chat::before {
    content: "\e9f3";
}
.icon-chat-fill::before {
    content: "\e9f4";
}
.icon-painting::before {
    content: "\e603";
}
.icon-painting-fill::before {
    content: "\e601";
}
.icon-sd::before {
    content: "\e604";
}
.icon-market::before {
    content: "\e9f6";
}
.icon-market-fill::before {
    content: "\e9f5";
}
.icon-gallery::before {
    content: "\e863";
}
.icon-gallery-fill::before {
    content: "\e861";
}
.icon-vip::before {
    content: "\ea41";
}
.icon-vip-fill::before {
    content: "\ea40";
}
.icon-me::before {
    content: "\ea42";
}
.icon-me-fill::before {
    content: "\ea3f";
}

.header {
    height: 45px;
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    padding: 0 1rem;
    background: var(--ai-dark-bg);
    border-top-right-radius: var(--bs-border-radius) !important;
    box-shadow: 0 .125rem .25rem rgba(var(--bs-body-color-rgb), .075) !important;
    position: relative;
    z-index: 100;
}

[data-bs-theme=dark] .header {
    box-shadow: 0 1px 0 hsl(0 calc( 1 * 0%) 0.8% / 0.2), 0 1.5px 0 hsl(240 calc( 1 * 7.7%) 2.5% / 0.05), 0 2px 0 hsl(0 calc( 1 * 0%) 0.8% / 0.05) !important;
}

@font-face {
    font-family: 'iconfont';  /* Project id 4079339 */
    src: url('//at.alicdn.com/t/c/font_4079339_i4n00vxvxhk.woff2?t=1716511727295') format('woff2'),
    url('//at.alicdn.com/t/c/font_4079339_i4n00vxvxhk.woff?t=1716511727295') format('woff'),
    url('//at.alicdn.com/t/c/font_4079339_i4n00vxvxhk.ttf?t=1716511727295') format('truetype');
}

.icon {
    font-family: iconfont;
}

.icon-btn {
    font-family: iconfont;
    color: var(--bs-body-color);
    cursor: pointer;
}

.icon-btn:hover {
    color: var(--ai-hover-color)
}

.icon-btn-secondary {
    font-family: iconfont;
    color: var(--ai-hover-color);
    cursor: pointer;
}

.icon-btn-secondary:hover {
    color: var(--bs-body-color);
}

.iconfont {
    font-family: iconfont;
    cursor: pointer;
    border-radius: var(--bs-border-radius);
    padding: 0 0.4rem;
}

.icon-btn-secondary {
    font-family: iconfont;
    color: var(--bs-secondary-color);
}

.icon-btn-secondary:hover {
    color: var(--bs-body-color);
}

.empty::before {
    font-family: iconfont;
    content: "\e9e4";
    align-items: center;
    justify-content: center;
    display: flex;
    height: 100%;
    width: 100%;
    font-size: 120px;
    color: var(--bs-secondary-bg);
}

.slide-in {
    animation: slideInFromRight 0.1s ease-in-out;
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(0);
    }
}

.slide-out {
    animation: slideOutToRight 0.1s ease-in-out;
}

@keyframes slideOutToRight {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(100%);
    }
}


.slide-up {
    animation: slideUp 0.1s forwards;
}

@keyframes slideUp {
    0% {
        opacity: 0;
        transform: translateY(100%);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
}

.fade-out {
    animation: fadeOut 2s forwards;
}

.iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.small-window {
    max-width: 1140px;
    max-height: 895px;
    border-radius: var(--bs-border-radius);
    position: relative;
}

.left-bar {
    width: 150px;
    min-width: 150px;
    max-width: 150px;
    height: 100%;
    padding-top: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
    font-family: iconfont;
    border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color);
}
.centered-span{
    margin-left: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
}

.qr-code {
    position: absolute;
    top: 75%; /* 根据实际需求调整 */
    left: 8%;
    z-index: 10;
    background: white;
    border: 1px solid #ddd;
    padding: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.qr-code img {
    width: 200px; /* 根据实际需求调整 */
    height: 200px; /* 根据实际需求调整 */ 
}

.search-box {
    height: 50px;
}

.left-bar .icon-btn, .left-bar .ai-menu {
    font-size: 25px;
    color: var(--bs-secondary-color);
    cursor: pointer;
}

.left-bar [class*=" bi-"] {
    font-size: 23px;
}

.left-bar .ai-menu [class*="icon-"]::before {
    font-style: normal !important;
}

.left-bar>div:last-child {
    margin-top: auto !important;
}

.left-bar .icon-btn.selected {
    color:var(--bs-primary);
}

.left-bar .avatar {
    width: 40px;
    height: 40px;
}

.send-btn {
    position: absolute;
    right: .5rem;
    bottom: .5rem;
    margin: 0;
}

.footer-txt-box {
    position: absolute;
    left: .8rem;
    bottom: .5rem;
    margin: 0;
    font-size: 12px;
}

.footer-txt-box a {
    color: var(--bs-border-color) !important;
    text-decoration: none;
}

.add-btn {
    height: 1.95rem !important;
    width: 2.2rem !important;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bs-body-color);
}

.chat-tools-item {
    font-size: 1.38rem;
    cursor: pointer;
    color: var(--bs-secondary-color);
    display: inline-block;
    margin: .3rem .7rem;
    padding: 0 .2rem !important;
}

.chat-tools-item:hover {
    color: var(--bs-body-color);
}

[v-cloak] {
    display: none !important;
}

.params-box{
    position: absolute;
    bottom: 200px;
    background-color: var(--bs-body-bg);
    padding: 1rem;
    border-radius: var(--bs-border-radius);
    max-width: 100%;
    width: 450px;
}

.center-div {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.center-box {
    position: absolute;
    width: 400px;
    background-color: var(--bs-body-bg);
    border-radius: var(--bs-border-radius);
    top: 50%;
    left: 50%;
    margin-left: -200px;
    margin-top: -250px;
    padding: 2rem;
    max-width: 100%;
}

.more-box {
    position: absolute;
    bottom: 20px;
    left: 66px;
    background-color: var(--bs-body-bg);
    width: 200px;
}

.right-box {
    position: absolute;
    width: 375px;
    max-width:85%;
    right: 0;
    top: 0;
    bottom: 0
}

.send-method-box {
    position: absolute;
    bottom: 200px;
    right: 0;
    background-color: var(--bs-body-bg);
    padding: 0.6rem;
    border-radius: var(--bs-border-radius);
    cursor: pointer;
    max-width: 100%;
}

.top-right-options-box {
    position: absolute;
    top: 45px;
    right: 0;
    background-color: var(--bs-body-bg);
    width: 160px;
}

.dropdown-item {
    padding: .25rem 1rem .25rem 1rem;
}

.dropdown-item:active, .dropdown-item:hover {
    color: var(--bs-primary-text);
}

.dropdown-item.selected::before {
    content: "✓";
}

.chat-bar {
    height: 100%;
    width: 350px;
    flex-shrink: 0;
    border-right: 1px solid var(--bs-border-color);
}
.chat-list {
    height: calc(100% - 60px);
    overflow-y: scroll;
    overflow-x: hidden;
    padding-bottom: 1rem;
}
.chat-list .item {
    padding: 0.6rem 0.8rem;
    display: flex;
    justify-content: space-between;
    -webkit-user-select: none !important;
    user-select: none !important;
}

.chat-list .item:hover {
    background-color: var(--ai-selected-bg);
}

.chat-list .text-truncate {
    max-width: 150px;
}

.chat-box {
    height: 100%;
    width: calc(100% - 250px);
}

.page-box {
    border-end-end-radius: var(--bs-border-radius) !important;
    border-top-right-radius: var(--bs-border-radius) !important;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.chat-box .header{
    height: 50px;
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    padding: 0 1rem;
    margin-bottom: 1px;
    border-top-right-radius: var(--bs-border-radius) !important;
}

.chat-box .body {
    position: relative;
    background: var(--ai-message-list-bg);
}

.message-list {
    padding: 0 1rem;
}

.message-list li.d-flex:first-child {
    margin-top: 2rem !important;
}

.message-list>li:last-child {
    margin-bottom: 2.5rem;
}

.message-list .message {
    padding: .5rem .75rem;
    min-height: 2.5rem;
}

.chat-box .body .stop-btn {
    position: absolute;
    bottom: .25rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
}

.chat-box .footer {
    border-top: 1px solid var(--bs-border-color);
    overflow: hidden;
    position: relative;
}

.chat-box .footer .tools {
    user-select: none;
}

.chat-box .footer textarea {
    width: 100%;
    padding: 0 1rem;
    border: 0;
    background: transparent;
    color: var(--bs-body-color)
}

.btn-primary-light {
    background-color: #3c9aff !important;
}

.voice-btn {
    height: 4rem;
    width: 4rem;
}

.f12 {
    font-size: 0.8rem;
}
.f13 {
    font-size: 0.866rem;
}
.f14 {
    font-size: 0.933rem;
}
.f15 {
    font-size: 1rem;
}
.f16 {
    font-size: 1.066rem;
}
.f17 {
    font-size: 1.133rem;
}
.f18 {
    font-size: 1.2rem;
}
.f19 {
    font-size: 1.266rem;
}
.f20 {
    font-size: 1.333rem;
}
.f21 {
    font-size: 1.4rem;
}
.f22 {
    font-size: 1.466rem;
}
.f23 {
    font-size: 1.533rem;
}
.f24 {
    font-size: 1.58rem;
}
.f25 {
    font-size: 1.666rem;
}
.f26 {
    font-size: 1.733rem;
}
.f27 {
    font-size: 1.8rem;
}
.f28 {
    font-size: 1.866rem;
}


.cursor-pointer {
    cursor: pointer;
}

.cursor-default {
    cursor: default;
}

.avatar {
    width: 2.8rem;
    height: 2.8rem;
    border-radius: 3rem;
    background-color: #ddd;
}

/* 拷贝 */
.block-copy, .block-copy:before, .block-copy:hover, .block-copy:active:before {
    content: '\e711';
    cursor: pointer;
    border-radius: var(--bs-border-radius);
    padding: .25rem .15rem;
}
.block-copy:active:before {
    content: '\e742';
}


.message img {
    max-width: 512px;
    height: auto;
    border-radius: var(--bs-border-radius);
}

.markdown-body {
    overflow-wrap: anywhere !important;
    font-size: 15px !important;
    color: var(--bs-body-color);
}

.markdown-body table {
    width: auto !important;
}

.markdown-body>p>img {
    margin-top: .25rem;
    margin-bottom: var(--bs-border-radius);
    border-radius: var(--bs-border-radius);
    max-width: 512px;
    max-height: 512px;
}

.markdown-body pre>code {
    white-space: pre-wrap !important;
    overflow-wrap: anywhere !important;
}

/* 消息 */
.message-list .alert {
    padding: .5rem .75rem;
    min-height: 2.5rem;
}

.message-list .alert-secondary {
    background-color: #ededed;
}

/* 闪烁的光标 */
.animate-blink {
    animation: blink 1.2s infinite steps(1,start);
}
@keyframes blink {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

/* loading 动画 */
.loading{
    display: flex;
    justify-content: center;
    align-items: center;
}
.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ddd;
    margin: 0 5px;
    opacity: 0.3;
    animation: pulse 0.5s ease-in-out infinite alternate;
}
.dot1 {
    animation-delay: 0s;
}
.dot2 {
    animation-delay: 0.2s;
}
.dot3 {
    animation-delay: 0.4s;
}
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.5);
        opacity: 1;
    }
}


/* 图片预览浮层 */
.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.overlay>img {
    max-width: 95%;
    max-height: 95%;
}
.overlay .close {
    position: absolute;
    top: 0.67rem;
    right: 0.67rem;
    color: #fff;
    cursor: pointer;
}
/* end */

/* markdown 数学公式 */
.katex { font-size: 1em !important; } /* align KaTeX font-size to surrounding text */

eq { display: inline-block; }
eqn { display: block}
section.eqno {
    display: flex;
    flex-direction: row;
    align-content: space-between;
    align-items: center;
}
section.eqno > eqn {
    width: 100%;
    margin-left: 3em;
}
section.eqno > span {
    width:3em;
    text-align:right;
}

/*上传图片列表*/
.images-box{
    bottom: .5rem;
    display: flex;
    position: absolute;
    left: .5rem;
    background: var(--ai-dark-bg);
    height: 100px;
    align-items: center;
    padding-left: 1rem;
    opacity: 0.8;
}
.images-box .images-item{
    display: flex;
}
.images-box .images-item .del{
    height: 1.2rem;
    width: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bs-body-bg);
    font-size: .7rem;
    border-radius: 1rem;
    right: .2rem;
    top: .2rem;
    opacity: 0.8;
    color: var(--bs-body-color);
    text-align: center;
    line-height: 1.2rem;
    cursor: pointer;
    position: absolute;
}
.images-box .images-item .del:hover{
    opacity: 1;
}
.images-box .images-item{
    margin-right: 1rem;
    position: relative;
}
.images-box .images-item img{
    max-width: 80px;
    height: 80px;
    min-width: 20px;
    filter: brightness(0.95);
}
.images-box .bi::before {
    font-weight: bold !important;
}
.tooltip-inner{
    max-width:100%;
    background-color: var(--ai-dark-bg);
}
/* 手机适配 */
@media (max-width: 768px) {
    .left-bar {
        flex-direction: row;
        width: 100%;
        justify-content: space-around;
        padding-top: 0;
        height: calc(3.2rem + env(safe-area-inset-bottom));
        flex-shrink: 0;
        border-top: 1px solid var(--bs-border-color);
        z-index:1001;
        padding-bottom: env(safe-area-inset-bottom);
        border-right: none;
    }
    .search-box {
        height: 60px;
    }
    .chat-bar {
        width: 100%;
        height: calc(100% - 3.2rem - env(safe-area-inset-bottom));
        border-right:none;
    }
    .avatar {
        width: 3rem;
        height: 3rem;
        border-radius: 3rem;
        background-color: #ddd;
    }
    .chat-list .desc {
        max-width: 70vw;
    }
    .message-list {
        padding: 0 .5rem;
    }
    .image-tools .btn {
        min-width: 50px;
    }
    .center-box, .params-box {
        width: 96% !important;
        margin:0;
        left: initial !important;
        top: initial !important;
    }
    .center-box {
        position: relative;
    }
    .params-box {
        bottom: 120px;
    }
    .chat-box .header {
        height: 50px;
    }
    .chat-box .footer {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
    }
    .more-box {
        width: 100%;
    }
    .chat-box .avatar {
        width: 2.5rem;
        height: 2.5rem;
    }
    .chat-list {
        height: calc(100% - 60px);
    }
    .chat-list-footer {
        display: none;
    }
    .chat-list .text-truncate {
        max-width: 65vw;
    }
    .markdown-body {
        font-size: 1rem !important;
        color: var(--bs-body-color);
    }
    .message img {
        max-width: 70vw !important;
    }
    .voice-btn {
        height: 2rem;
        width: 2rem;
        font-size: 1rem !important;
    }
    html, body {
        height: 100%;
        font-size: 16px;
    }
    body {
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
        padding-top: env(safe-area-inset-top);
    }
}