<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\controller;

use plugin\ai\app\event\data\EventData;
use plugin\ai\app\service\Audio;
use plugin\ai\app\service\Category;
use plugin\ai\app\service\Common;
use plugin\ai\app\service\Model;
use plugin\ai\app\service\Plan;
use plugin\ai\app\service\Setting;
use support\Request;
use support\Response;
use Webman\Event\Event;

class SettingController extends Base
{
    /**
     * 不需要登录的方法
     *
     * @var string[]
     */
    protected $noNeedLogin = ['index', 'models', 'categories'];

    /**
     * 获取配置
     *
     * @return Response
     */
    public function index(): Response
    {
        $models = Model::getSetting();
        $enabledModelTypes = Common::getModelTypes();
        $availableModels = Common::getModels();
        foreach ($models as $model => $name) {
            if (!in_array($model, $availableModels)) {
                unset($models[$model]);
            }
        }
        $setting = Setting::getSetting();
        $audioSetting = Audio::getSetting();
        $menus = [
            'painting' => [
                'enabled' => $enabledModelTypes['midjourney'] ?? false, // 是否开启
                'title' => '绘画',
                'icon' => [ // 图标
                    'light' => '<i class="icon-painting"></i><span class="centered-span">MJ绘画</span>',
                    'dark' => '<i class="icon-painting"></i><span class="centered-span">MJ绘画</span>',
                    'active' => '<i class="icon-painting-fill"></i><span class="centered-span">MJ绘画</span>',
                ],
                'url' => '/app/ai/painting', // iframe url
                'mobile' => true, // 移动端显示入口
                'keep' => true, // 切换标签后iframe保留，避免重复加载
            ],
            'market' => [
                'enabled' => true,
                'title' => '应用市场',
                'icon' => [
                    'light' => '<i class="icon-market"></i><span class="centered-span">应用市场</span>',
                    'dark' => '<i class="icon-market"></i><span class="centered-span">应用市场</span>',
                    'active' => '<i class="icon-market-fill"></i><span class="centered-span">应用市场</span>'
                ],
                'url' => '/app/ai/market',
                'mobile' => true,
                'keep' => true
            ],
//            'market_new' => [
//                'enabled' => false,
//                'title' => '新测试应用市场',
//                'icon' => [
//                    'light' => '<i class="icon-market"></i>',
//                    'dark' => '<i class="icon-market"></i>',
//                    'active' => '<i class="icon-market-fill"></i>'
//                ],
//                'url' => '/app/ai/market_new',
//                'mobile' => true,
//                'keep' => true
//            ],
//            'test_video' => [
//                'enabled' => $enabledModelTypes['midjourney'] ?? false,
//                'title' => '视频测试',
//                'icon' => [
//                    // 'light' => '<i class="icon-gallery"></i>',
//                    // 'dark' => '<i class="icon-gallery"></i>',
//                    // 'active' => '<i class="icon-gallery-fill"></i>'
//                    'light' => '<i><svg t="1721115582725" class="icon" viewBox="0 0 1256 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5235" width="23" height="23"><path d="M201.681455 74.472727a111.709091 111.709091 0 0 0-111.709091 111.709091v651.636364a111.709091 111.709091 0 0 0 111.709091 111.709091h837.818181a111.709091 111.709091 0 0 0 111.709091-111.709091V186.181818a111.709091 111.709091 0 0 0-111.709091-111.709091h-837.818181z m0-74.472727h837.818181a186.181818 186.181818 0 0 1 186.181819 186.181818v651.636364a186.181818 186.181818 0 0 1-186.181819 186.181818h-837.818181a186.181818 186.181818 0 0 1-186.181819-186.181818V186.181818a186.181818 186.181818 0 0 1 186.181819-186.181818zM798.72 533.085091a9.309091 9.309091 0 0 0-1.861818-13.032727l-248.226909-186.181819a9.309091 9.309091 0 0 0-14.894546 7.447273v372.363637a9.309091 9.309091 0 0 0 14.894546 7.447272l248.226909-186.181818a9.309091 9.309091 0 0 0 1.861818-1.861818z m-205.405091 247.621818a83.781818 83.781818 0 0 1-134.050909-67.025454v-372.363637a83.781818 83.781818 0 0 1 134.050909-67.025454l248.226909 186.181818a83.781818 83.781818 0 0 1 0 134.050909l-248.226909 186.181818z" fill="#2c2c2c" p-id="5236"></path></svg></i><span class="centered-span">视频课程</span>',
//                    'dark' => '<i><svg t="1721115582725" class="icon" viewBox="0 0 1256 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5235" width="23" height="23"><path d="M201.681455 74.472727a111.709091 111.709091 0 0 0-111.709091 111.709091v651.636364a111.709091 111.709091 0 0 0 111.709091 111.709091h837.818181a111.709091 111.709091 0 0 0 111.709091-111.709091V186.181818a111.709091 111.709091 0 0 0-111.709091-111.709091h-837.818181z m0-74.472727h837.818181a186.181818 186.181818 0 0 1 186.181819 186.181818v651.636364a186.181818 186.181818 0 0 1-186.181819 186.181818h-837.818181a186.181818 186.181818 0 0 1-186.181819-186.181818V186.181818a186.181818 186.181818 0 0 1 186.181819-186.181818zM798.72 533.085091a9.309091 9.309091 0 0 0-1.861818-13.032727l-248.226909-186.181819a9.309091 9.309091 0 0 0-14.894546 7.447273v372.363637a9.309091 9.309091 0 0 0 14.894546 7.447272l248.226909-186.181818a9.309091 9.309091 0 0 0 1.861818-1.861818z m-205.405091 247.621818a83.781818 83.781818 0 0 1-134.050909-67.025454v-372.363637a83.781818 83.781818 0 0 1 134.050909-67.025454l248.226909 186.181818a83.781818 83.781818 0 0 1 0 134.050909l-248.226909 186.181818z" fill="#8A9198" p-id="5236"></path></svg></i><span class="centered-span">视频课程</span>',
//                    'active' => '<i><svg t="1721115760880" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14243" width="24" height="24"><path d="M688.347429 950.857143H122.916571C55.076571 950.784 0.073143 895.067429 0 826.331429V197.668571C0.073143 128.950857 55.058286 73.252571 122.916571 73.142857h565.430858c66.523429-0.365714 121.051429 53.394286 122.496 120.758857v1.609143c0.036571 0.731429 0.036571 1.499429 0 2.249143v146.962286l130.432-68.461715a54.290286 54.290286 0 0 1 55.259428-0.219428A55.771429 55.771429 0 0 1 1024 324.608v377.6c-0.054857 30.866286-24.740571 55.881143-55.204571 55.917714a53.632 53.632 0 0 1-27.428572-7.259428l-130.450286-68.461715v143.908572a27.428571 27.428571 0 0 1 0 3.712v2.834286c-3.254857 66.157714-57.179429 118.070857-122.569142 117.997714zM219.337143 219.428571a109.714286 109.714286 0 1 0 0.182857 219.428572 109.714286 109.714286 0 0 0-0.182857-219.428572z" p-id="14244"></path></svg></i><span class="centered-span">视频课程</span>'
//                ],
//                'url' => '/app/ai/test_video',
//                'mobile' => true,
//                'keep' => true
//            ],
//            'detail' => [
//                'enabled' => $enabledModelTypes['midjourney'] ?? false,
//                'title' => '详情页',
//                'icon' => [
//                    'light' => '<i class="icon-detail"></i>',
//                    'dark' => '<i class="icon-detail"></i>',
//                    'active' => '<i class="icon-detail-fill"></i>'
//                ],
//                'url' => '/app/ai/detail',
//                'mobile' => true,
//                'keep' => true
//            ],
            // 'gallery' => [
            //     'enabled' => $enabledModelTypes['midjourney'] ?? false,
            //     'title' => '图片广场',
            //     'icon' => [
            //         'light' => '<i class="icon-gallery"></i><span class="centered-span">图片广场</span>',
            //         'dark' => '<i class="icon-gallery"></i><span class="centered-span">图片广场</span>',
            //         'active' => '<i class="icon-gallery-fill"></i><span class="centered-span">图片广场</span>',
            //     ],
            //     'url' => '/app/ai/gallery',
            //     'mobile' => true,
            //     'keep' => true
            // ],
            'vip' => [
                'enabled' => $setting['enable_payment']??false,
                'title' => '会员充值',
                'icon' => [
                    'light' => '<i class="icon-vip"></i><span class="centered-span">会员充值</span>',
                    'dark' => '<i class="icon-vip"></i><span class="centered-span">会员充值</span>',
                    'active' => '<i class="icon-vip-fill"></i><span class="centered-span">会员充值</span>'
                ],
                'url' => '/app/ai/user/vip',
                'mobile' => !($enabledModelTypes['midjourney'] ?? false),
            ],
            'vips' => [
                'enabled' => false,
                'title' => '会员充值',
                'icon' => [
                    'light' => '<i class="icon-vip"></i>',
                    'dark' => '<i class="icon-vip"></i>',
                    'active' => '<i class="icon-vip-fill"></i>'
                ],
                'url' => '/app/ai/user/vip',
                'mobile' => true,
                'keep' => true
            ],
            'uservip' => [
                'enabled' => true,
                'title' => '开通会员',
                'icon' => [
                    'light' => '<i class="icon-vip"></i><span class="centered-span">开通会员</span>',
                    'dark' => '<i class="icon-vip"></i><span class="centered-span">开通会员</span>',
                    'active' => '<i class="icon-vip-fill"></i><span class="centered-span">开通会员</span>'
                ],
                'url' => '/app/ai/user/recharge',
                'mobile' => true,
                'keep' => true
            ],
            'me' => [
                'enabled' => true,
                'title' => '我的信息',
                'icon' => [
                    'light' => '<i class="icon-me"></i><span class="centered-span">我的信息</span>',
                    'dark' => '<i class="icon-me"></i><span class="centered-span">我的信息</span>',
                    'active' => '<i class="icon-me-fill"></i><span class="centered-span">我的信息</span>'
                ],
                'url' => '/app/ai/user',
                'mobile' => true,
            ],
            'classzd' => [
                'enabled' => true,
                'title' => '教程指导',
                'icon' => [
                    'light' => '<i class="icon-market"></i><span class="centered-span">教程指导</span>',
                    'dark' => '<i class="icon-market"></i><span class="centered-span">教程指导</span>',
                    'active' => '<i class="icon-market-fill"></i><span class="centered-span">教程指导</span>'
                ],
                'url' => '/app/ai/user/classs',
                'mobile' => true,
            ],
        ];
        $eventData = new EventData($menus);
        Event::dispatch('ai.menu.list', $eventData);
        return $this->json(0, 'ok', [
            'version' => config('plugin.ai.app.version'),
            'defaultModels' => $models,
            'enabledAlipay' => static::alipayEnabled(),
            'enabledWechat' => static::wechatEnabled(),
            'enablePayment' => $setting['enable_payment']??false,
            'icp' => $setting['icp']??'',
            'beian' => $setting['beian']??'',
            'footer_txt' => $setting['footer_txt']??'',
            'enabledModelTypes' => $enabledModelTypes,
            'plans' => Plan::getSetting(),
            'audio' => [
                'enable_xunfei_iat' => (bool)($audioSetting['enable_xunfei_iat'] ?? false),
                'enable_yingying_tts' => (Common::getModelInfoByType('ying')->status ?? 1) === 0,
                'enable_gpt_tts' => (Common::getModelInfoByType('tts')->status ?? 1) === 0
            ],
            'menus' => $eventData->data,
        ]);
    }

    /**
     * 可用模型
     *
     * @param Request $request
     * @return Response
     */
    public function models(Request $request): Response
    {
        $models = [];
        foreach (Model::getSetting() as $model => $name) {
            $models[] = [
                'name' => $name,
                'value' => $model
            ];
        }
        return $this->json(0, 'ok', $models);
    }


    /**
     * 获取所有分类
     *
     * @param Request $request
     * @return Response
     */
    public function categories(Request $request): Response
    {
        $categories = explode("\n", Category::getSetting());
        $categories = array_filter($categories, 'strlen');
        $data = [];
        foreach ($categories as $category) {
            $category = trim($category);
            if ($category === '') continue;
            $data[] = [
                'name' => $category,
                'value' => $category
            ];
        }
        return json(['code' => 0, 'msg' => 'ok', 'data' => $data]);
    }

}
