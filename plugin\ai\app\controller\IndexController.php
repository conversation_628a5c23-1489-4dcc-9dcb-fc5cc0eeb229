<?php

/**
 * This file is part of Webman AI.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 */

namespace plugin\ai\app\controller;

use plugin\ai\app\service\Setting;
use support\Request;
use support\Response;

class IndexController extends Base
{
    /**
     * 不需要登录的方法
     *
     * @var string[]
     */
    protected $noNeedLogin = ['index', 'info'];

    /**
     * 首页
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        if ($request->host() === 'www.workerman.net' && !session('user')) {
            return redirect('/user/login?redirect=/ai');
        }
        $setting = Setting::getSetting();
        return view('index/index', [
            'site_title' => empty($setting['site_title']) ? 'Webman AI助手' : $setting['site_title'],
            'site_desc' => empty($setting['site_desc']) ?
                'Webman AI是一套多角色可训练的AI程序，支持GPT Midjourney Gemini 文心一言 通义千问 讯飞星火 清华智普等众多模型' : $setting['site_desc'],
            'theme' => $setting['theme'] ?? 'light',
        ]);
    }

    /**
     * 信息页
     *
     * @return Response
     */
    public function info(): Response
    {
        return view('index/info');
    }

}
