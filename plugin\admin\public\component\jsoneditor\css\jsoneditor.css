.jsoneditor-field, .jsoneditor-value, .jsoneditor-field-readonly, .jsoneditor-readonly {
    border: 1px solid transparent;
    min-height: 16px;
    min-width: 24px;
    padding: 2px;
    margin: 1px;
    outline: none;
    word-wrap: break-word;
    float: left;
}

/* adjust margin of p elements inside editable divs, needed for Opera, IE */
.jsoneditor-field p, .jsoneditor-value p {
    margin: 0;
}

.jsoneditor-value {
    word-break: break-word;
}

.jsoneditor-empty {
    background-color: #E5E5E5;
    border-radius: 2px;
}

.jsoneditor-separator {
    padding: 3px 0;
    vertical-align: top;
}

.jsoneditor-value:focus, .jsoneditor-field:focus,
.jsoneditor-value:hover, .jsoneditor-field:hover,
.jsoneditor-search-highlight {
    background-color: #FFFFAB;
    border: 1px solid yellow;
    border-radius: 2px;
}

.jsoneditor-search-highlight-active,
.jsoneditor-search-highlight-active:focus,
.jsoneditor-search-highlight-active:hover {
    background-color: #ffee00;
    border: 1px solid #ffc700;
    border-radius: 2px;
}

.jsoneditor-field-readonly:hover {
    border: 1px solid white;
}

.jsoneditor-readonly {
    color: gray;
}

button.jsoneditor-remove, button.jsoneditor-append, button.jsoneditor-duplicate,
button.jsoneditor-collapsed, button.jsoneditor-expanded,
button.jsoneditor-invisible, button.jsoneditor-dragarea,
button.jsoneditor-type-auto, button.jsoneditor-type-string,
button.jsoneditor-type-array, button.jsoneditor-type-object {
    width: 24px;
    height: 24px;
    padding: 0;
    margin: 0;
    border: none;
    cursor: pointer;
    background: url('jsoneditor-icons.png');
}

button:disabled {
    color: #808080;
}

button.jsoneditor-collapsed {
    background-position: -168px 0;
}

button.jsoneditor-expanded {
    background-position: -168px -24px;
}

button.jsoneditor-invisible {
    visibility: hidden;
    background: none;
}

button.jsoneditor-collapsed, button.jsoneditor-expanded,
button.jsoneditor-invisible {
    float: left;
}

button.jsoneditor-remove {
    background-position: -24px -24px;
}
button.jsoneditor-remove:hover {
    background-position: -24px 0;
}

button.jsoneditor-append {
    background-position: 0 -24px;
}
button.jsoneditor-append:hover {
    background-position: 0 0;
}

button.jsoneditor-duplicate {
    background-position: -48px -24px;
}
button.jsoneditor-duplicate:hover {
    background-position: -48px 0;
}

button.jsoneditor-type-string {
    background-position: -144px -24px;
}
button.jsoneditor-type-string:hover {
    background-position: -144px 0;
}

button.jsoneditor-type-auto {
    background-position: -120px -24px;
}
button.jsoneditor-type-auto:hover {
    background-position: -120px 0;
}

button.jsoneditor-type-object {
    background-position: -72px -24px;
}
button.jsoneditor-type-object:hover {
    background-position: -72px 0;
}

button.jsoneditor-type-array {
    background-position: -96px -24px;
}
button.jsoneditor-type-array:hover {
    background-position: -96px 0;
}

div.jsoneditor-select {
    border: 1px solid gray;
    background-color: white;
    box-shadow: 4px 4px 10px rgba(128, 128, 128, 0.5);
}

div.jsoneditor-option {
    color: #4D4D4D;
    background-color: white;

    border: none;
    margin: 0;
    display: block;
    text-align: left;
    cursor: pointer;
}
div.jsoneditor-option:hover {
    background-color: #FFFFAB;
    color: #1A1A1A;
}
div.jsoneditor-option-selected {
    background-color: #C1C1C1;
}
div.jsoneditor-option-text {
    height: 24px;
    line-height: 24px;
    padding: 0 12px 0 0;
    display: inline-block;
}

div.jsoneditor-option-string, div.jsoneditor-option-auto,
div.jsoneditor-option-object, div.jsoneditor-option-array {
    float: left;
    width: 24px;
    height: 24px;
    display: inline-block;
    background: url('jsoneditor-icons.png');
}
div.jsoneditor-option-string {
    background-position: -144px 0;
}
div.jsoneditor-option-auto {
    background-position: -120px 0;
}
div.jsoneditor-option-object {
    background-position: -72px 0;
}
div.jsoneditor-option-array {
    background-position: -96px 0;
}


div.jsoneditor-frame {
    color: #1A1A1A;
    border: 1px solid #C1C1C1;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    overflow: auto;
    position: relative;
    padding: 0;
}

table.jsoneditor-table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    margin: 0;
}

div.jsoneditor-content-outer, div.jsonformatter-content {
    width: 100%;
    height: 100%;
    margin: -35px 0 0 0;
    padding: 35px 0 0 0;

    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;

    overflow: hidden;
}

div.jsoneditor-content {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: auto;
}

textarea.jsonformatter-textarea {
    width: 100%;
    height: 100%;
    margin: 0;

    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;

    border: none;
    background-color: white;
    resize: none;
}

tr.jsoneditor-tr-highlight {
    background-color: #FFFFAB;
}

button.jsoneditor-dragarea {
    width: 16px;
    height: 24px;
    /*
    margin: 3px 0;
    background: url('img/dots_gray.gif') top center;
    background-repeat: repeat-y;
    */
    background: url('jsoneditor-icons.png') -220px 0;

    display: block;
    cursor: move;
}

div.jsoneditor-menu {
    width: 100%;
    height: 35px;
    padding: 2px;
    margin: 0;
    overflow: hidden;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;

    color: #1A1A1A;
    border-bottom: 1px solid #C1C1C1;
}

table.jsoneditor-search {
    position: absolute;
    right: 2px;
    top: 2px;
}

table.jsoneditor-search-input {
    border-collapse: collapse;
}

div.jsoneditor-search {
    border: 1px solid #C1C1C1;
    background-color: white;
    padding: 0 2px;
    margin: 0;
}

input.jsoneditor-search {
    width: 120px;
    border: none;
    outline: none;
    margin: 1px;
}

div.jsoneditor-search-results {
    color: #4d4d4d;
    padding-right: 5px;
}

button.jsoneditor-search-refresh, button.jsoneditor-search-next,
button.jsoneditor-search-previous {
    width: 16px;
    height: 24px;
    padding: 0;
    margin: 0;
    border: none;
    background: url('jsoneditor-icons.png');
    vertical-align: top;
}

button.jsoneditor-search-refresh {
    width: 18px;
    background-position: -243px -25px;
}

button.jsoneditor-search-next {
    cursor: pointer;
    background-position: -268px -25px;
}
button.jsoneditor-search-next:hover {
    background-position: -268px -1px;
}

button.jsoneditor-search-previous {
    cursor: pointer;
    background-position: -292px -25px;
    margin-right: 2px;
}
button.jsoneditor-search-previous:hover {
    background-position: -292px -1px;
}


button.jsoneditor-menu {
    width: 26px;
    height: 26px;
    margin: 2px;
    padding: 2px;
    border-radius: 2px;
    border: 1px solid #aec0f8;
    background: #D0F0EF url('jsoneditor-icons.png');
}

button.jsoneditor-menu:hover {
    background-color: #FFFFFF;
}
button.jsoneditor-menu:active {
    background-color: #ffffff;
}
button.jsoneditor-menu:disabled {
    background-color: #D0F0EF;
}

button.jsoneditor-collapse-all {
    background-position: -312px 0;
}
button.jsoneditor-expand-all {
    background-position: -312px -24px;
}
button.jsoneditor-undo {
    background-position: -336px 0;
}
button.jsoneditor-redo {
    background-position: -360px 0;
}
button.jsoneditor-undo:disabled {
    background-position: -336px -24px;
}
button.jsoneditor-redo:disabled {
    background-position: -360px -24px;
}
/* TODO: css for button:disabled is not supported by IE8 */
button.jsoneditor-compact {
    background-position: -384px 0;
}
button.jsoneditor-format {
    background-position: -384px -24px;
}


tr, th, td {
    padding: 0;
    margin: 0;
}

td.jsoneditor-td {
    vertical-align: top;
}

td.jsoneditor-td {
    padding: 0 3px;
}

td.jsoneditor-td-edit {
    background-color: #F5F5F5;
    padding: 0;
}

td.jsoneditor-td-tree {
    vertical-align: top;
}

td.jsoneditor-droparea {
    height: 24px;

    border-top: 1px dashed gray;
    border-bottom: 1px dashed gray;
    background-color: #FFFF80;
}

.jsoneditor-field, .jsoneditor-value, .jsoneditor-td, .jsoneditor-th,
.jsoneditor-type,
.jsonformatter-textarea {
    font-family: droid sans mono, monospace, courier new, courier, sans-serif;
    font-size: 10pt;
    color: #1A1A1A;
}

.jsoneditor-hidden-focus {
    position: absolute;
    left: -1000px;
    top: -1000px;
    border: none;
    outline: none;
}

#by-jsonlint{
    text-align: right;
}