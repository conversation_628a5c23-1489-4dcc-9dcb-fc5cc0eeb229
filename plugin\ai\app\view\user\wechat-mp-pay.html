<?=plugin\user\api\Template::header('购买AI会员')?>
<link rel="stylesheet" href="/app/ai/css/app.css?v=<?=ai_css_version()?>">
<style>
    .background {
        height: 168px;
        width: 100%;
        background: linear-gradient(to right, rgb(59 217 189) 0%, rgb(151 193 254) 100%);
    }

    .header {
        padding-top: .75rem;
        padding-bottom: .75rem;
    }

    .box {
        margin: -45px 16px 0;
        padding: 8px 24px 16px;
        background: #fff;
        position: relative;
    }

    .item {
        display: flex;
        font-size: 16px;
        line-height: 20px;
        color: #444950;
        padding: 16px 0;
        border-top: 1px dotted rgba(0, 0, 0, 0.08);
    }

    .label {
        display: inline-block;
        color: #8d949e;
        width: 100px;
    }

    .value {
        max-width: calc(100% - 100px);
        display: inline-block;
        white-space: normal;
        word-wrap: break-word;
    }
</style>
<div id="app">
    <div class="background">
        <div class="bg-white d-flex justify-content-between header"><b class="iconfont f22 px-3" @click="back()">&#xe9ef;</b><span>正在支付</span><span
                class=" px-3"></span></div>
    </div>
    <div class="bg-white rounded shadow-sm box">
        <div class="item">
            <div class="label">购买版本：</div>
            <div class="value"><?=$planName?></div>
        </div>
        <div class="item">
            <div class="label">订单编号：</div>
            <div class="value"><?=$order->order_id?></div>
        </div>
        <div class="item">
            <div class="label">下单时间：</div>
            <div class="value"><?=$order->created_at?></div>
        </div>
        <div class="item">
            <div class="label">订单金额：</div>
            <div class="value">
                <div class="price">¥<span><?=$order->total_amount?></span></div>
            </div>
        </div>
    </div>
    <div class="alert alert-success mt-3 mx-3" v-if="success">
        <span class="iconfont">&#xe9da;</span> 支付成功
    </div>

    <div class="justify-content-center position-absolute w-100" style="bottom:80px;">
        <a v-if="!success" class="btn btn-primary d-block mx-3" target="_this" @click="payment()">立即支付</a>
        <a v-else class="btn btn-success d-block mx-3" target="_this" @click="back()">返回AI</a>
    </div>
</div>

<!-- vue -->
<script type="text/javascript" src="/app/ai/js/vue.global.js"></script>

<script>
    const App = {
        data() {
            return {
                plan: 1,
                orderId: '',
                paymentMethod: "alipay",
                waitTimeout: false,
                statusCount: 0,
                success: <?=$order->state === 'paid' ? 'true' : 'false'?>,
                orderCreateTime: 0
            }
        },
        mounted() {
            this.orderCreateTime = new Date().getTime();
            const urlParams = new URLSearchParams(window.location.search);
            this.orderId = urlParams.get('orderId');
            this.checkOrderStatus();
            this.getOrderStatus();
        },
        methods: {
            payment() {
                if (typeof WeixinJSBridge == "undefined") {
                    if (document.addEventListener) {
                        document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady, false);
                    } else if (document.attachEvent) {
                        document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady);
                        document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady);
                    }
                } else {
                    this.onBridgeReady();
                }
            },
            onBridgeReady() {
                WeixinJSBridge.invoke('getBrandWCPayRequest', {
                        "appId": "<?=$payData->appId?>",     //公众号ID，由商户传入
                        "timeStamp": "<?=$payData->timeStamp?>",     //时间戳，自1970年以来的秒数
                        "nonceStr": "<?=$payData->nonceStr?>",      //随机串
                        "package": "<?=$payData->package?>",
                        "signType": "<?=$payData->signType?>",     //微信签名方式：
                        "paySign": "<?=$payData->paySign?>" //微信签名
                    },
                    function(res){
                        if (res.err_msg === "get_brand_wcpay_request:ok") {
                            this.success = true;
                        } else if (res.err_msg === "get_brand_wcpay_request:fail") {
                            return alert('支付失败');
                        }
                    });
            },
            back() {
                window.location.href = "/app/ai";
            },
            checkOrderStatus(once) {
                $.ajax({
                    url: "/app/ai/order/status",
                    data: {orderId: this.orderId},
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        if (res.data.status === 'paid') {
                            this.success = true;
                            return;
                        }
                        if (!once) {
                            this.timer = setTimeout(() => {
                                this.checkOrderStatus();
                            }, 3000);
                        }
                    },
                    complete: () => {
                        // 8分钟后过期
                        if (this.orderCreateTime && new Date().getTime() - this.orderCreateTime > 8 * 60 * 1000) {
                            this.clearTimer();
                            this.waitTimeout = true;
                        }
                    }
                });
            },
            getOrderStatus() {
                $.ajax({
                    url: "/app/ai/order/get-status",
                    data: {orderId: this.orderId},
                    success: (res) => {
                        if (res.code) {
                            return alert(res.msg);
                        }
                        if (res.data.status === 'paid') {
                            this.success = true;
                        }
                    }
                });
            },
            clearTimer() {
                if (this.timer) {
                    clearTimeout(this.timer);
                    this.timer = 0;
                }
                this.statusCount = 0;
                this.orderCreateTime = 0;
            },
            done() {
                this.clearTimer();
                if (!this.orderId) {
                    return;
                }
                this.getOrderStatus();
            }
        }
    }
    Vue.createApp(App).mount('#app');
</script>

<?=plugin\user\api\Template::footer()?>
