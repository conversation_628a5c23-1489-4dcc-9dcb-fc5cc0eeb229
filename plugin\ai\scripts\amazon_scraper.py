#!/usr/bin/env python3
"""
亚马逊商品信息爬虫
用法: python amazon_scraper.py [url1] [url2] ...
输出: JSON格式的商品信息
"""

import sys
import json
import re
import time
import random
from typing import List, Dict, Any, Optional
import requests
from bs4 import BeautifulSoup

# 默认请求头
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Cache-Control': 'max-age=0',
}

def normalize_amazon_url(url: str) -> str:
    """规范化亚马逊URL，提取ASIN"""
    asin_match = re.search(r'amazon\.[^/]+/(?:.+/)?(?:dp|gp/product)/([A-Z0-9]{10})', url)
    if asin_match:
        asin = asin_match.group(1)
        return f"https://www.amazon.com/dp/{asin}"
    raise ValueError(f"无法从URL中提取ASIN: {url}")

def detect_language(text: str) -> str:
    """检测文本语言"""
    # 检测中文
    if re.search(r'[\u4e00-\u9fff]', text):
        return 'zh'
    
    # 检测日文
    if re.search(r'[\u3040-\u309F\u30A0-\u30FF]', text):
        return 'ja'
    
    # 检测其他非英文字符
    if re.search(r'[àáâäãåèéêëìíîïòóôöõùúûüÿ]', text, re.IGNORECASE):
        return 'other'
    
    return 'en'

def scrape_amazon_product(url: str, max_retries: int = 3) -> Dict[str, Any]:
    """爬取亚马逊商品信息"""
    result = {
        'url': url,
        'success': False,
        'error': None,
        'title': None,
        'features': [],
        'language': 'en'
    }
    
    try:
        # 规范化URL
        url = normalize_amazon_url(url)
        result['url'] = url
        
        # 重试机制
        for attempt in range(max_retries):
            try:
                # 添加随机延迟
                if attempt > 0:
                    time.sleep(1 + random.random() * 2)
                
                # 发送请求
                headers = DEFAULT_HEADERS.copy()
                # 添加随机性到User-Agent以降低被识别风险
                chrome_version = random.randint(110, 120)
                headers['User-Agent'] = f'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/537.36'
                
                response = requests.get(url, headers=headers, timeout=15)
                
                # 检查响应状态
                if response.status_code != 200:
                    raise Exception(f"HTTP错误: {response.status_code}")
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取标题
                title_elem = soup.select_one('#productTitle')
                if not title_elem:
                    title_elem = soup.select_one('#title')
                
                if title_elem:
                    result['title'] = title_elem.get_text().strip()
                else:
                    raise Exception("找不到商品标题")
                
                # 提取特点列表
                features_found = False
                
                # 尝试几种可能的选择器
                feature_selectors = [
                    '#feature-bullets ul li',
                    '#featurebullets_feature_div ul li',
                    'div[id*="feature-bullets"] ul li'
                ]
                
                for selector in feature_selectors:
                    feature_elements = soup.select(selector)
                    if feature_elements:
                        for elem in feature_elements:
                            text = elem.get_text().strip()
                            if text and 'see more' not in text.lower():  # 过滤掉"查看更多"链接
                                result['features'].append(text)
                        features_found = True
                        break
                
                if not features_found:
                    # 如果没有找到特点，尝试查找产品描述
                    desc_elem = soup.select_one('#productDescription')
                    if desc_elem:
                        result['features'].append(desc_elem.get_text().strip())
                        features_found = True
                
                # 如果还是没有找到，记录错误但继续
                if not features_found:
                    result['features'] = ["No product features found"]
                
                # 检测语言
                if result['title']:
                    result['language'] = detect_language(result['title'])
                
                # 标记成功
                result['success'] = True
                break  # 成功后跳出重试循环
                
            except Exception as e:
                if attempt == max_retries - 1:  # 是最后一次尝试
                    raise
                # 否则继续重试
        
    except Exception as e:
        result['error'] = str(e)
    
    return result

def main():
    """主函数，处理命令行参数并执行爬取"""
    if len(sys.argv) < 2:
        print(json.dumps({
            "success": False,
            "error": "No URLs provided",
            "data": []
        }))
        return
    
    urls = sys.argv[1:]
    results = []
    languages = {'en': 0}
    
    for url in urls:
        result = scrape_amazon_product(url)
        results.append(result)
        
        # 统计语言
        if result['success'] and result['language'] in languages:
            languages[result['language']] += 1
        elif result['success']:
            languages[result['language']] = 1
    
    # 确定主要语言
    main_language = max(languages.items(), key=lambda x: x[1])[0]
    
    # 输出JSON结果
    output = {
        "success": True,
        "data": results,
        "mainLanguage": main_language
    }
    
    print(json.dumps(output, ensure_ascii=False))

if __name__ == "__main__":
    main()